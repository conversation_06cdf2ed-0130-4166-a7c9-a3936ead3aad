{"extends": ["plugin:prettier/recommended", "plugin:@typescript-eslint/recommended"], "ignorePatterns": [".eslintrc"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module", "ecmaVersion": 2023}, "plugins": ["eslint-plugin-import", "eslint-plugin-prefer-arrow", "eslint-plugin-jsdoc", "eslint-plugin-unicorn", "@typescript-eslint/eslint-plugin", "prettier"], "root": true, "rules": {"prettier/prettier": ["error", {"endOfLine": "auto"}], "@typescript-eslint/adjacent-overload-signatures": "error", "@typescript-eslint/array-type": ["error", {"default": "array"}], "@typescript-eslint/consistent-type-assertions": "error", "@typescript-eslint/dot-notation": "error", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-member-accessibility": ["off", {"accessibility": "explicit"}], "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/indent": "off", "@typescript-eslint/member-delimiter-style": ["off", {"multiline": {"delimiter": "none", "requireLast": true}, "singleline": {"delimiter": "semi", "requireLast": false}}], "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-empty-function": "error", "@typescript-eslint/no-empty-interface": "warn", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-new": "error", "@typescript-eslint/no-namespace": "error", "@typescript-eslint/no-parameter-properties": "off", "@typescript-eslint/no-shadow": ["warn", {"hoist": "all"}], "@typescript-eslint/no-this-alias": "error", "@typescript-eslint/no-unnecessary-type-assertion": "error", "@typescript-eslint/no-unused-expressions": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-use-before-define": "off", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/prefer-for-of": "warn", "@typescript-eslint/prefer-function-type": "error", "@typescript-eslint/prefer-namespace-keyword": "error", "@typescript-eslint/quotes": "off", "@typescript-eslint/semi": ["off", null], "@typescript-eslint/triple-slash-reference": ["error", {"path": "always", "types": "prefer-import", "lib": "always"}], "@typescript-eslint/type-annotation-spacing": "off", "@typescript-eslint/typedef": "off", "@typescript-eslint/unified-signatures": "warn", "arrow-parens": ["off", "always"], "brace-style": ["off", "off"], "comma-dangle": "off", "@typescript-eslint/no-inferrable-types": "off", "no-useless-catch": "off", "complexity": "off", "constructor-super": "error", "curly": "off", "dot-notation": "off", "eol-last": "off", "eqeqeq": ["warn", "always"], "guard-for-in": "error", "id-denylist": ["error", "any", "Number", "number", "String", "string", "Boolean", "Undefined", "undefined"], "id-match": "error", "import/no-deprecated": "error", "import/no-extraneous-dependencies": "off", "import/no-internal-modules": "off", "import/no-unassigned-import": "warn", "import/order": "off", "indent": "off", "jsdoc/check-alignment": "error", "jsdoc/check-indentation": "error", "linebreak-style": "off", "max-classes-per-file": "off", "max-len": "off", "new-parens": "off", "newline-per-chained-call": "off", "no-bitwise": "error", "no-caller": "error", "no-cond-assign": "error", "no-console": ["warn", {"allow": ["warn", "dir", "timeLog", "assert", "clear", "count", "<PERSON><PERSON><PERSON><PERSON>", "group", "groupEnd", "table", "dirxml", "error", "groupCollapsed", "<PERSON><PERSON><PERSON>", "profile", "profileEnd", "timeStamp", "context"]}], "no-debugger": "error", "no-duplicate-case": "error", "no-duplicate-imports": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-empty-function": "off", "no-eval": "error", "no-extra-bind": "error", "no-extra-semi": "off", "no-fallthrough": "off", "no-invalid-this": "off", "no-irregular-whitespace": "off", "no-multiple-empty-lines": "off", "no-new-func": "error", "no-new-wrappers": "error", "no-param-reassign": "error", "no-redeclare": "error", "no-return-await": "error", "no-sequences": "error", "no-shadow": "off", "no-sparse-arrays": "error", "no-template-curly-in-string": "error", "no-throw-literal": "error", "no-trailing-spaces": "off", "no-undef-init": "error", "no-underscore-dangle": "off", "no-unsafe-finally": "error", "no-unused-expressions": "off", "no-unused-labels": "error", "no-unused-vars": "off", "no-use-before-define": "off", "no-var": "error", "no-void": "error", "object-shorthand": "error", "one-var": ["error", "never"], "padded-blocks": ["off", {"blocks": "never"}, {"allowSingleLineBlocks": true}], "prefer-arrow/prefer-arrow-functions": "off", "prefer-const": "error", "prefer-object-spread": "error", "quote-props": "off", "quotes": "off", "radix": "error", "semi": "off", "space-before-function-paren": "off", "space-in-parens": ["off", "never"], "unicorn/prefer-ternary": "error", "use-isnan": "error", "valid-typeof": "off", "no-case-declarations": "off"}}