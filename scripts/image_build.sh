#!/bin/sh

apt-get update && apt-get install -y gettext
APPVERSION=${SEM_VERSION}-$(date +"%Y-%m-%dT%H:%M:%S")-${ENVIRONMENT}
echo $APPVERSION > VERSION.txt
docker build -t minicardiac-$ENVIRONMENT:$SEM_VERSION -f Dockerfile .
docker tag minicardiac-$ENVIRONMENT:$SEM_VERSION $CONTAINER_REGISTRY_NAME/minicardiac-$ENVIRONMENT:$SEM_VERSION
docker push $CONTAINER_REGISTRY_NAME/minicardiac-$ENVIRONMENT:$SEM_VERSION
envsubst <task-definition.sample.json >task-definition.json
