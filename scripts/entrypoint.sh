#!/bin/sh
set -e

if [ -z $NODE_ENV ]; then
    echo "error: NODE_ENV not set in environment"
    exit 1
fi

if [ -z $DOPPLER_TOKEN ]; then
    echo "error: DOPPLER_TOKEN not set in environment"
    exit 1
fi

export HISTIGNORE='export DOPPLER_TOKEN*'
export DOPPLER_TOKEN=$DOPPLER_TOKEN

if [ $NODE_ENV == "production" ]; then
     doppler run -- npx knex --knexfile knexfile.js -- migrate:latest
     doppler run -- node main.js
else
    echo "error: NODE_ENV not set to production"
    exit 1
fi
