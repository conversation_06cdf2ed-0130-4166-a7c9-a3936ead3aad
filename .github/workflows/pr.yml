name: PR Validation

on:
  workflow_dispatch:
  pull_request:
    branches:
      - '*'
    types: [opened, synchronize, reopened]

# Set the access for individual scopes, or use permissions: write-all
permissions:
  pull-requests: write
  contents: write
  actions: read
  deployments: write
  id-token: write
  issues: write

env:
  NODE_VERSION: 22

jobs:
  lint:
    if: github.actor != 'dependabot[bot]'
    name: Validate Code
    runs-on: ubuntu-latest
    steps:
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: latest

      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Cache node modules
        uses: actions/cache@v4
        id: cache-npm
        with:
          path: node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Install Node.js dependencies
        if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        run: |
          pnpm install --frozen-lockfile

      - name: Run Formatter Check
        run: pnpm run prettier:check

      - name: Run Linter Check
        run: pnpm run lint

      - name: Run Type Check
        run: pnpm tsc
