name: <PERSON>elop Build & Deploy

on:
  push:
    branches:
      - develop

# Set the access for individual scopes, or use permissions: write-all
permissions:
  pull-requests: write
  contents: write
  actions: read
  deployments: write
  id-token: write
  issues: write

env:
  NODE_VERSION: 22

jobs:
  deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    environment: develop
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: latest

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Setup Buildx
        uses: docker/setup-buildx-action@v3

      - name: Publish Build Start Notification
        run: |
          VERSION=${GITHUB_SHA::8}
          aws sns publish \
            --topic-arn "$DEPLOYMENT_SNS_TOPIC_ARN" \
            --message '{"version":"1.0","source":"custom","content":{"textType":"client-markdown","title":":bell: Minicardiac Server - Build In-Progress for develop","description":"Building version '"$VERSION"'"}}'
        env:
          DEPLOYMENT_SNS_TOPIC_ARN: ${{ secrets.DEPLOYMENT_SNS_TOPIC_ARN }}

      - name: Build Image
        run: |
          export SEM_VERSION=$(echo ${GITHUB_SHA::7})
          source scripts/image_build.sh
        env:
          CONTAINER_REGISTRY_NAME: ${{ secrets.CONTAINER_REGISTRY_NAME }}
          ENVIRONMENT: dev
          DOPPLER_TOKEN_SECRETS_ARN: ${{ secrets.DOPPLER_TOKEN_SECRETS_ARN }}
          TASK_EXECUTION_ROLE: ${{ secrets.TASK_EXECUTION_ROLE }}
          PORT: ${{ secrets.PORT }}

      - name: Deploy to Amazon ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: task-definition.json
          service: api-service
          cluster: minicardiac-dev-ecs-cluster
          wait-for-service-stability: false
        env:
          CONTAINER_REGISTRY_NAME: ${{ secrets.CONTAINER_REGISTRY_NAME }}
          ENVIRONMENT: dev
          DOPPLER_TOKEN_SECRETS_ARN: ${{ secrets.DOPPLER_TOKEN_SECRETS_ARN }}
          TASK_EXECUTION_ROLE: ${{ secrets.TASK_EXECUTION_ROLE }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
          PORT: ${{ secrets.PORT }}

      - name: Publish Deployment Notification
        run: |
          VERSION=${GITHUB_SHA::8}
          aws sns publish \
            --topic-arn "$DEPLOYMENT_SNS_TOPIC_ARN" \
            --message '{"version":"1.0","source":"custom","content":{"textType":"client-markdown","title":":bell: Minicardiac Server - Deployment In-Progress for develop","description":"Version '"$VERSION"' was pushed to ECS for a deployment."}}'
        env:
          DEPLOYMENT_SNS_TOPIC_ARN: ${{ secrets.DEPLOYMENT_SNS_TOPIC_ARN }}

      - name: Notify Action Failure
        if: ${{ failure() }}
        run: |
          aws sns publish \
            --topic-arn "$DEPLOYMENT_SNS_TOPIC_ARN" \
            --message '{"version":"1.0","source":"custom","content":{"textType":"client-markdown","title":":bell: Minicardiac Server - Deployment Failed for develop","description":"Action failed to deploy to develop."}}'
        env:
          DEPLOYMENT_SNS_TOPIC_ARN: ${{ secrets.DEPLOYMENT_SNS_TOPIC_ARN }}
