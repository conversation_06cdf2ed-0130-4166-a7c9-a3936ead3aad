/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import capitalize from 'lodash.capitalize';

import * as systemExceptions from '@/exceptions/system';

@Injectable()
class ValidationPipe implements PipeTransform<any> {
  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const object = plainToInstance(metatype, value);
    const errors = await validate(object);

    if (errors.length > 0) {
      let errorMessage: string | undefined;
      const error = errors?.[0];

      if (error) {
        try {
          const [firstErrorMessage] = Object.values(error?.constraints as object);
          errorMessage = firstErrorMessage;
        } catch (ignoredError) {
          // this might likely be an issue with an array
          if ('children' in error) {
            // this is an error in the array of objects
            try {
              const errorObj = error?.children?.[0]?.children?.[0];
              const [firstErrorMessage] = Object.values(errorObj?.constraints as object);
              errorMessage = firstErrorMessage;
            } catch (errorArr) {
              throw systemExceptions.validationError();
            }
          }
        }
      }

      throw systemExceptions.validationError(errorMessage ? capitalize(errorMessage) : undefined, {
        metadata: errors,
      });
    }
    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}

export default ValidationPipe;
