import type { K<PERSON> } from 'knex';
import { getAuth } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';

import { AuthConstants } from '@/constants/auth';
import { Roles } from '@/constants/user-types';

// Constants
const SUPER_ADMIN_EMAIL = '<EMAIL>';
const SUPER_ADMIN_FIRST_NAME = 'minicardiac';
const SUPER_ADMIN_LAST_NAME = 'admin';

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  const config = envConfiguration();
  initializeApp({
    credential: cert(config.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  try {
    // Fetch super admin details from Firebase
    const firebaseUser = await getAuth().getUserByEmail(SUPER_ADMIN_EMAIL);
    const firebaseCustomClaims = firebaseUser.customClaims || {};

    const firebaseUserDetails = {
      id: firebaseUser.customClaims?.[AuthConstants.FIREBASE_CLAIM_USER_ID],
      provider_id: firebaseUser.uid,
    };

    await knex.transaction(async (trx) => {
      // Get current super admin details from local database
      const localUserDetails = await trx('users')
        .select('id', 'provider_id')
        .where({ email: SUPER_ADMIN_EMAIL })
        .first();

      // Update local database user if Firebase and local details don't match
      if (
        !localUserDetails ||
        firebaseUserDetails.id !== localUserDetails.id ||
        firebaseUserDetails.provider_id !== localUserDetails.provider_id
      ) {
        // Remove existing user if exists
        if (localUserDetails) {
          await trx('users').where({ email: SUPER_ADMIN_EMAIL }).delete();
        }

        // Create new user with Firebase details
        await trx('users').insert({
          ...firebaseUserDetails,
          email: SUPER_ADMIN_EMAIL,
          first_name: SUPER_ADMIN_FIRST_NAME,
          last_name: SUPER_ADMIN_LAST_NAME,
        });
      }

      // Get super admin role
      const superAdminRole = await trx('roles')
        .where('key', Roles.SUPER_ADMIN)
        .select('id')
        .first();

      if (!superAdminRole) {
        throw new Error('Super admin role not found in the database');
      }

      // Remove any existing roles for the super admin that are not super_admin
      await trx('user_roles')
        .where({ user_id: firebaseUserDetails.id })
        .whereNotIn('role_id', [superAdminRole.id])
        .delete();

      // Create user role association
      const [userRole] = await trx('user_roles')
        .insert({
          user_id: firebaseUserDetails.id,
          role_id: superAdminRole.id,
        })
        .returning('id');

      // Get all active permissions for super admin role
      const rolePermissions = await trx('role_permissions')
        .where({
          role_id: superAdminRole.id,
          status: 1,
        })
        .select('permission_id');

      // Create user permissions entries
      if (rolePermissions.length > 0) {
        const userPermissionsData = rolePermissions.map(({ permission_id }) => ({
          user_role_id: userRole.id,
          permission_id,
        }));

        await trx('user_permissions').insert(userPermissionsData);
      }

      // Get all permission keys for Firebase claims
      const permissionKeys = await trx('user_permissions')
        .where({
          user_role_id: userRole.id,
          'user_permissions.status': 1,
        })
        .leftJoin('permissions', 'user_permissions.permission_id', 'permissions.id')
        .select('permissions.key')
        .then((records) => records.map((record) => record.key));

      // Update Firebase custom claims
      const updatedClaims = {
        ...firebaseCustomClaims,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: Roles.SUPER_ADMIN,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: permissionKeys,
      };

      await getAuth().setCustomUserClaims(firebaseUserDetails.provider_id, updatedClaims);
    });
  } catch (error) {
    console.error('Error in super admin migration:', error);
    throw error; // Re-throw to ensure migration fails properly
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // Get the super admin user details from local database
    const superAdmin = await trx('users').select('id').where({ email: SUPER_ADMIN_EMAIL }).first();

    // Delete the user_roles entry of the super admin
    // Note: user_permissions will be automatically deleted due to cascade delete
    await trx('user_roles').where({ user_id: superAdmin.id }).delete();
  });
}
