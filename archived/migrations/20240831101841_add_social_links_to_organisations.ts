import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.text('instagram_link');
    t.text('facebook_link');
    t.text('twitter_link');
    t.text('linkedin_link');
    t.text('youtube_link');
    t.text('tiktok_link');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.dropColumn('instagram_link');
    t.dropColumn('facebook_link');
    t.dropColumn('twitter_link');
    t.dropColumn('linkedin_link');
    t.dropColumn('youtube_link');
    t.dropColumn('tiktok_link');
  });
}
