import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subscriptions', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('organisation_id')
        .notNullable()
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_subscriptions_on_organisation_id');
      t.date('start_date').notNullable();
      t.date('end_date').notNullable();
      t.date('payment_date').notNullable();
      t.text('payment_method').notNullable();
      t.smallint('amount').notNullable();
      t.text('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subscriptions_updated_at BEFORE UPDATE
ON subscriptions FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subscriptions');
}
