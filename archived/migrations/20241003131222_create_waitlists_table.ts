import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('waitlists', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
    t.text('first_name').notNullable();
    t.text('last_name').notNullable();
    t.text('email').notNullable().unique();
    t.integer('status').defaultTo(1);
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
  }).raw(`
    CREATE TRIGGER update_waitlists_updated_at BEFORE UPDATE
    ON waitlists FOR EACH ROW
    EXECUTE PROCEDURE update_updated_at_column(); 
  `);
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('waitlists');
}
