import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.text('hq_location').notNullable();
    t.text('registration_number').notNullable();
    t.uuid('country_id')
      .references('id')
      .inTable('countries')
      .onDelete('CASCADE')
      .index('index_organisations_on_country_id')
      .notNullable();
    t.string('email').notNullable().unique();
    t.text('website');
    t.string('phone_number', 30).notNullable();
    t.text('zip_code').notNullable();
    t.text('about_us');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.dropColumn('hq_location');
    t.dropColumn('registration_number');
    t.dropColumn('country_id');
    t.dropColumn('email');
    t.dropColumn('website');
    t.dropColumn('phone_number');
    t.dropColumn('zip_code');
    t.dropColumn('about_us');
  });
}
