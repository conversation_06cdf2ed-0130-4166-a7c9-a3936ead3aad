import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('subscription_countries', (t) => {
    t.dropPrimary();
    t.dropIndex('subscription_id', 'index_subscription_countries_on_subscription_id');
    t.dropColumn('subscription_id');

    t.uuid('subscription_workspace_id')
      .notNullable()
      .references('id')
      .inTable('subscription_workspaces')
      .onDelete('CASCADE')
      .index('index_subscription_countries_on_subscription_workspace_id');

    t.primary(['subscription_workspace_id', 'country_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('subscription_countries', (t) => {
    t.dropPrimary();
    t.dropIndex(
      'subscription_workspace_id',
      'index_subscription_countries_on_subscription_workspace_id',
    );
    t.dropColumn('subscription_workspace_id');

    t.uuid('subscription_id')
      .notNullable()
      .references('id')
      .inTable('subscriptions')
      .onDelete('CASCADE')
      .index('index_subscription_countries_on_subscription_id');

    t.primary(['subscription_id', 'country_id']);
  });
}
