import type { Knex } from 'knex';

// Database table names
const TABLES = {
  SUBTYPES: 'subtypes',
  WORKSPACES: 'workspaces',
} as const;

// Column names for each table
const COLUMNS = {
  SUBTYPES: {
    ACCOUNT_TYPE: 'account_type',
  },
  WORKSPACES: {
    TYPE: 'type',
  },
} as const;

// Account type mapping constants
const OLD_ACCOUNT_TYPES = {
  ORGANISATION: 'organisation',
  GENERAL_PUBLIC: 'general_public',
  SURGEON: 'cardiac_surgeon',
  PROFESSIONAL: 'professional',
} as const;

const NEW_ACCOUNT_TYPES = {
  ORGANISATION: 'ORGANISATION',
  GENERAL_PUBLIC: 'GENERAL_PUBLIC',
  SURGEON: 'SURGEON',
  PROFESSIONAL: 'PROFESSIONAL',
} as const;

// Types for the mappings
type SubtypesMapping = {
  oldValue: (typeof OLD_ACCOUNT_TYPES)[keyof typeof OLD_ACCOUNT_TYPES];
  newValue: (typeof NEW_ACCOUNT_TYPES)[keyof typeof NEW_ACCOUNT_TYPES];
};

// Mappings for subtypes table (includes all types)
const SUBTYPES_MAPPINGS: SubtypesMapping[] = [
  { oldValue: OLD_ACCOUNT_TYPES.ORGANISATION, newValue: NEW_ACCOUNT_TYPES.ORGANISATION },
  { oldValue: OLD_ACCOUNT_TYPES.GENERAL_PUBLIC, newValue: NEW_ACCOUNT_TYPES.GENERAL_PUBLIC },
  { oldValue: OLD_ACCOUNT_TYPES.SURGEON, newValue: NEW_ACCOUNT_TYPES.SURGEON },
  { oldValue: OLD_ACCOUNT_TYPES.PROFESSIONAL, newValue: NEW_ACCOUNT_TYPES.PROFESSIONAL },
];

// Mappings for workspaces table (excludes GENERAL_PUBLIC)
const WORKSPACES_MAPPINGS: SubtypesMapping[] = SUBTYPES_MAPPINGS.filter(
  (mapping) => mapping.oldValue !== OLD_ACCOUNT_TYPES.GENERAL_PUBLIC,
);

export async function up(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // Update subtypes table
    await Promise.all(
      SUBTYPES_MAPPINGS.map(({ oldValue, newValue }) =>
        trx(TABLES.SUBTYPES)
          .where({ [COLUMNS.SUBTYPES.ACCOUNT_TYPE]: oldValue })
          .update({ [COLUMNS.SUBTYPES.ACCOUNT_TYPE]: newValue }),
      ),
    );

    // Update workspaces table
    await Promise.all(
      WORKSPACES_MAPPINGS.map(({ oldValue, newValue }) =>
        trx(TABLES.WORKSPACES)
          .where({ [COLUMNS.WORKSPACES.TYPE]: oldValue })
          .update({ [COLUMNS.WORKSPACES.TYPE]: newValue }),
      ),
    );
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // Revert subtypes table
    await Promise.all(
      SUBTYPES_MAPPINGS.map(({ oldValue, newValue }) =>
        trx(TABLES.SUBTYPES)
          .where({ [COLUMNS.SUBTYPES.ACCOUNT_TYPE]: newValue })
          .update({ [COLUMNS.SUBTYPES.ACCOUNT_TYPE]: oldValue }),
      ),
    );

    // Revert workspaces table
    await Promise.all(
      WORKSPACES_MAPPINGS.map(({ oldValue, newValue }) =>
        trx(TABLES.WORKSPACES)
          .where({ [COLUMNS.WORKSPACES.TYPE]: newValue })
          .update({ [COLUMNS.WORKSPACES.TYPE]: oldValue }),
      ),
    );
  });
}
