import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subscription_countries', (t) => {
      t.primary(['subscription_id', 'country_id']);
      t.uuid('subscription_id')
        .notNullable()
        .references('id')
        .inTable('subscriptions')
        .onDelete('CASCADE')
        .index('index_subscription_countries_on_subscription_id');
      t.uuid('country_id')
        .notNullable()
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_subscription_countries_on_country_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subscription_countries_updated_at BEFORE UPDATE
ON subscription_countries FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subscription_countries');
}
