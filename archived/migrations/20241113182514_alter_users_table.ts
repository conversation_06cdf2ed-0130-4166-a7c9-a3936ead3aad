import type { Knex } from 'knex';

const TABLE_NAME = 'users';

const COLUMN_NAME = 'provider_id';
const DEFAULT_VALUE = '';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, (table) => {
    table.text(COLUMN_NAME).notNullable().defaultTo(DEFAULT_VALUE).alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, (table) => {
    table.text(COLUMN_NAME).notNullable().alter();
  });
}
