import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('opportunities_attachments', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
    t.uuid('opportunity_id')
      .notNullable()
      .references('id')
      .inTable('opportunities')
      .onDelete('CASCADE')
      .index('index_opportunities_attachments_on_opportunity_id');
    t.text('file_path').notNullable();
    t.text('file_type').notNullable();
    t.float('file_size').notNullable();
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.integer('status').defaultTo(1);
  }).raw(`
    CREATE TRIGGER opportunities_attachments_updated_at BEFORE UPDATE
    ON opportunities_attachments FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
  `);
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('opportunities_attachments');
}
