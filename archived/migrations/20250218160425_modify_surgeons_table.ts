import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('surgeons', (table) => {
    table.integer('years_of_experience').nullable();
    table.text('current_hospital').nullable();
    table.text('email').nullable();
    table.text('profile_summary').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('surgeons', (table) => {
    table.dropColumns('years_of_experience', 'current_hospital', 'email', 'profile_summary');
  });
}
