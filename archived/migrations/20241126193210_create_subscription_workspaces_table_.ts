import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subscription_workspaces', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('subscription_id')
        .notNullable()
        .references('id')
        .inTable('subscriptions')
        .onDelete('CASCADE')
        .index('index_subscription_workspaces_on_subscription_id');
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_subscription_workspaces_on_workspace_id');
      t.date('start_date').notNullable();
      t.date('end_date').notNullable();
      t.date('payment_date').notNullable();
      t.text('payment_method').notNullable();
      t.text('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subscription_workspaces_updated_at BEFORE UPDATE
ON subscription_workspaces FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subscription_workspaces');
}
