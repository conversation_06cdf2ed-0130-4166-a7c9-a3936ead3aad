import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // First, add a temporary nullable column
  await knex.schema.alterTable('posts', (t) => {
    t.timestamp('post_schedule_date_new', { useTz: true }).nullable();
  });

  // Update the new column with created_at values for existing records
  await knex.raw(`
    UPDATE posts 
    SET post_schedule_date_new = created_at 
    WHERE post_schedule_date_new IS NULL
  `);

  // Make the new column not nullable
  await knex.schema.alterTable('posts', (t) => {
    t.timestamp('post_schedule_date_new', { useTz: true }).notNullable().alter();
  });

  // Drop the old column
  await knex.schema.alterTable('posts', (t) => {
    t.dropColumn('post_schedule_date');
  });

  // Rename the new column to the original name
  await knex.schema.alterTable('posts', (t) => {
    t.renameColumn('post_schedule_date_new', 'post_schedule_date');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Similar pattern - create temporary column first
  await knex.schema.alterTable('posts', (t) => {
    t.date('post_schedule_date_old').nullable();
  });

  // Convert timestamp to date for all records
  await knex.raw(`
    UPDATE posts 
    SET post_schedule_date_old = post_schedule_date::date
  `);

  // Make not nullable after data is populated
  await knex.schema.alterTable('posts', (t) => {
    t.date('post_schedule_date_old').notNullable().alter();
  });

  // Drop the timestamp column
  await knex.schema.alterTable('posts', (t) => {
    t.dropColumn('post_schedule_date');
  });

  // Rename back to original name
  await knex.schema.alterTable('posts', (t) => {
    t.renameColumn('post_schedule_date_old', 'post_schedule_date');
  });
}
