import type { Knex } from 'knex';

const countriesData = [
  { Name: 'Afghanistan', Code: 'AF', Continent: 'Asia' },
  { Name: 'Albania', Code: 'AL', Continent: 'Europe' },
  { Name: 'Algeria', Code: 'DZ', Continent: 'Africa' },
  { Name: 'American Samoa', Code: 'AS', Continent: 'Oceania' },
  { Name: 'Andorra', Code: 'AD', Continent: 'Europe' },
  { Name: 'Angola', Code: 'AO', Continent: 'Africa' },
  { Name: 'Anguilla', Code: 'AI', Continent: 'North America' },
  { Name: 'Antarctica', Code: 'AQ', Continent: 'Antarctica' },
  { Name: 'Antigua and Barbuda', Code: 'AG', Continent: 'North America' },
  { Name: 'Argentina', Code: 'AR', Continent: 'South America' },
  { Name: 'Armenia', Code: 'AM', Continent: 'Asia' },
  { Name: 'Aruba', Code: 'AW', Continent: 'North America' },
  { Name: 'Australia', Code: 'AU', Continent: 'Oceania' },
  { Name: 'Austria', Code: 'AT', Continent: 'Europe' },
  { Name: 'Azerbaijan', Code: 'AZ', Continent: 'Asia' },
  { Name: 'Bahamas', Code: 'BS', Continent: 'North America' },
  { Name: 'Bahrain', Code: 'BH', Continent: 'Asia' },
  { Name: 'Bangladesh', Code: 'BD', Continent: 'Asia' },
  { Name: 'Barbados', Code: 'BB', Continent: 'North America' },
  { Name: 'Belarus', Code: 'BY', Continent: 'Europe' },
  { Name: 'Belgium', Code: 'BE', Continent: 'Europe' },
  { Name: 'Belize', Code: 'BZ', Continent: 'North America' },
  { Name: 'Benin', Code: 'BJ', Continent: 'Africa' },
  { Name: 'Bermuda', Code: 'BM', Continent: 'North America' },
  { Name: 'Bhutan', Code: 'BT', Continent: 'Asia' },
  { Name: 'Bolivia, Plurinational State of', Code: 'BO', Continent: 'South America' },
  { Name: 'Bonaire, Sint Eustatius and Saba', Code: 'BQ', Continent: 'North America' },
  { Name: 'Bosnia and Herzegovina', Code: 'BA', Continent: 'Europe' },
  { Name: 'Botswana', Code: 'BW', Continent: 'Africa' },
  { Name: 'Bouvet Island', Code: 'BV', Continent: 'Antarctica' },
  { Name: 'Brazil', Code: 'BR', Continent: 'South America' },
  { Name: 'British Indian Ocean Territory', Code: 'IO', Continent: 'Asia' },
  { Name: 'Brunei Darussalam', Code: 'BN', Continent: 'Asia' },
  { Name: 'Bulgaria', Code: 'BG', Continent: 'Europe' },
  { Name: 'Burkina Faso', Code: 'BF', Continent: 'Africa' },
  { Name: 'Burundi', Code: 'BI', Continent: 'Africa' },
  { Name: 'Cambodia', Code: 'KH', Continent: 'Asia' },
  { Name: 'Cameroon', Code: 'CM', Continent: 'Africa' },
  { Name: 'Canada', Code: 'CA', Continent: 'North America' },
  { Name: 'Cape Verde', Code: 'CV', Continent: 'Africa' },
  { Name: 'Cayman Islands', Code: 'KY', Continent: 'North America' },
  { Name: 'Central African Republic', Code: 'CF', Continent: 'Africa' },
  { Name: 'Chad', Code: 'TD', Continent: 'Africa' },
  { Name: 'Chile', Code: 'CL', Continent: 'South America' },
  { Name: 'China', Code: 'CN', Continent: 'Asia' },
  { Name: 'Christmas Island', Code: 'CX', Continent: 'Asia' },
  { Name: 'Cocos (Keeling) Islands', Code: 'CC', Continent: 'Asia' },
  { Name: 'Colombia', Code: 'CO', Continent: 'South America' },
  { Name: 'Comoros', Code: 'KM', Continent: 'Africa' },
  { Name: 'Congo', Code: 'CG', Continent: 'Africa' },
  { Name: 'Congo, the Democratic Republic of the', Code: 'CD', Continent: 'Africa' },
  { Name: 'Cook Islands', Code: 'CK', Continent: 'Oceania' },
  { Name: 'Costa Rica', Code: 'CR', Continent: 'North America' },
  { Name: 'Croatia', Code: 'HR', Continent: 'Europe' },
  { Name: 'Cuba', Code: 'CU', Continent: 'North America' },
  { Name: 'Curaçao', Code: 'CW', Continent: 'North America' },
  { Name: 'Cyprus', Code: 'CY', Continent: 'Asia' },
  { Name: 'Czech Republic', Code: 'CZ', Continent: 'Europe' },
  { Name: "Côte d'Ivoire", Code: 'CI', Continent: 'Africa' },
  { Name: 'Denmark', Code: 'DK', Continent: 'Europe' },
  { Name: 'Djibouti', Code: 'DJ', Continent: 'Africa' },
  { Name: 'Dominica', Code: 'DM', Continent: 'North America' },
  { Name: 'Dominican Republic', Code: 'DO', Continent: 'North America' },
  { Name: 'Ecuador', Code: 'EC', Continent: 'South America' },
  { Name: 'Egypt', Code: 'EG', Continent: 'Africa' },
  { Name: 'El Salvador', Code: 'SV', Continent: 'North America' },
  { Name: 'Equatorial Guinea', Code: 'GQ', Continent: 'Africa' },
  { Name: 'Eritrea', Code: 'ER', Continent: 'Africa' },
  { Name: 'Estonia', Code: 'EE', Continent: 'Europe' },
  { Name: 'Ethiopia', Code: 'ET', Continent: 'Africa' },
  { Name: 'Falkland Islands (Malvinas)', Code: 'FK', Continent: 'South America' },
  { Name: 'Faroe Islands', Code: 'FO', Continent: 'Europe' },
  { Name: 'Fiji', Code: 'FJ', Continent: 'Oceania' },
  { Name: 'Finland', Code: 'FI', Continent: 'Europe' },
  { Name: 'France', Code: 'FR', Continent: 'Europe' },
  { Name: 'French Guiana', Code: 'GF', Continent: 'South America' },
  { Name: 'French Polynesia', Code: 'PF', Continent: 'Oceania' },
  { Name: 'French Southern Territories', Code: 'TF', Continent: 'Antarctica' },
  { Name: 'Gabon', Code: 'GA', Continent: 'Africa' },
  { Name: 'Gambia', Code: 'GM', Continent: 'Africa' },
  { Name: 'Georgia', Code: 'GE', Continent: 'Asia' },
  { Name: 'Germany', Code: 'DE', Continent: 'Europe' },
  { Name: 'Ghana', Code: 'GH', Continent: 'Africa' },
  { Name: 'Gibraltar', Code: 'GI', Continent: 'Europe' },
  { Name: 'Greece', Code: 'GR', Continent: 'Europe' },
  { Name: 'Greenland', Code: 'GL', Continent: 'North America' },
  { Name: 'Grenada', Code: 'GD', Continent: 'North America' },
  { Name: 'Guadeloupe', Code: 'GP', Continent: 'North America' },
  { Name: 'Guam', Code: 'GU', Continent: 'Oceania' },
  { Name: 'Guatemala', Code: 'GT', Continent: 'North America' },
  { Name: 'Guernsey', Code: 'GG', Continent: 'Europe' },
  { Name: 'Guinea', Code: 'GN', Continent: 'Africa' },
  { Name: 'Guinea-Bissau', Code: 'GW', Continent: 'Africa' },
  { Name: 'Guyana', Code: 'GY', Continent: 'South America' },
  { Name: 'Haiti', Code: 'HT', Continent: 'North America' },
  { Name: 'Heard Island and McDonald Islands', Code: 'HM', Continent: 'Antarctica' },
  { Name: 'Holy See (Vatican City State)', Code: 'VA', Continent: 'Europe' },
  { Name: 'Honduras', Code: 'HN', Continent: 'North America' },
  { Name: 'Hong Kong', Code: 'HK', Continent: 'Asia' },
  { Name: 'Hungary', Code: 'HU', Continent: 'Europe' },
  { Name: 'Iceland', Code: 'IS', Continent: 'Europe' },
  { Name: 'India', Code: 'IN', Continent: 'Asia' },
  { Name: 'Indonesia', Code: 'ID', Continent: 'Asia' },
  { Name: 'Iran, Islamic Republic of', Code: 'IR', Continent: 'Asia' },
  { Name: 'Iraq', Code: 'IQ', Continent: 'Asia' },
  { Name: 'Ireland', Code: 'IE', Continent: 'Europe' },
  { Name: 'Isle of Man', Code: 'IM', Continent: 'Europe' },
  { Name: 'Israel', Code: 'IL', Continent: 'Asia' },
  { Name: 'Italy', Code: 'IT', Continent: 'Europe' },
  { Name: 'Jamaica', Code: 'JM', Continent: 'North America' },
  { Name: 'Japan', Code: 'JP', Continent: 'Asia' },
  { Name: 'Jersey', Code: 'JE', Continent: 'Europe' },
  { Name: 'Jordan', Code: 'JO', Continent: 'Asia' },
  { Name: 'Kazakhstan', Code: 'KZ', Continent: 'Asia' },
  { Name: 'Kenya', Code: 'KE', Continent: 'Africa' },
  { Name: 'Kiribati', Code: 'KI', Continent: 'Oceania' },
  { Name: "Korea, Democratic People's Republic of", Code: 'KP', Continent: 'Asia' },
  { Name: 'Korea, Republic of', Code: 'KR', Continent: 'Asia' },
  { Name: 'Kuwait', Code: 'KW', Continent: 'Asia' },
  { Name: 'Kyrgyzstan', Code: 'KG', Continent: 'Asia' },
  { Name: "Lao People's Democratic Republic", Code: 'LA', Continent: 'Asia' },
  { Name: 'Latvia', Code: 'LV', Continent: 'Europe' },
  { Name: 'Lebanon', Code: 'LB', Continent: 'Asia' },
  { Name: 'Lesotho', Code: 'LS', Continent: 'Africa' },
  { Name: 'Liberia', Code: 'LR', Continent: 'Africa' },
  { Name: 'Libya', Code: 'LY', Continent: 'Africa' },
  { Name: 'Liechtenstein', Code: 'LI', Continent: 'Europe' },
  { Name: 'Lithuania', Code: 'LT', Continent: 'Europe' },
  { Name: 'Luxembourg', Code: 'LU', Continent: 'Europe' },
  { Name: 'Macao', Code: 'MO', Continent: 'Asia' },
  { Name: 'Macedonia, the Former Yugoslav Republic of', Code: 'MK', Continent: 'Europe' },
  { Name: 'Madagascar', Code: 'MG', Continent: 'Africa' },
  { Name: 'Malawi', Code: 'MW', Continent: 'Africa' },
  { Name: 'Malaysia', Code: 'MY', Continent: 'Asia' },
  { Name: 'Maldives', Code: 'MV', Continent: 'Asia' },
  { Name: 'Mali', Code: 'ML', Continent: 'Africa' },
  { Name: 'Malta', Code: 'MT', Continent: 'Europe' },
  { Name: 'Marshall Islands', Code: 'MH', Continent: 'Oceania' },
  { Name: 'Martinique', Code: 'MQ', Continent: 'North America' },
  { Name: 'Mauritania', Code: 'MR', Continent: 'Africa' },
  { Name: 'Mauritius', Code: 'MU', Continent: 'Africa' },
  { Name: 'Mayotte', Code: 'YT', Continent: 'Africa' },
  { Name: 'Mexico', Code: 'MX', Continent: 'North America' },
  { Name: 'Micronesia, Federated States of', Code: 'FM', Continent: 'Oceania' },
  { Name: 'Moldova, Republic of', Code: 'MD', Continent: 'Europe' },
  { Name: 'Monaco', Code: 'MC', Continent: 'Europe' },
  { Name: 'Mongolia', Code: 'MN', Continent: 'Asia' },
  { Name: 'Montenegro', Code: 'ME', Continent: 'Europe' },
  { Name: 'Montserrat', Code: 'MS', Continent: 'North America' },
  { Name: 'Morocco', Code: 'MA', Continent: 'Africa' },
  { Name: 'Mozambique', Code: 'MZ', Continent: 'Africa' },
  { Name: 'Myanmar', Code: 'MM', Continent: 'Asia' },
  { Name: 'Namibia', Code: 'NA', Continent: 'Africa' },
  { Name: 'Nauru', Code: 'NR', Continent: 'Oceania' },
  { Name: 'Nepal', Code: 'NP', Continent: 'Asia' },
  { Name: 'Netherlands', Code: 'NL', Continent: 'Europe' },
  { Name: 'New Caledonia', Code: 'NC', Continent: 'Oceania' },
  { Name: 'New Zealand', Code: 'NZ', Continent: 'Oceania' },
  { Name: 'Nicaragua', Code: 'NI', Continent: 'North America' },
  { Name: 'Niger', Code: 'NE', Continent: 'Africa' },
  { Name: 'Nigeria', Code: 'NG', Continent: 'Africa' },
  { Name: 'Niue', Code: 'NU', Continent: 'Oceania' },
  { Name: 'Norfolk Island', Code: 'NF', Continent: 'Oceania' },
  { Name: 'Northern Mariana Islands', Code: 'MP', Continent: 'Oceania' },
  { Name: 'Norway', Code: 'NO', Continent: 'Europe' },
  { Name: 'Oman', Code: 'OM', Continent: 'Asia' },
  { Name: 'Pakistan', Code: 'PK', Continent: 'Asia' },
  { Name: 'Palau', Code: 'PW', Continent: 'Oceania' },
  { Name: 'Palestine, State of', Code: 'PS', Continent: 'Asia' },
  { Name: 'Panama', Code: 'PA', Continent: 'North America' },
  { Name: 'Papua New Guinea', Code: 'PG', Continent: 'Oceania' },
  { Name: 'Paraguay', Code: 'PY', Continent: 'South America' },
  { Name: 'Peru', Code: 'PE', Continent: 'South America' },
  { Name: 'Philippines', Code: 'PH', Continent: 'Asia' },
  { Name: 'Pitcairn', Code: 'PN', Continent: 'Oceania' },
  { Name: 'Poland', Code: 'PL', Continent: 'Europe' },
  { Name: 'Portugal', Code: 'PT', Continent: 'Europe' },
  { Name: 'Puerto Rico', Code: 'PR', Continent: 'North America' },
  { Name: 'Qatar', Code: 'QA', Continent: 'Asia' },
  { Name: 'Romania', Code: 'RO', Continent: 'Europe' },
  { Name: 'Russian Federation', Code: 'RU', Continent: 'Europe' },
  { Name: 'Rwanda', Code: 'RW', Continent: 'Africa' },
  { Name: 'Réunion', Code: 'RE', Continent: 'Africa' },
  { Name: 'Saint Barthélemy', Code: 'BL', Continent: 'North America' },
  { Name: 'Saint Helena, Ascension and Tristan da Cunha', Code: 'SH', Continent: 'Africa' },
  { Name: 'Saint Kitts and Nevis', Code: 'KN', Continent: 'North America' },
  { Name: 'Saint Lucia', Code: 'LC', Continent: 'North America' },
  { Name: 'Saint Martin (French part)', Code: 'MF', Continent: 'North America' },
  { Name: 'Saint Pierre and Miquelon', Code: 'PM', Continent: 'North America' },
  { Name: 'Saint Vincent and the Grenadines', Code: 'VC', Continent: 'North America' },
  { Name: 'Samoa', Code: 'WS', Continent: 'Oceania' },
  { Name: 'San Marino', Code: 'SM', Continent: 'Europe' },
  { Name: 'Sao Tome and Principe', Code: 'ST', Continent: 'Africa' },
  { Name: 'Saudi Arabia', Code: 'SA', Continent: 'Asia' },
  { Name: 'Senegal', Code: 'SN', Continent: 'Africa' },
  { Name: 'Serbia', Code: 'RS', Continent: 'Europe' },
  { Name: 'Seychelles', Code: 'SC', Continent: 'Africa' },
  { Name: 'Sierra Leone', Code: 'SL', Continent: 'Africa' },
  { Name: 'Singapore', Code: 'SG', Continent: 'Asia' },
  { Name: 'Sint Maarten (Dutch part)', Code: 'SX', Continent: 'North America' },
  { Name: 'Slovakia', Code: 'SK', Continent: 'Europe' },
  { Name: 'Slovenia', Code: 'SI', Continent: 'Europe' },
  { Name: 'Solomon Islands', Code: 'SB', Continent: 'Oceania' },
  { Name: 'Somalia', Code: 'SO', Continent: 'Africa' },
  { Name: 'South Africa', Code: 'ZA', Continent: 'Africa' },
  {
    Name: 'South Georgia and the South Sandwich Islands',
    Code: 'GS',
    Continent: 'Antarctica',
  },
  { Name: 'South Sudan', Code: 'SS', Continent: 'Africa' },
  { Name: 'Spain', Code: 'ES', Continent: 'Europe' },
  { Name: 'Sri Lanka', Code: 'LK', Continent: 'Asia' },
  { Name: 'Sudan', Code: 'SD', Continent: 'Africa' },
  { Name: 'Suriname', Code: 'SR', Continent: 'South America' },
  { Name: 'Svalbard and Jan Mayen', Code: 'SJ', Continent: 'Europe' },
  { Name: 'Swaziland', Code: 'SZ', Continent: 'Africa' },
  { Name: 'Sweden', Code: 'SE', Continent: 'Europe' },
  { Name: 'Switzerland', Code: 'CH', Continent: 'Europe' },
  { Name: 'Syrian Arab Republic', Code: 'SY', Continent: 'Asia' },
  { Name: 'Taiwan, Province of China', Code: 'TW', Continent: 'Asia' },
  { Name: 'Tajikistan', Code: 'TJ', Continent: 'Asia' },
  { Name: 'Tanzania, United Republic of', Code: 'TZ', Continent: 'Africa' },
  { Name: 'Thailand', Code: 'TH', Continent: 'Asia' },
  { Name: 'Timor-Leste', Code: 'TL', Continent: 'Asia' },
  { Name: 'Togo', Code: 'TG', Continent: 'Africa' },
  { Name: 'Tokelau', Code: 'TK', Continent: 'Oceania' },
  { Name: 'Tonga', Code: 'TO', Continent: 'Oceania' },
  { Name: 'Trinidad and Tobago', Code: 'TT', Continent: 'North America' },
  { Name: 'Tunisia', Code: 'TN', Continent: 'Africa' },
  { Name: 'Turkey', Code: 'TR', Continent: 'Asia' },
  { Name: 'Turkmenistan', Code: 'TM', Continent: 'Asia' },
  { Name: 'Turks and Caicos Islands', Code: 'TC', Continent: 'North America' },
  { Name: 'Tuvalu', Code: 'TV', Continent: 'Oceania' },
  { Name: 'Uganda', Code: 'UG', Continent: 'Africa' },
  { Name: 'Ukraine', Code: 'UA', Continent: 'Europe' },
  { Name: 'United Arab Emirates', Code: 'AE', Continent: 'Asia' },
  { Name: 'United Kingdom', Code: 'GB', Continent: 'Europe' },
  { Name: 'United States', Code: 'US', Continent: 'North America' },
  { Name: 'United States Minor Outlying Islands', Code: 'UM', Continent: 'Oceania' },
  { Name: 'Uruguay', Code: 'UY', Continent: 'South America' },
  { Name: 'Uzbekistan', Code: 'UZ', Continent: 'Asia' },
  { Name: 'Vanuatu', Code: 'VU', Continent: 'Oceania' },
  { Name: 'Venezuela, Bolivarian Republic of', Code: 'VE', Continent: 'South America' },
  { Name: 'Viet Nam', Code: 'VN', Continent: 'Asia' },
  { Name: 'Virgin Islands, British', Code: 'VG', Continent: 'North America' },
  { Name: 'Virgin Islands, U.S.', Code: 'VI', Continent: 'North America' },
  { Name: 'Wallis and Futuna', Code: 'WF', Continent: 'Oceania' },
  { Name: 'Western Sahara', Code: 'EH', Continent: 'Africa' },
  { Name: 'Yemen', Code: 'YE', Continent: 'Asia' },
  { Name: 'Zambia', Code: 'ZM', Continent: 'Africa' },
  { Name: 'Zimbabwe', Code: 'ZW', Continent: 'Africa' },
  { Name: 'Åland Islands', Code: 'AX', Continent: 'Europe' },
];

export async function up(knex: Knex): Promise<void> {
  const countriesToInsert = [];

  for (const country of countriesData) {
    const { Name, Code, Continent } = country;

    const continentRecord = await knex('continents')
      .select('id')
      .where({ name: Continent })
      .first();

    if (continentRecord) {
      countriesToInsert.push({
        name: Name,
        code: Code,
        continent_id: continentRecord.id,
      });
    } else {
      console.error(`Continent not found for: ${Continent}`);
    }
  }

  if (countriesToInsert.length > 0) {
    await knex('countries').insert(countriesToInsert);
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('countries').del();
}
