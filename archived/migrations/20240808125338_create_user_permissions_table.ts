import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Ensure that the user_roles table exists before creating the foreign key
  const tableExists = await knex.schema.hasTable('user_roles');

  if (!tableExists) {
    throw new Error('The user_roles table does not exist. Please create it first.');
  }

  return knex.schema
    .createTable('user_permissions', (t) => {
      t.primary(['user_role_id', 'permission_id']);
      t.uuid('user_role_id')
        .notNullable()
        .references('id')
        .inTable('user_roles')
        .onDelete('CASCADE')
        .index('index_user_permissions_on_user_role_id');
      t.uuid('permission_id')
        .notNullable()
        .references('id')
        .inTable('permissions')
        .onDelete('CASCADE')
        .index('index_user_permissions_on_permission_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER user_permissions_updated_at BEFORE UPDATE
ON user_permissions FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('user_permissions');
}
