import type { Knex } from 'knex';

import { EntityType } from '@/constants/user-types';

export async function up(knex: Knex): Promise<void> {
  // Update post_likes table
  await knex.schema.alterTable('post_likes', (t) => {
    // Remove existing constraints and indexes on user_id
    t.dropPrimary();
    t.dropIndex([], 'index_post_likes_on_user_id');
    t.dropForeign(['user_id']);

    // Rename user_id to entity_id to accommodate both user and workspace IDs
    t.renameColumn('user_id', 'entity_id');

    // Add a new column to store the entity type (either 'user' or 'workspace')
    t.text('entity_type').notNullable().defaultTo(EntityType.USER);

    // Re-establish primary key using post_id and entity_id
    t.primary(['post_id', 'entity_id']);
    t.index(['entity_id'], 'index_post_likes_on_entity_id');
  });

  // Updating post_comments table to support both user_id and workspace_id
  await knex.schema.alterTable('post_comments', (t) => {
    // Remove existing constraints and indexes on user_id
    t.dropIndex([], 'index_post_comments_on_user_id');
    t.dropForeign(['user_id']);

    // Rename user_id to entity_id to accommodate both user and workspace IDs
    t.renameColumn('user_id', 'entity_id');

    // Add a new column to store the entity type (either 'user' or 'workspace')
    t.text('entity_type').notNullable().defaultTo(EntityType.USER);

    t.index(['entity_id'], 'index_post_comments_on_entity_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  // Reverting changes to post_comments table
  await knex.schema.alterTable('post_comments', (t) => {
    // Remove the newly added entity_type column and indexes
    t.dropIndex([], 'index_post_comments_on_entity_id');
    t.dropColumn('entity_type');

    // Rename entity_id back to user_id
    t.renameColumn('entity_id', 'user_id');

    // Restore foreign key constraint on user_id
    t.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    t.index('user_id', 'index_post_comments_on_user_id');
  });

  // Reverting changes to post_likes table
  await knex.schema.alterTable('post_likes', (t) => {
    // Remove the newly added entity_type column and indexes
    t.dropPrimary();
    t.dropIndex([], 'index_post_likes_on_entity_id');
    t.dropColumn('entity_type');

    // Rename entity_id back to user_id
    t.renameColumn('entity_id', 'user_id');

    // Restore foreign key constraint on user_id
    t.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    t.index('user_id', 'index_post_likes_on_user_id');

    // Re-establish primary key using post_id and user_id
    t.primary(['post_id', 'user_id']);
  });
}
