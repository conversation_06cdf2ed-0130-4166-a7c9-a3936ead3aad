import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // First add the column as nullable
  await knex.schema.alterTable('users', (table) => {
    table.text('username').nullable().unique();
  });

  // Update existing records with the constructed username
  await knex.raw(`
    UPDATE users 
    SET username = LOWER(
      CONCAT(
        REPLACE(TRIM(first_name), ' ', ''),
        '-',
        REPLACE(TRIM(last_name), ' ', ''),
        '-',
        id::text
      )
    )
  `);

  // After all records have a username, make the column not nullable
  await knex.schema.alterTable('users', (table) => {
    table.text('username').notNullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('users', (table) => {
    table.dropColumn('username');
  });
}
