import type { K<PERSON> } from 'knex';
import { getAuth } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';

const envConfig = envConfiguration();
if (!getApps().length) {
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  // Fetch the admin user from the database
  const superAdmin = await knex('users')
    .where({ email: '<EMAIL>' })
    .first()
    .select('provider_id');

  if (!superAdmin || !superAdmin.provider_id) {
    throw new Error('Admin user not found in the local database or provider_id is missing.');
  }

  try {
    await getAuth().updateUser(superAdmin.provider_id, {
      password: envConfig.superAdminPassword,
    });
  } catch (error) {
    throw new Error(`Failed to update password for admin user: ${error.message}`);
  }
}

export async function down(knex: Knex): Promise<void> {
  // Fetch the admin user from the database
  const superAdmin = await knex('users')
    .where({ email: '<EMAIL>' })
    .first()
    .select('provider_id');

  if (!superAdmin || !superAdmin.provider_id) {
    throw new Error('Admin user not found in the local database or provider_id is missing.');
  }

  try {
    await getAuth().updateUser(superAdmin.provider_id, {
      password: 'hello_world',
    });
  } catch (error) {
    throw new Error(`Failed to update password for admin user: ${error.message}`);
  }
}
