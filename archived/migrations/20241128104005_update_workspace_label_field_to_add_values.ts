import { K<PERSON> } from 'knex';

import { SUBTYPES } from '@/constants/subtypes';
import { WorkspaceType } from '@/constants/workspaces';
import { WorkspaceUsers } from '@/db/schema';

export async function up(knex: Knex): Promise<void> {
  // alter table to add a field created_by;
  await knex.schema.alterTable('workspaces', (t) => {
    t.uuid('created_by')
      .references('id')
      .inTable('users')
      .onDelete('CASCADE')
      .index('index_workspaces_on_created_by');

    t.uuid('subtype_id')
      .references('id')
      .inTable('subtypes')
      .onDelete('CASCADE')
      .index('index_workspaces_on_subtype_id');
  });

  const workspaces = await knex('workspaces').select('id', 'type', 'label');

  const surgeonSubtype = await knex('subtypes')
    .select('id')
    .where('label', SUBTYPES.CardiacSurgeon.SeniorSurgeons.Specialist.label)
    .first();

  const professionalSubtype = await knex('subtypes')
    .select('id')
    .where('label', SUBTYPES.Professionals.Perfusionist.label)
    .first();

  for (const workspace of workspaces) {
    if (workspace.label) continue;

    let label = '';
    let created_by = '';
    let subtype_id = '';

    switch (workspace.type) {
      case WorkspaceType.ORGANISATION:
        const organisation = await knex('organisations')
          .select('name', 'subtype_id')
          .where('workspace_id', workspace.id)
          .first();

        const createdUser = await knex<WorkspaceUsers>('workspace_users')
          .select('users.id')
          .leftJoin('users', 'users.id', 'workspace_users.user_id')
          .where('workspace_id', workspace.id)
          .first();

        label = organisation?.name ?? '';
        created_by = createdUser.id;
        subtype_id = organisation.subtype_id;
        break;
      case WorkspaceType.SURGEON:
        const surgeons = await knex<WorkspaceUsers>('workspace_users')
          .select('users.id', 'users.first_name', 'users.middle_name', 'users.last_name')
          .leftJoin('users', 'users.id', 'workspace_users.user_id')
          .where('workspace_id', workspace.id)
          .first();
        label =
          surgeons?.first_name +
          ' ' +
          (surgeons?.middle_name ? surgeons?.middle_name + ' ' : '') +
          surgeons?.last_name;
        created_by = surgeons.id;
        subtype_id = surgeonSubtype.id;
        break;
      case WorkspaceType.PROFESSIONAL:
        const professionals = await knex<WorkspaceUsers>('workspace_users')
          .select('users.id', 'users.first_name', 'users.middle_name', 'users.last_name')
          .leftJoin('users', 'users.id', 'workspace_users.user_id')
          .where('workspace_id', workspace.id)
          .first();
        label =
          professionals?.first_name +
          ' ' +
          (professionals?.middle_name ? professionals?.middle_name + ' ' : '') +
          professionals?.last_name;
        created_by = professionals.id;
        subtype_id = professionalSubtype.id;
        break;
    }

    await knex('workspaces').where('id', '=', workspace.id).update({
      label,
      created_by,
      subtype_id,
    });
  }

  await knex.schema.alterTable('workspaces', (t) => {
    t.text('label').notNullable().alter();
    t.uuid('created_by').notNullable().alter();
    t.uuid('subtype_id').notNullable().alter();
  });

  await knex.schema.alterTable('organisations', (t) => {
    t.dropColumn('name');
    t.dropColumn('subtype_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('organisations', (t) => {
    t.text('name').notNullable().defaultTo('');
    t.uuid('subtype_id')
      .references('id')
      .inTable('subtypes')
      .onDelete('CASCADE')
      .index('index_organisations_on_subtype_id');
  });

  const organisationWorkspaces = await knex('workspaces')
    .select('id', 'subtype_id', 'label')
    .where('type', '=', WorkspaceType.ORGANISATION);

  for (const orgWorkspace of organisationWorkspaces) {
    const { id, subtype_id, label } = orgWorkspace;

    await knex('organisations').where('workspace_id', '=', id).update({
      name: label,
      subtype_id,
    });
  }

  await knex.schema.alterTable('workspaces', (t) => {
    t.text('label').nullable().alter();

    t.dropColumn('created_by');
    t.dropColumn('subtype_id');
  });

  await knex('workspaces').update({
    label: null,
  });
}
