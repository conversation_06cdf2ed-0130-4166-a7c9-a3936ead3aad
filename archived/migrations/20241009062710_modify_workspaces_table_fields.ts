import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspaces', (table) => {
    table.text('type').notNullable().defaultTo('organisation').alter();

    table.text('label');

    table.dropColumn('name');
    table.dropColumn('hq_location');
    table.dropColumn('registration_number');
    table.dropColumn('country_id');
    table.dropColumn('email');
    table.dropColumn('website');
    table.dropColumn('phone_number');
    table.dropColumn('zip_code');
    table.dropColumn('about_us');
    table.dropColumn('instagram_link');
    table.dropColumn('facebook_link');
    table.dropColumn('twitter_link');
    table.dropColumn('linkedin_link');
    table.dropColumn('youtube_link');
    table.dropColumn('tiktok_link');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspaces', (table) => {
    table.text('type').nullable().alter();

    table.dropColumn('label');

    table.text('name').notNullable().defaultTo('');
    table.text('hq_location');
    table.text('registration_number');
    table
      .uuid('country_id')
      .references('id')
      .inTable('countries')
      .onDelete('CASCADE')
      .index('index_workspaces_on_country_id');
    table.text('email').unique();
    table.text('website');
    table.text('phone_number');
    table.text('zip_code');
    table.text('about_us');
    table.text('instagram_link');
    table.text('facebook_link');
    table.text('twitter_link');
    table.text('linkedin_link');
    table.text('youtube_link');
    table.text('tiktok_link');
  });
}
