import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('organisation_users', (t) => {
      t.primary(['user_id', 'organisation_id']);
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_organisation_users_on_user_id');
      t.uuid('organisation_id')
        .notNullable()
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_organisation_users_on_organisation_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER organisation_users_updated_at BEFORE UPDATE
ON organisation_users FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('organisation_users');
}
