import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.dropTable('users_followers');
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('users_followers', (t) => {
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_users_followers_on_user_id');
      t.uuid('following_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_users_followers_on_following_id');
      t.text('profile_type').notNullable().defaultTo('user');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').notNullable().defaultTo(1);
      t.primary(['user_id', 'following_id']);
    })
    .raw(
      `
CREATE TRIGGER users_followers_updated_at BEFORE UPDATE
ON users_followers FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}
