import { EntityType } from '@/constants/user-types';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_followers', async (t) => {
    t.dropIndex('user_id', 'index_workspaces_followers_on_user_id');
    // Drop the foreign key with its original name
    t.dropForeign('user_id', 'organisation_followers_user_id_foreign');
    // Continue with the rest of your migration
    t.renameColumn('user_id', 'entity_id');
    t.text('entity_type').notNullable().defaultTo(EntityType.USER);
    t.index('entity_id', 'index_workspaces_followers_on_entity_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_followers', async (t) => {
    // Check if the entity_id index exists before trying to drop it
    t.dropIndex('entity_id', 'index_workspaces_followers_on_entity_id');
    // Drop entity_type column
    t.dropColumn('entity_type');
    // Rename entity_id back to user_id
    t.renameColumn('entity_id', 'user_id');
    // Create index on user_id
    t.index('user_id', 'index_workspaces_followers_on_user_id');
    // Re-create the foreign key with the existed name
    t.foreign('user_id', 'organisation_followers_user_id_foreign')
      .references('id')
      .inTable('users')
      .onDelete('CASCADE');
  });
}

// FIXME: ACTUALLY SOME CHANGES THAT WE MADE WHILE CHANGE ORGANISTION TABLE TO WORKSPACE TABLE IS NOT APPLIED, OR FORGOTTEN, HAS TO MANAGE IT LATER
