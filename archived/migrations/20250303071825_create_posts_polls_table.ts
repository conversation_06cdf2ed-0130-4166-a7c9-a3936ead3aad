import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('posts_polls', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));

    t.uuid('post_id')
      .notNullable()
      .references('id')
      .inTable('posts')
      .onDelete('CASCADE')
      .index('index_posts_polls_on_post_id');

    t.text('option').notNullable();

    t.uuid('voted_by')
      .notNullable()
      .references('id')
      .inTable('users')
      .onDelete('CASCADE')
      .index('index_posts_polls_on_voted_by');

    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.integer('status').defaultTo(1);
  }).raw(`
    CREATE TRIGGER posts_polls_updated_at BEFORE UPDATE ON posts_polls FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();`);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('posts_polls');
}
