import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('post_likes', (t) => {
      t.primary(['post_id', 'user_id']);
      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_likes_on_post_id');
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_post_likes_on_user_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER post_likes_updated_at BEFORE UPDATE
ON post_likes FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('post_likes');
}
