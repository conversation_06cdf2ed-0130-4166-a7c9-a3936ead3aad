import type { Knex } from 'knex';
import { getAuth } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';
import { Roles } from '@/constants/user-types';

const superAdminEmail = '<EMAIL>';

const envConfig = envConfiguration();
if (!getApps().length) {
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  try {
    const userRecord = await getAuth().getUserByEmail(superAdminEmail);
    const originalClaims = userRecord.customClaims || {};

    const superAdminRole = await knex('roles').where('key', Roles.SUPER_ADMIN).select('id').first();
    const superAdminPerms = await getPermissionsForRoles(knex, superAdminRole.id);

    // Update to new claims structure
    const updatedClaims: any = {
      ...originalClaims,
      role: Roles.SUPER_ADMIN,
      permissions: superAdminPerms,
      workspaceId: null,
    };
    delete updatedClaims.workspaceRole;
    delete updatedClaims.workspacePermissions;
    delete updatedClaims.globalRole;
    delete updatedClaims.globalPermissions;

    // Set claims
    await getAuth().setCustomUserClaims(userRecord.uid, updatedClaims);
  } catch (error) {
    console.log('🚀 ~ up ~ error:', error);
  }
}

export async function down(): Promise<void> {
  // no going back
}

async function getPermissionsForRoles(knex: Knex, userRoleId: string) {
  const permissions = await knex('user_permissions')
    .where('user_role_id', userRoleId)
    .where('user_permissions.status', 1)
    .leftJoin('permissions', 'user_permissions.permission_id', 'permissions.id')
    .select('permissions.key');

  return permissions.map((p) => p.key);
}
