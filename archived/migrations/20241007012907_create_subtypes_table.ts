import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subtypes', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('label').notNullable();
      t.uuid('parent_id')
        .references('id')
        .inTable('subtypes')
        .onDelete('CASCADE')
        .index('index_subtypes_on_parent_id');
      t.text('account_type').notNullable();
      t.integer('status').notNullable().defaultTo(1);
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subtypes_updated_at BEFORE UPDATE
ON subtypes FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subtypes');
}
