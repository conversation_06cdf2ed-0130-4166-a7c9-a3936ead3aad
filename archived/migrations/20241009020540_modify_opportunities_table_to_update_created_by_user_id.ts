import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('opportunities', (table) => {
    table.dropIndex('createdByUserId', 'index_opportunities_on_user_id');
    table.renameColumn('createdByUserId', 'created_by_user_id');
    table.index('created_by_user_id', 'index_opportunities_on_created_by_user_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('opportunities', (table) => {
    table.dropIndex('created_by_user_id', 'index_opportunities_on_created_by_user_id');
    table.renameColumn('created_by_user_id', 'createdByUserId');
    table.index('createdByUserId', 'index_opportunities_on_user_id');
  });
}
