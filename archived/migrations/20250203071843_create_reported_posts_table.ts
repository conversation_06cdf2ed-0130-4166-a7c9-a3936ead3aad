import { Knex } from 'knex';

import { ReportedPostStatus } from '@/constants/posts';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('reported_posts', (t) => {
      t.primary(['reporter_id', 'post_id']);
      t.uuid('reporter_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_reported_posts_on_reporter_id');

      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_reported_posts_on_post_id');

      t.text('reported_reason').notNullable();

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').defaultTo(ReportedPostStatus.ACTIVE);
    })
    .raw(
      `
CREATE TRIGGER reported_posts_updated_at BEFORE UPDATE
ON reported_posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('reported_posts');
}
