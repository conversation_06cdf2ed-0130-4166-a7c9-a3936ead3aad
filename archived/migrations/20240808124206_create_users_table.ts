import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('users', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('name').notNullable();
      t.text('email').unique().notNullable();
      t.text('provider_id');
      t.boolean('is_live').notNullable().defaultTo(false);
      t.boolean('active').notNullable().defaultTo(true);
      t.boolean('archived').notNullable().defaultTo(false);
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER users_updated_at BEFORE UPDATE
ON users FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('users');
}
