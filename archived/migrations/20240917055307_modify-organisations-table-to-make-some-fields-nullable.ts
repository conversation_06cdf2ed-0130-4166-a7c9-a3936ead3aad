import type { Knex } from 'knex';

const ORGANISATIONS_TABLE_NAME = 'organisations';
const COUNTRY_TABLE_NAME = 'countries';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(ORGANISATIONS_TABLE_NAME, async (table) => {
    table.text('hq_location').nullable().alter();
    table.text('registration_number').nullable().alter();
    table.uuid('country_id').nullable().alter();
    table.text('email').nullable().alter();
    table.text('website').nullable().alter();
    table.text('phone_number').nullable().alter();
    table.text('zip_code').nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  // Fetch the first country_id before altering the table
  const firstCountryId = await getFirstCountryId(knex);

  // Update all columns that have NULL values with default values
  await knex(ORGANISATIONS_TABLE_NAME).update({
    hq_location: knex.raw(`COALESCE(hq_location, '')`),
    registration_number: knex.raw(`COALESCE(registration_number, '')`),
    country_id: knex.raw(`COALESCE(country_id, ?)`, [firstCountryId]),
    website: knex.raw(`COALESCE(website, '')`),
    phone_number: knex.raw(`COALESCE(phone_number, '')`),
    zip_code: knex.raw(`COALESCE(zip_code, '')`),
  });

  await knex.schema.alterTable(ORGANISATIONS_TABLE_NAME, async (table) => {
    table.text('hq_location').defaultTo('').notNullable().alter();
    table.text('registration_number').defaultTo('').notNullable().alter();
    table.uuid('country_id').notNullable().alter();
    table.text('website').defaultTo('').notNullable().alter();
    table.text('phone_number').defaultTo('').notNullable().alter();
    table.text('zip_code').defaultTo('').notNullable().alter();
  });
}

async function getFirstCountryId(knex: Knex): Promise<string> {
  const result = await knex(COUNTRY_TABLE_NAME).select('id').first();
  return result ? result.id : null;
}
