import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('opportunity_applications', (t) => {
      t.primary(['user_id', 'opportunity_id']);
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_opportunity_applications_on_user_id');
      t.uuid('opportunity_id')
        .notNullable()
        .references('id')
        .inTable('opportunities')
        .onDelete('CASCADE')
        .index('index_opportunity_applications_on_opportunity_id');

      t.string('name').notNullable();
      t.string('phone_number').notNullable();
      t.string('email').notNullable();

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').defaultTo(1);
    })
    .raw(
      `CREATE TRIGGER opportunity_applications_updated_at 
       BEFORE UPDATE ON opportunity_applications 
       FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('opportunity_applications');
}
