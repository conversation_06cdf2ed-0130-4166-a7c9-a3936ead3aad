import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('post_views', (t) => {
    t.dropColumn('id');

    t.primary(['post_id', 'user_id']);
    t.integer('view_count').notNullable().defaultTo(1);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('post_views', (t) => {
    t.dropPrimary();
    t.dropColumn('view_count');

    t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
  });
}
