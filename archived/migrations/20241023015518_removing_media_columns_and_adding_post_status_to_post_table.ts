import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('posts', (t) => {
    t.dropColumn('active');
    t.dropColumn('archived');

    t.string('post_status').notNullable().defaultTo('draft');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('posts', (t) => {
    t.boolean('active').notNullable().defaultTo(true);
    t.boolean('archived').notNullable().defaultTo(false);

    t.dropColumn('post_status');
  });
}
