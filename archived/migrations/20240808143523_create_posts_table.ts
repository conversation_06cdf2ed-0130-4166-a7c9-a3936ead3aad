import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('posts', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('published_by')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_posts_on_published_by');
      t.uuid('updated_by')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_posts_on_updated_by');
      t.text('content');
      t.uuid('organisation_id')
        .notNullable()
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_posts_on_organisation_id');
      t.text('media_path');
      t.text('media_type');
      t.uuid('category_id')
        .notNullable()
        .references('id')
        .inTable('categories')
        .onDelete('CASCADE')
        .index('index_posts_on_category_id');
      t.boolean('active').notNullable().defaultTo(true);
      t.boolean('archived').notNullable().defaultTo(false);
      t.date('post_schedule_date').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER posts_updated_at BEFORE UPDATE
ON posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('posts');
}
