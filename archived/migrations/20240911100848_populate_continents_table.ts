import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex('continents').insert([
    { name: 'Africa' },
    { name: 'Antarctica' },
    { name: 'Asia' },
    { name: 'Europe' },
    { name: 'North America' },
    { name: 'Oceania' },
    { name: 'South America' },
  ]);
}

export async function down(knex: Knex): Promise<void> {
  await knex('continents').del();
}
