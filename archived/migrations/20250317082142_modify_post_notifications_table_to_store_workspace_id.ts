import type { K<PERSON> } from 'knex';

import { EntityType } from '@/constants/user-types';

export async function up(knex: Knex): Promise<void> {
  // Use a transaction for atomicity
  await knex.transaction(async (trx) => {
    // Update post_notifications table schema
    await trx.schema.alterTable('post_notifications', (t) => {
      t.dropIndex([], 'index_post_notifications_on_actor_id');
      t.dropForeign(['actor_id'], 'post_notifications_actor_id_foreign');
      t.renameColumn('actor_id', 'entity_id');
      t.text('entity_type').notNullable().defaultTo(EntityType.USER);
      t.index(['entity_id'], 'index_post_notifications_on_entity_id');
    });

    // Get all notifications
    const postNotifications = await trx('post_notifications').select('id', 'entity_id');

    // Only update those that are workspaces
    for (const notification of postNotifications) {
      const workspaceExists = await trx('workspaces').where('id', notification.entity_id).first();

      if (workspaceExists) {
        await trx('post_notifications')
          .where('id', notification.id)
          .update({ entity_type: EntityType.WORKSPACE });
      }
    }
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    await trx.schema.alterTable('post_notifications', (t) => {
      t.dropIndex([], 'index_post_notifications_on_entity_id');
      t.dropColumn('entity_type');
      t.renameColumn('entity_id', 'actor_id');
      t.foreign('actor_id').references('id').inTable('users').onDelete('CASCADE');
      t.index('actor_id', 'index_post_notifications_on_actor_id');
    });
  });
}
