import type { Knex } from 'knex';

interface Category {
  label: string;
  children?: Category[];
}

// Define the structure of the seed data
interface SeedData {
  account_type: string;
  categories: Category[];
}

const seedData: SeedData[] = [
  // Professional
  {
    account_type: 'professional',
    categories: [
      {
        label: 'Perfusionist',
        children: [],
      },
      {
        label: 'Researcher',
        children: [],
      },
    ],
  },
];

export async function up(knex: Knex): Promise<void> {
  async function insertCategories(categories: Category[], parentId = null, accountType: string) {
    for (const category of categories) {
      const [{ id: subtypeId }] = await knex('subtypes')
        .insert({
          label: category.label,
          parent_id: parentId,
          account_type: accountType,
        })
        .returning('id');

      if (category.children && category.children.length > 0) {
        await insertCategories(category.children, subtypeId, accountType);
      }
    }
  }

  for (const account of seedData) {
    await insertCategories(account.categories, null, account.account_type);
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('subtypes').where({ account_type: 'professional' }).del();
}
