import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('user_roles', (t) => {
    t.uuid('organisation_id')
      .references('id')
      .inTable('organisations')
      .onDelete('CASCADE')
      .index('index_user_roles_on_organisation_id')
      .nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('user_roles', (t) => {
    t.dropColumn('organisation_id');
  });
}
