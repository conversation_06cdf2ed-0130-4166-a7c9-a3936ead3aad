import type { Knex } from 'knex';

const TAGS = [
  'Heart Health',
  'Cardiac Surgery',
  'Heart Disease Prevention',
  'Cardiomyopathy',
  'Hypertension',
  'Arrhythmia',
  'Heart Failure',
  'Cardiac Rehabilitation',
  'Nutrition for Heart Health',
  'Exercise & Fitness for Heart Patients',
];

export async function up(knex: Knex): Promise<void> {
  await knex('tags').insert(TAGS.map((label) => ({ label })));
}

export async function down(knex: Knex): Promise<void> {
  await knex('tags').whereIn('label', TAGS).del();
}
