import type { K<PERSON> } from 'knex';

import { EntityType } from '@/constants/user-types';

export async function up(knex: Knex): Promise<void> {
  // Use a transaction for atomicity
  await knex.transaction(async (trx) => {
    // Update followers_notification table schema
    await trx.schema.alterTable('followers_notification', (t) => {
      t.dropIndex([], 'index_followers_notification_on_follower_id');
      t.dropForeign(['follower_id'], 'followers_notification_follower_id_foreign');
      t.renameColumn('follower_id', 'entity_id');
      t.text('entity_type').notNullable().defaultTo(EntityType.USER);
      t.index(['entity_id'], 'index_followers_notifications_on_entity_id');
    });

    // Get all notifications
    const followerNotifications = await trx('followers_notification').select('id', 'entity_id');

    // Update those that are workspaces
    for (const notification of followerNotifications) {
      const workspaceExists = await trx('workspaces').where('id', notification.entity_id).first();

      if (workspaceExists) {
        await trx('followers_notification')
          .where('id', notification.id)
          .update({ entity_type: EntityType.WORKSPACE });
      }
      // If not found, it will retain the default USER type
    }
  });
}

export async function down(knex: Knex): Promise<void> {
  // Use a transaction for the down migration as well
  await knex.transaction(async (trx) => {
    // Reverting changes to followers_notification table
    await trx.schema.alterTable('followers_notification', (t) => {
      t.dropIndex([], 'index_followers_notifications_on_entity_id');
      t.dropColumn('entity_type');
      t.renameColumn('entity_id', 'follower_id');
      t.foreign('follower_id').references('id').inTable('users').onDelete('CASCADE');
      t.index('follower_id', 'index_followers_notification_on_follower_id');
    });
  });
}
