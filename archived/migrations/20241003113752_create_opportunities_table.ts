import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('opportunities', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
    t.uuid('organisation_id')
      .notNullable()
      .references('id')
      .inTable('organisations')
      .onDelete('CASCADE')
      .index('index_opportunities_on_organisation_id');
    t.uuid('createdByUserId')
      .notNullable()
      .references('id')
      .inTable('users')
      .onDelete('CASCADE')
      .index('index_opportunities_on_user_id');
    t.text('type').notNullable();
    t.text('title').notNullable();
    t.text('description').notNullable();
    t.text('short_description').notNullable();
    t.text('contact_person_name').notNullable();
    t.text('email').notNullable();
    t.text('phone').notNullable();
    t.text('website_link').notNullable();
    t.timestamp('expiry_date').notNullable();
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.integer('status').defaultTo(1);
  }).raw(`
    CREATE TRIGGER opportunities_updated_at BEFORE UPDATE
    ON opportunities FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
  `);
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('opportunities');
}
