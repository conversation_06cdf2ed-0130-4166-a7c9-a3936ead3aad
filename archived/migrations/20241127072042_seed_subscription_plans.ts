import type { <PERSON><PERSON> } from 'knex';

import { BillingCycle } from '@/constants/subscriptions';
import { WorkspaceType } from '@/constants/workspaces';

// Define the structure of the seed data

type Subscriptions = {
  title: string;
  workspace_type: WorkspaceType;
  billing_cycle: BillingCycle;
  max_duration_months: number | null;
  posts_count: number;
  articles_count: number;
  opportunities_count: number;
  connects_count: number;
  cold_reach_count: number;
  team_count: number;
  amount: number;
  gallery_count: number;
};

const seedData: Subscriptions[] = [
  // Organisation monthly
  {
    title: 'basic',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 300,
    posts_count: 10,
    articles_count: 2,
    opportunities_count: 2,
    connects_count: 50,
    cold_reach_count: 0,
    team_count: 5,
    gallery_count: 30,
  },
  {
    title: 'standard',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 600,
    posts_count: 20,
    articles_count: 10,
    opportunities_count: 5,
    connects_count: 100,
    cold_reach_count: 10,
    team_count: 10,
    gallery_count: 50,
  },
  {
    title: 'enterprise',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 900,
    posts_count: 30,
    articles_count: 15,
    opportunities_count: 10,
    connects_count: 150,
    cold_reach_count: 20,
    team_count: 20,
    gallery_count: 100,
  },

  // Organisation yearly
  {
    title: 'basic',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 300,
    posts_count: 10,
    articles_count: 2,
    opportunities_count: 2,
    connects_count: 50,
    cold_reach_count: 0,
    team_count: 5,
    gallery_count: 30,
  },
  {
    title: 'standard',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 600,
    posts_count: 20,
    articles_count: 10,
    opportunities_count: 5,
    connects_count: 100,
    cold_reach_count: 10,
    team_count: 10,
    gallery_count: 50,
  },
  {
    title: 'enterprise',
    workspace_type: WorkspaceType.ORGANISATION,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 900,
    posts_count: 30,
    articles_count: 15,
    opportunities_count: 10,
    connects_count: 150,
    cold_reach_count: 20,
    team_count: 20,
    gallery_count: 100,
  },

  // surgeon monthly
  {
    title: 'basic',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 300,
    posts_count: 5,
    articles_count: 2,
    opportunities_count: 1,
    connects_count: 10,
    cold_reach_count: 0,
    team_count: 2,
    gallery_count: 15,
  },
  {
    title: 'premium',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 600,
    posts_count: 10,
    articles_count: 5,
    opportunities_count: 2,
    connects_count: 20,
    cold_reach_count: 5,
    team_count: 5,
    gallery_count: 30,
  },
  {
    title: 'prestige',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 900,
    posts_count: 20,
    articles_count: 10,
    opportunities_count: 5,
    connects_count: 30,
    cold_reach_count: 10,
    team_count: 10,
    gallery_count: 50,
  },

  // surgeon yearly
  {
    title: 'basic',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 300,
    posts_count: 5,
    articles_count: 2,
    opportunities_count: 1,
    connects_count: 10,
    cold_reach_count: 0,
    team_count: 2,
    gallery_count: 15,
  },
  {
    title: 'premium',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 600,
    posts_count: 10,
    articles_count: 5,
    opportunities_count: 2,
    connects_count: 20,
    cold_reach_count: 5,
    team_count: 5,
    gallery_count: 30,
  },
  {
    title: 'prestige',
    workspace_type: WorkspaceType.SURGEON,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 900,
    posts_count: 20,
    articles_count: 10,
    opportunities_count: 5,
    connects_count: 30,
    cold_reach_count: 10,
    team_count: 10,
    gallery_count: 50,
  },

  // professional monthly
  {
    title: 'basic',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 300,
    posts_count: 3,
    articles_count: 1,
    opportunities_count: 0,
    connects_count: 10,
    cold_reach_count: 0,
    team_count: 1,
    gallery_count: 10,
  },
  {
    title: 'standard',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 600,
    posts_count: 5,
    articles_count: 2,
    opportunities_count: 1,
    connects_count: 20,
    cold_reach_count: 0,
    team_count: 2,
    gallery_count: 15,
  },
  {
    title: 'advanced',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.MONTHLY,
    max_duration_months: 1,
    amount: 900,
    posts_count: 10,
    articles_count: 5,
    opportunities_count: 2,
    connects_count: 30,
    cold_reach_count: 5,
    team_count: 5,
    gallery_count: 20,
  },

  // professional yearly
  {
    title: 'basic',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 300,
    posts_count: 3,
    articles_count: 1,
    opportunities_count: 0,
    connects_count: 10,
    cold_reach_count: 0,
    team_count: 1,
    gallery_count: 10,
  },
  {
    title: 'standard',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 600,
    posts_count: 5,
    articles_count: 2,
    opportunities_count: 1,
    connects_count: 20,
    cold_reach_count: 0,
    team_count: 2,
    gallery_count: 15,
  },
  {
    title: 'advanced',
    workspace_type: WorkspaceType.PROFESSIONAL,
    billing_cycle: BillingCycle.YEARLY,
    max_duration_months: 12,
    amount: 900,
    posts_count: 10,
    articles_count: 5,
    opportunities_count: 2,
    connects_count: 30,
    cold_reach_count: 5,
    team_count: 5,
    gallery_count: 20,
  },
];

export async function up(knex: Knex): Promise<void> {
  await knex('subscriptions').insert(seedData);
}

export async function down(knex: Knex): Promise<void> {
  await knex('subscriptions').del();
}
