import type { Knex } from 'knex';
import { getAuth } from 'firebase-admin/auth';
import { cert, getApps, initializeApp } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';

import { Roles } from '@/constants/user-types';

interface UserClaimsTracker {
  userId: string;
  providerId: string;
  originalClaims: any;
}

if (!getApps().length) {
  const envConfig = envConfiguration();
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  const users = await knex('users')
    .whereNotNull('provider_id')
    .whereRaw("provider_id <> ''")
    .select('id', 'provider_id')
    .orderBy('created_at', 'asc');

  // Only track successfully updated users
  const updatedUsers: UserClaimsTracker[] = [];

  for (const user of users) {
    try {
      const userRecord = await getAuth().getUser(user.provider_id);
      const originalClaims = userRecord.customClaims || {};
      const existingClaims = { ...originalClaims };

      const userRoleDetails = await knex('user_roles')
        .where('user_id', user.id)
        .where('user_roles.active', true)
        .where('user_roles.status', 1)
        .leftJoin('roles', 'user_roles.role_id', 'roles.id')
        .select('roles.key', 'user_roles.workspace_id', 'user_roles.id');

      let globalRoleDetails = userRoleDetails?.find((gRole) =>
        [Roles.GENERAL_PUBLIC, Roles.SYSTEM_ADMIN, Roles.SUPER_ADMIN, Roles.OWNER].includes(
          gRole.key,
        ),
      );

      if (!globalRoleDetails) {
        const generalPublicRole = await knex('roles')
          .where('key', Roles.GENERAL_PUBLIC)
          .select('id')
          .first();

        globalRoleDetails = {
          key: Roles.GENERAL_PUBLIC,
          id: generalPublicRole.id,
        };

        // 2. Create new user role entry
        const [newUserRole] = await knex('user_roles')
          .insert({
            user_id: user.id,
            role_id: generalPublicRole.id,
          })
          .returning('id');

        // 3. Get default permissions for General Public role
        const defaultPermissions = await knex('role_permissions')
          .where('role_id', generalPublicRole.id)
          .where('status', 1)
          .select('permission_id');

        // 4. Create user permissions entries
        if (defaultPermissions.length > 0) {
          const userPermissionsData = defaultPermissions.map(({ permission_id }) => ({
            user_role_id: newUserRole.id,
            permission_id,
          }));

          await knex('user_permissions').insert(userPermissionsData);
        }
      }

      const globalPerms = await getPermissionsForRoles(knex, globalRoleDetails.id);

      // Update to new claims structure
      const updatedClaims: any = {
        ...existingClaims,
        role: globalRoleDetails.key,
        permissions: globalPerms,
        workspaceId: null,
      };

      delete updatedClaims.workspaceRole;
      delete updatedClaims.workspacePermissions;
      delete updatedClaims.globalRole;
      delete updatedClaims.globalPermissions;

      // 8. Set claims with error handling
      try {
        await getAuth().setCustomUserClaims(user.provider_id, updatedClaims);
      } catch (error) {
        console.error(`Error setting claims for user ${user.id}:`, error);
        continue;
      }

      // Only track after successful update
      updatedUsers.push({
        userId: user.id,
        providerId: user.provider_id,
        originalClaims,
      });
    } catch (error) {
      // Log the error
      console.error(`Error processing user ${user.id}:`, error);

      // Immediately start rollback for any successful updates
      if (updatedUsers.length > 0) {
        await rollbackChanges(updatedUsers);
      }

      // Throw error to stop the migration
      throw new Error(
        `Migration failed at user ${user.id} - previous changes have been rolled back`,
      );
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  const users = await knex('users')
    .whereNotNull('provider_id')
    .whereRaw("provider_id <> ''")
    .select('id', 'provider_id')
    .orderBy('created_at', 'asc');

  // Only track successfully updated users
  const updatedUsers: UserClaimsTracker[] = [];

  for (const user of users) {
    try {
      const userRecord = await getAuth().getUser(user.provider_id);
      const originalClaims = userRecord.customClaims || {};
      const existingClaims = { ...originalClaims };

      const userRoleDetails = await knex('user_roles')
        .where('user_id', user.id)
        .where('user_roles.active', true)
        .where('user_roles.status', 1)
        .leftJoin('roles', 'user_roles.role_id', 'roles.id')
        .select('roles.key', 'user_roles.workspace_id', 'user_roles.id');

      let globalRoleDetails = userRoleDetails?.find((gRole) =>
        [Roles.GENERAL_PUBLIC, Roles.SYSTEM_ADMIN, Roles.SUPER_ADMIN, Roles.OWNER].includes(
          gRole.key,
        ),
      );

      if (!globalRoleDetails) {
        const generalPublicRole = await knex('roles')
          .where('key', Roles.GENERAL_PUBLIC)
          .select('id')
          .first();

        globalRoleDetails = {
          key: Roles.GENERAL_PUBLIC,
          id: generalPublicRole.id,
        };

        // 2. Create new user role entry
        const [newUserRole] = await knex('user_roles')
          .insert({
            user_id: user.id,
            role_id: generalPublicRole.id,
          })
          .returning('id');

        // 3. Get default permissions for General Public role
        const defaultPermissions = await knex('role_permissions')
          .where('role_id', generalPublicRole.id)
          .where('status', 1)
          .select('permission_id');

        // 4. Create user permissions entries
        if (defaultPermissions.length > 0) {
          const userPermissionsData = defaultPermissions.map(({ permission_id }) => ({
            user_role_id: newUserRole.id,
            permission_id,
          }));

          await knex('user_permissions').insert(userPermissionsData);
        }
      }

      const workspaceRoleDetails = userRoleDetails?.find((wRole) =>
        [
          Roles.ORGANISATION_ADMIN,
          Roles.ORGANISATION_USER,
          Roles.SURGEON_ADMIN,
          Roles.SURGEON_USER,
          Roles.PROFESSIONAL_ADMIN,
          Roles.PROFESSIONAL_USER,
        ].includes(wRole.key),
      );

      // Get permissions with error handling
      let workspacePerms = [];

      let globalPerms = [];
      try {
        globalPerms = await getPermissionsForRoles(knex, globalRoleDetails.id);
        if (workspaceRoleDetails) {
          workspacePerms = await getPermissionsForRoles(knex, workspaceRoleDetails.id);
        }
      } catch (error) {
        console.error(`Error fetching permissions for user ${user.id}:`, error);
        continue;
      }

      const revertedClaims = {
        ...existingClaims,
        globalRole: globalRoleDetails.key,
        globalPermissions: globalPerms,
        workspaceId: workspaceRoleDetails?.workspace_id,
        workspaceRole: workspaceRoleDetails?.key, //update with claims
        workspacePermissions: workspacePerms,
      };

      // 8. Set claims with error handling
      try {
        await getAuth().setCustomUserClaims(user.provider_id, revertedClaims);
      } catch (error) {
        console.error(`Error setting claims for user ${user.id}:`, error);
        continue;
      }

      // Only track after successful update
      updatedUsers.push({
        userId: user.id,
        providerId: user.provider_id,
        originalClaims,
      });
    } catch (error) {
      // Log the error
      console.error(`Error processing user ${user.id}:`, error);

      // Immediately start rollback for any successful updates
      if (updatedUsers.length > 0) {
        console.log('Starting immediate rollback due to error...');
        await rollbackChanges(updatedUsers);
      }

      // Throw error to stop the migration
      throw new Error(
        `Migration failed at user ${user.id} - previous changes have been rolled back`,
      );
    }
  }
}

async function rollbackChanges(updatedUsers: UserClaimsTracker[]): Promise<void> {
  for (const user of updatedUsers) {
    try {
      await getAuth().setCustomUserClaims(user.providerId, user.originalClaims);
      console.log(`Successfully rolled back changes for user ${user.userId}`);
    } catch (error) {
      console.error(`CRITICAL: Failed to rollback changes for user ${user.userId}:`, error);
      console.error('Original claims:', user.originalClaims);
      // Continue trying to rollback other users
    }
  }
}

async function getPermissionsForRoles(knex: Knex, userRoleId: string) {
  const permissions = await knex('user_permissions')
    .where('user_role_id', userRoleId)
    .where('user_permissions.status', 1)
    .leftJoin('permissions', 'user_permissions.permission_id', 'permissions.id')
    .select('permissions.key');

  return permissions.map((p) => p.key);
}
