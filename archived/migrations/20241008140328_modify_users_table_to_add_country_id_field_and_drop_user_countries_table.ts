import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.dropTable('user_countries');

  return knex.schema.alterTable('users', (t) => {
    t.uuid('country_id')
      .nullable()
      .references('id')
      .inTable('countries')
      .onDelete('CASCADE')
      .index('index_users_on_country_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema
    .alterTable('users', (t) => {
      t.dropColumn('country_id');
    })
    .then(() => {
      return knex.schema
        .createTable('user_countries', (t) => {
          t.primary(['user_id', 'country_id']);
          t.uuid('user_id')
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE')
            .index('index_user_countries_on_user_id');
          t.uuid('country_id')
            .notNullable()
            .references('id')
            .inTable('countries')
            .onDelete('CASCADE')
            .index('index_user_countries_on_country_id');
          t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
          t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
          t.integer('status').notNullable().defaultTo(1);
        })
        .raw(
          `
    CREATE TRIGGER user_countries_updated_at BEFORE UPDATE
    ON user_countries FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
    `,
        );
    });
}
