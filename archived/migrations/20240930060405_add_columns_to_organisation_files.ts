import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisation_files', (t) => {
    t.text('label').notNullable().defaultTo('');
    t.text('section').notNullable().defaultTo('');
    t.text('media_path').unique().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisation_files', (t) => {
    t.dropColumn('label');
    t.dropColumn('section');
    t.text('media_path').alter();
    t.dropUnique(['media_path']);
  });
}
