import type { Knex } from 'knex';

const tables = [
  'modules',
  'doctor_types',
  'permissions',
  'roles',
  'users',
  'user_permissions',
  'user_files',
  'organisations',
  'organisation_files',
  'organisation_followers',
  'continents',
  'countries',
  'user_countries',
  'categories',
  'post_comments',
  'posts',
  'role_permissions',
  'organisation_users',
  'user_categories',
  'subscriptions',
  'subscription_countries',
  'post_countries',
  'post_likes',
  'post_views',
  'user_roles',
];

export async function up(knex: Knex): Promise<void> {
  for (const table of tables) {
    try {
      // Check if the table exists before altering
      const tableExists = await knex.schema.hasTable(table);
      if (tableExists) {
        const columnExists = await knex.schema.hasColumn(table, 'status');
        if (!columnExists) {
          await knex.schema.table(table, (t) => {
            t.integer('status').defaultTo(1);
          });
        }
      }
    } catch (error) {
      console.error(`Error altering table '${table}':`, error);
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  for (const table of tables) {
    try {
      // Check if the table exists before altering
      const tableExists = await knex.schema.hasTable(table);
      if (tableExists) {
        const columnExists = await knex.schema.hasColumn(table, 'status');
        if (columnExists) {
          await knex.schema.table(table, (t) => {
            t.dropColumn('status');
          });
        }
      }
    } catch (error) {
      console.error(`Error rolling back table '${table}':`, error);
    }
  }
}
