import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('post_notifications', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('actor_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_post_notifications_on_actor_id');

      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_notifications_on_post_id');
      t.uuid('comment_id')
        .nullable()
        .references('id')
        .inTable('post_comments')
        .onDelete('CASCADE')
        .index('index_post_notifications_on_comment_id');

      t.text('type').notNullable(); // 'like', 'comment'
      t.boolean('is_read').notNullable().defaultTo(false);

      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').defaultTo(1);
    })
    .raw(
      `
CREATE TRIGGER post_notifications_updated_at BEFORE UPDATE
ON post_notifications FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('post_notifications');
}
