import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('organisations', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('name').notNullable();
      t.uuid('workspace_id')
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_organisations_on_workspace_id')
        .notNullable();
      t.uuid('subtype_id')
        .references('id')
        .inTable('subtypes')
        .onDelete('CASCADE')
        .index('index_organisations_on_subtype_id');
      t.text('hq_location');
      t.text('registration_number');
      t.uuid('country_id')
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_organisations_on_country_id');
      t.text('email').unique();
      t.text('website');
      t.text('phone_number');
      t.text('zip_code');
      t.text('about_us');
      t.text('instagram_link');
      t.text('facebook_link');
      t.text('twitter_link');
      t.text('linkedin_link');
      t.text('youtube_link');
      t.text('tiktok_link');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').notNullable().defaultTo(1);
    })
    .raw(
      `
CREATE TRIGGER organisations_updated_at BEFORE UPDATE
ON organisations FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('organisations');
}
