import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspaces', (table) => {
    table.text('profile_image_url').nullable().unique();
    table.text('profile_image_url_thumbnail').nullable().unique();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspaces', (table) => {
    table.dropColumn('profile_image_url');
    table.dropColumn('profile_image_url_thumbnail');
  });
}
