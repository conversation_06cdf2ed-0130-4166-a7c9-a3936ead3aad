import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('users', (t) => {
    t.uuid('subtype_id')
      .references('id')
      .inTable('subtypes')
      .onDelete('CASCADE')
      .index('index_users_on_subtype_id')
      .nullable();
    t.dropColumn('archived');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('users', (t) => {
    t.dropColumn('subtype_id');
    t.boolean('archived').notNullable().defaultTo(false);
  });
}
