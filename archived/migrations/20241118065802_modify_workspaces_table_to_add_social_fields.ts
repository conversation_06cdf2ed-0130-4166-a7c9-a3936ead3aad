import type { K<PERSON> } from 'knex';

//table names
const WORKSPACES_TABLE = 'workspaces';
const ORGANISATION_TABLE = 'organisations';

//social column names
const INSTAGRAM_LINK_COLUMN = 'instagram_link';
const FACEBOOK_LINK_COLUMN = 'facebook_link';
const TWITTER_LINK_COLUMN = 'twitter_link';
const LINKEDIN_LINK_COLUMN = 'linkedin_link';
const YOUTUBE_LINK_COLUMN = 'youtube_link';
const TIKTOK_LINK_COLUMN = 'tiktok_link';

//other colomn names
const ID_COLUMN = 'id';
const WORKSPACE_ID_COLUMN = 'workspace_id';

export async function up(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // First add the social columns workspaces table
    await trx.schema.alterTable(WORKSPACES_TABLE, (t) => {
      t.text(INSTAGRAM_LINK_COLUMN);
      t.text(FACEBOOK_LINK_COLUMN);
      t.text(TWITTER_LINK_COLUMN);
      t.text(LINKEDIN_LINK_COLUMN);
      t.text(YOUTUBE_LINK_COLUMN);
      t.text(TIKTOK_LINK_COLUMN);
    });

    // Get all organisations with their social links
    const organisationsWithSocials = await trx
      .select(
        ID_COLUMN,
        WORKSPACE_ID_COLUMN,
        INSTAGRAM_LINK_COLUMN,
        FACEBOOK_LINK_COLUMN,
        TWITTER_LINK_COLUMN,
        LINKEDIN_LINK_COLUMN,
        YOUTUBE_LINK_COLUMN,
        TIKTOK_LINK_COLUMN,
      )
      .from(ORGANISATION_TABLE);

    // Move the social links to the workspaces table
    if (organisationsWithSocials.length > 0) {
      for (const organisationWithSocials of organisationsWithSocials) {
        await trx(WORKSPACES_TABLE)
          .where({ [ID_COLUMN]: organisationWithSocials[WORKSPACE_ID_COLUMN] })
          .update({
            [INSTAGRAM_LINK_COLUMN]: organisationWithSocials[INSTAGRAM_LINK_COLUMN],
            [FACEBOOK_LINK_COLUMN]: organisationWithSocials[FACEBOOK_LINK_COLUMN],
            [TWITTER_LINK_COLUMN]: organisationWithSocials[TWITTER_LINK_COLUMN],
            [LINKEDIN_LINK_COLUMN]: organisationWithSocials[LINKEDIN_LINK_COLUMN],
            [YOUTUBE_LINK_COLUMN]: organisationWithSocials[YOUTUBE_LINK_COLUMN],
            [TIKTOK_LINK_COLUMN]: organisationWithSocials[TIKTOK_LINK_COLUMN],
          });
      }
    }

    // Finally, remove the social columns from the organisations table
    await trx.schema.alterTable(ORGANISATION_TABLE, (t) => {
      t.dropColumn(INSTAGRAM_LINK_COLUMN);
      t.dropColumn(FACEBOOK_LINK_COLUMN);
      t.dropColumn(TWITTER_LINK_COLUMN);
      t.dropColumn(LINKEDIN_LINK_COLUMN);
      t.dropColumn(YOUTUBE_LINK_COLUMN);
      t.dropColumn(TIKTOK_LINK_COLUMN);
    });
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.transaction(async (trx) => {
    // First add the social columns back to the organisations table
    await trx.schema.alterTable(ORGANISATION_TABLE, (t) => {
      t.text(INSTAGRAM_LINK_COLUMN);
      t.text(FACEBOOK_LINK_COLUMN);
      t.text(TWITTER_LINK_COLUMN);
      t.text(LINKEDIN_LINK_COLUMN);
      t.text(YOUTUBE_LINK_COLUMN);
      t.text(TIKTOK_LINK_COLUMN);
    });

    // Get all workspaces with their social links
    const workspacesWithSocials = await trx
      .select(
        ID_COLUMN,
        INSTAGRAM_LINK_COLUMN,
        FACEBOOK_LINK_COLUMN,
        TWITTER_LINK_COLUMN,
        LINKEDIN_LINK_COLUMN,
        YOUTUBE_LINK_COLUMN,
        TIKTOK_LINK_COLUMN,
      )
      .from(WORKSPACES_TABLE);

    // Move the social links back to the organisations table
    if (workspacesWithSocials.length > 0) {
      for (const workspaceWithSocials of workspacesWithSocials) {
        await trx(ORGANISATION_TABLE)
          .where({ [WORKSPACE_ID_COLUMN]: workspaceWithSocials[ID_COLUMN] })
          .update({
            [INSTAGRAM_LINK_COLUMN]: workspaceWithSocials[INSTAGRAM_LINK_COLUMN],
            [FACEBOOK_LINK_COLUMN]: workspaceWithSocials[FACEBOOK_LINK_COLUMN],
            [TWITTER_LINK_COLUMN]: workspaceWithSocials[TWITTER_LINK_COLUMN],
            [LINKEDIN_LINK_COLUMN]: workspaceWithSocials[LINKEDIN_LINK_COLUMN],
            [YOUTUBE_LINK_COLUMN]: workspaceWithSocials[YOUTUBE_LINK_COLUMN],
            [TIKTOK_LINK_COLUMN]: workspaceWithSocials[TIKTOK_LINK_COLUMN],
          });
      }
    }

    // Finally, remove the social columns from the workspaces table
    await trx.schema.alterTable(WORKSPACES_TABLE, (t) => {
      t.dropColumn(INSTAGRAM_LINK_COLUMN);
      t.dropColumn(FACEBOOK_LINK_COLUMN);
      t.dropColumn(TWITTER_LINK_COLUMN);
      t.dropColumn(LINKEDIN_LINK_COLUMN);
      t.dropColumn(YOUTUBE_LINK_COLUMN);
      t.dropColumn(TIKTOK_LINK_COLUMN);
    });
  });
}
