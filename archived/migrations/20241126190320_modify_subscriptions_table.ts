import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('subscriptions', (table) => {
    table.text('title').notNullable();
    table.text('workspace_type').notNullable().defaultTo('organisation');

    table.text('billing_cycle').notNullable();
    table.smallint('max_duration_months').nullable();

    table.smallint('posts_count').notNullable();
    table.smallint('articles_count').notNullable();
    table.smallint('opportunities_count').notNullable();
    table.smallint('connects_count').notNullable();
    table.smallint('cold_reach_count').notNullable();
    table.smallint('team_count').notNullable();
    table.smallint('gallery_count').notNullable();

    table.dropColumn('workspace_id');
    table.dropColumn('start_date');
    table.dropColumn('end_date');
    table.dropColumn('payment_date');
    table.dropColumn('payment_method');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('subscriptions', (table) => {
    table.dropColumn('title');
    table.dropColumn('workspace_type');

    // Time model related fields
    table.dropColumn('billing_cycle');
    table.dropColumn('max_duration_months');

    table.dropColumn('posts_count');
    table.dropColumn('articles_count');
    table.dropColumn('opportunities_count');
    table.dropColumn('connects_count');
    table.dropColumn('cold_reach_count');
    table.dropColumn('team_count');
    table.dropColumn('gallery_count');

    table
      .uuid('workspace_id')
      .references('id')
      .inTable('workspaces')
      .onDelete('CASCADE')
      .index('index_subscriptions_on_workspace_id');
    table.date('start_date').notNullable();
    table.date('end_date').notNullable();
    table.date('payment_date').notNullable();
    table.text('payment_method').notNullable();
  });
}
