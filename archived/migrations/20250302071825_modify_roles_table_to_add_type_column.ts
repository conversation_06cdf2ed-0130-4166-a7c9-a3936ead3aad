import type { K<PERSON> } from 'knex';

import { Roles } from '@/constants/user-types';
import { RoleType } from '@/constants/roles';

export async function up(knex: Knex): Promise<void> {
  // Step 1: Add the 'type' column as nullable
  await knex.schema.alterTable('roles', (table) => {
    table.text('type').nullable();
  });

  // Step 2: Update existing roles with appropriate types
  // Update workspace-specific roles
  await knex('roles')
    .whereIn('key', [
      Roles.ORGANISATION_ADMIN,
      Roles.ORGANISATION_USER,
      Roles.SURGEON_ADMIN,
      Roles.SURGEON_USER,
      Roles.PROFESSIONAL_ADMIN,
      Roles.PROFESSIONAL_USER,
    ])
    .update({ type: RoleType.WORKSPACE });

  // Update all other roles to 'global'
  await knex('roles').whereNull('type').update({ type: RoleType.GLOBAL });

  // Step 3: Make the 'type' column not nullable
  await knex.schema.alterTable('roles', (table) => {
    table.text('type').notNullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('roles', (table) => {
    table.dropColumn('type');
  });
}
