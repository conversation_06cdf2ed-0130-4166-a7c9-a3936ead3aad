import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.dropTable('doctor_types');
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('doctor_types', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('name').unique().notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.integer('status').notNullable().defaultTo(1);
    })
    .raw(
      `
    CREATE TRIGGER doctor_types_updated_at BEFORE UPDATE
    ON doctor_types FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
    `,
    );
}
