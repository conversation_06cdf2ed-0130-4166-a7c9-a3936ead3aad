import { EntityType } from '@/constants/user-types';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts_polls', (t) => {
    t.dropPrimary();
    t.dropIndex([], 'index_posts_polls_on_voted_by');
    t.dropForeign(['voted_by']);

    t.renameColumn('voted_by', 'entity_id');

    t.text('entity_type').notNullable().defaultTo(EntityType.USER);

    t.primary(['post_id', 'entity_id']);
    t.index('entity_id', 'index_posts_polls_on_entity_id');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts_polls', (t) => {
    t.dropPrimary();
    t.dropIndex([], 'index_posts_polls_on_entity_id');
    t.dropColumn('entity_type');

    t.renameColumn('entity_id', 'voted_by');

    t.foreign('voted_by').references('id').inTable('users').onDelete('CASCADE');
    t.index('voted_by', 'index_posts_polls_on_voted_by');

    t.primary(['post_id', 'voted_by']);
  });
}
