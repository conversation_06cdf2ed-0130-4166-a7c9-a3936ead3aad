import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('user_countries', (t) => {
      t.primary(['user_id', 'country_id']);
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_role_user_countries_on_user_id');
      t.uuid('country_id')
        .notNullable()
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_role_user_countries_on_country_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER user_countries_updated_at BEFORE UPDATE
ON user_countries FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('user_countries');
}
