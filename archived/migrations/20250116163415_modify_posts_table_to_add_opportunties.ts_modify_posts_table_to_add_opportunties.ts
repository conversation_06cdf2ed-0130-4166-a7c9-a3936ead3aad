import { Knex } from 'knex';

import { PostActiveStatus, PostStatus, PostType } from '@/constants/posts';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('posts', (t) => {
    // Make category_id nullable since a post can now be linked to either category or opportunity
    t.uuid('category_id').nullable().alter();

    // Add opportunity_id as nullable with foreign key
    t.uuid('opportunity_id')
      .references('id')
      .inTable('opportunities')
      .onDelete('CASCADE')
      .unique()
      .nullable()
      .index('index_posts_on_opportunity_id');
  });

  // First check if there are any eligible opportunities
  const eligibleOpportunities = await knex('opportunities')
    .where({
      share_to_feed: true,
      status: 1,
    })
    .select(
      'id as opportunity_id',
      'created_by_user_id as published_by',
      'created_by_user_id as updated_by',
      'description as content',
      'workspace_id',
    );

  // Only proceed with insertion if there are eligible opportunities
  if (eligibleOpportunities.length > 0) {
    const postsToInsert = eligibleOpportunities.map((opportunity) => ({
      ...opportunity,
      post_status: PostStatus.PUBLISHED,
      post_type: PostType.OPPORTUNITY,
      post_schedule_date: new Date(),
      status: PostActiveStatus.ACTIVE,
    }));

    await knex('posts').insert(postsToInsert);
  }
}

export async function down(knex: Knex): Promise<void> {
  // First delete all posts linked to opportunities
  await knex('posts').where('opportunity_id', 'is not', null).delete();

  await knex.schema.alterTable('posts', (t) => {
    // Remove opportunity_id column
    t.dropColumn('opportunity_id');

    // Make category_id not nullable again
    t.uuid('category_id').notNullable().alter();
  });
}
