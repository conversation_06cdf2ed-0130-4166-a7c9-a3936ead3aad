import type { Knex } from 'knex';

const TABLES = {
  ORG: {
    original: 'organisations',
    renamed: 'workspaces',
  },
  ORG_FILES: {
    original: 'organisation_files',
    renamed: 'workspace_files',
  },
  ORG_USERS: {
    original: 'organisation_users',
    renamed: 'workspace_users',
  },
  ORG_FOLLOWERS: {
    original: 'organisation_followers',
    renamed: 'workspace_followers',
  },
  OPPORTUNITIES: 'opportunities',
  SUBSCRIPTIONS: 'subscriptions',
  POSTS: 'posts',
  USER_ROLES: 'user_roles',
};

const ORG_ID_FIELD = 'organisation_id';
const WORKSPACE_ID_FIELD = 'workspace_id';

export async function up(knex: Knex): Promise<void> {
  const tablesToRename = [TABLES.ORG, TABLES.ORG_FILES, TABLES.ORG_USERS, TABLES.ORG_FOLLOWERS];

  // Drop old triggers for the tables before renaming
  for (const table of tablesToRename) {
    await knex.raw(`DROP TRIGGER IF EXISTS ${table.original}_updated_at ON ${table.original}`);
  }

  // Drop the unique constraint on the email field before renaming the organisation table
  await knex.schema.alterTable(TABLES.ORG.original, (table) => {
    table.dropUnique(['email']);
  });

  // Drop the old indexes before renaming the fields
  await knex.schema.alterTable(TABLES.ORG.original, (table) => {
    table.dropIndex('country_id', 'index_organisations_on_country_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FILES.original, (table) => {
    table.dropIndex(ORG_ID_FIELD, 'index_organisation_files_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.ORG_USERS.original, (table) => {
    table.dropIndex('user_id', 'index_organisation_users_on_user_id');
    table.dropIndex(ORG_ID_FIELD, 'index_organisation_users_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FOLLOWERS.original, (table) => {
    table.dropIndex('user_id', 'index_organisation_followers_on_user_id');
    table.dropIndex(ORG_ID_FIELD, 'index_organisation_followers_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.OPPORTUNITIES, (table) => {
    table.dropIndex(ORG_ID_FIELD, 'index_opportunities_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.SUBSCRIPTIONS, (table) => {
    table.dropIndex(ORG_ID_FIELD, 'index_subscriptions_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.POSTS, (table) => {
    table.dropIndex(ORG_ID_FIELD, 'index_posts_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.USER_ROLES, (table) => {
    table.dropIndex(ORG_ID_FIELD, 'index_user_roles_on_organisation_id');
  });

  // Rename the tables
  for (const table of tablesToRename) {
    await knex.schema.renameTable(table.original, table.renamed);
  }

  // Recreate unique constraint on 'email' for the renamed table
  await knex.schema.alterTable(TABLES.ORG.renamed, (table) => {
    table.unique(['email']);
  });

  await knex.schema.alterTable(TABLES.ORG_FILES.renamed, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index(WORKSPACE_ID_FIELD, 'index_workspaces_files_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.ORG_USERS.renamed, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index('user_id', 'index_workspaces_users_on_user_id');
    table.index(WORKSPACE_ID_FIELD, 'index_workspaces_users_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FOLLOWERS.renamed, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index('user_id', 'index_workspaces_followers_on_user_id');
    table.index(WORKSPACE_ID_FIELD, 'index_workspaces_followers_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.OPPORTUNITIES, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index(WORKSPACE_ID_FIELD, 'index_opportunities_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.SUBSCRIPTIONS, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index(WORKSPACE_ID_FIELD, 'index_subscriptions_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.POSTS, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index(WORKSPACE_ID_FIELD, 'index_posts_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.USER_ROLES, (table) => {
    table.renameColumn(ORG_ID_FIELD, WORKSPACE_ID_FIELD);
    table.index(WORKSPACE_ID_FIELD, 'index_user_roles_on_workspace_id');
  });

  // Recreate triggers for the renamed tables
  for (const table of tablesToRename) {
    await knex.raw(`
      CREATE TRIGGER ${table.renamed}_updated_at BEFORE UPDATE
      ON ${table.renamed} FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `);
  }
}

export async function down(knex: Knex): Promise<void> {
  const tablesToRename = [TABLES.ORG, TABLES.ORG_FILES, TABLES.ORG_USERS, TABLES.ORG_FOLLOWERS];

  // Drop old triggers for the tables before renaming
  for (const table of tablesToRename) {
    await knex.raw(`DROP TRIGGER IF EXISTS ${table.renamed}_updated_at ON ${table.renamed}`);
  }

  // Drop the unique constraint on the email field before renaming the organisation table
  await knex.schema.alterTable(TABLES.ORG.renamed, (table) => {
    table.dropUnique(['email']);
  });

  // Drop the old indexes before renaming the fields
  await knex.schema.alterTable(TABLES.ORG.renamed, (table) => {
    table.dropIndex('country_id', 'index_workspaces_on_country_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FILES.renamed, (table) => {
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_workspaces_files_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.ORG_USERS.renamed, (table) => {
    table.dropIndex('user_id', 'index_workspaces_users_on_user_id');
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_workspaces_users_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FOLLOWERS.renamed, (table) => {
    table.dropIndex('user_id', 'index_workspaces_followers_on_user_id');
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_workspaces_followers_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.OPPORTUNITIES, (table) => {
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_opportunities_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.SUBSCRIPTIONS, (table) => {
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_subscriptions_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.POSTS, (table) => {
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_posts_on_workspace_id');
  });

  await knex.schema.alterTable(TABLES.USER_ROLES, (table) => {
    table.dropIndex(WORKSPACE_ID_FIELD, 'index_user_roles_on_workspace_id');
  });

  // Rename the tables
  for (const table of tablesToRename) {
    await knex.schema.renameTable(table.renamed, table.original);
  }

  // Recreate unique constraint on 'email' for the renamed table
  await knex.schema.alterTable(TABLES.ORG.original, (table) => {
    table.unique(['email']);
  });

  // Rename fields from 'organisation_id' to 'workspace_id' and recreate indexes
  await knex.schema.alterTable(TABLES.ORG.original, (table) => {
    table.index('country_id', 'index_organisations_on_country_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FILES.original, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index(ORG_ID_FIELD, 'index_organisation_files_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.ORG_USERS.original, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index('user_id', 'index_organisation_users_on_user_id');
    table.index(ORG_ID_FIELD, 'index_organisation_users_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.ORG_FOLLOWERS.original, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index('user_id', 'index_organisation_followers_on_user_id');
    table.index(ORG_ID_FIELD, 'index_organisation_followers_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.OPPORTUNITIES, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index(ORG_ID_FIELD, 'index_opportunities_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.SUBSCRIPTIONS, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index(ORG_ID_FIELD, 'index_subscriptions_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.POSTS, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index(ORG_ID_FIELD, 'index_posts_on_organisation_id');
  });

  await knex.schema.alterTable(TABLES.USER_ROLES, (table) => {
    table.renameColumn(WORKSPACE_ID_FIELD, ORG_ID_FIELD);
    table.index(ORG_ID_FIELD, 'index_user_roles_on_organisation_id');
  });

  // Recreate triggers for the renamed tables
  for (const table of tablesToRename) {
    await knex.raw(`
        CREATE TRIGGER ${table.original}_updated_at BEFORE UPDATE
        ON ${table.original} FOR EACH ROW EXECUTE PROCEDURE 
        update_updated_at_column();
      `);
  }
}
