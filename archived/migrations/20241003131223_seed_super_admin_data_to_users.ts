import type { K<PERSON> } from 'knex';
import { getAuth, CreateRequest, UserRecord } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';

import { AuthConstants } from '@/constants/auth';
import { Roles } from '@/constants/user-types';

import { UserData } from '@/interfaces/auth';

const EMAIL = '<EMAIL>';
const PASS = 'hello_world';
const FIRST_NAME = 'MiniCardiac';
const LAST_NAME = 'admin';

if (!getApps().length) {
  const envConfig = envConfiguration();
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  const superAdmin = {
    first_name: FIRST_NAME,
    last_name: LAST_NAME,
    email: EMAIL,
  };

  const firebaseObj: CreateRequest = {
    displayName: `${superAdmin.first_name} ${superAdmin.last_name}`,
    email: superAdmin.email,
    emailVerified: true,
    password: PASS,
  };

  let createdNew = false;

  let firebaseUser: UserRecord | null;
  try {
    // check if firebase user already exists
    firebaseUser = await getAuth().getUserByEmail(superAdmin.email);
  } catch (error) {
    // firebase throws an error when the user is not found
    // that causes an exception so we are skipping that error
    if (error.code === 'auth/user-not-found') {
      firebaseUser = null;
    } else {
      // rethrow if it's an unexpected error
      throw new Error(`Error fetching Firebase user: ${error.message}`);
    }
  }

  try {
    if (!firebaseUser) {
      firebaseUser = await getAuth().createUser(firebaseObj);
      createdNew = true;
    }

    const insertStaff = {
      ...(firebaseUser.customClaims?.userId && { id: firebaseUser.customClaims.userId }),
      ...superAdmin,
      provider_id: firebaseUser.uid,
    };

    const [insertedRow] = await knex('users').insert(insertStaff).returning('id');

    if (createdNew) {
      const additionalClaims: UserData = {
        [AuthConstants.FIREBASE_CLAIM_USER_ID]: insertedRow.id as string,
        [AuthConstants.FIREBASE_CLAIM_EMAIL]: superAdmin.email,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: Roles.SUPER_ADMIN,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: [],
        [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: null,
      };

      await getAuth().setCustomUserClaims(firebaseUser.uid, additionalClaims);
    }

    // Set Firebase custom claims
  } catch (error) {
    console.error('Error during user creation:', error);
    // Check if Firebase user was created and delete it
    if (firebaseUser?.uid) {
      await getAuth().deleteUser(firebaseUser.uid);
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('users').where('email', EMAIL).del().returning('provider_id');
  // await getAuth().deleteUser(user.provider_id);
}
