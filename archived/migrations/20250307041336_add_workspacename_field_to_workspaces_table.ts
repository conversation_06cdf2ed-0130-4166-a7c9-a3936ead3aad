import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // First add the column as nullable
  await knex.schema.alterTable('workspaces', (table) => {
    table.text('workspacename').nullable().unique();
  });

  // Update existing records with a cleaned workspacename
  await knex.raw(`
    UPDATE workspaces 
    SET workspacename = LOWER(
      CONCAT(
        REGEXP_REPLACE(TRIM(label), '[^a-zA-Z0-9_-]', '', 'g'),
        '-',
        id::text
      )
    )
  `);

  // After all records have a username, make the column not nullable
  await knex.schema.alterTable('workspaces', (table) => {
    table.text('workspacename').notNullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspaces', (table) => {
    table.dropColumn('workspacename');
  });
}
