import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Insert data from `workspaces` to `organisations`
  const workspaces = await knex
    .select(
      'id as workspace_id',
      'name',
      'created_at',
      'updated_at',
      'status',
      'hq_location',
      'registration_number',
      'country_id',
      'email',
      'website',
      'phone_number',
      'zip_code',
      'about_us',
      'instagram_link',
      'facebook_link',
      'twitter_link',
      'linkedin_link',
      'youtube_link',
      'tiktok_link',
    )
    .from('workspaces');

  if (workspaces.length > 0) {
    await knex('organisations').insert(workspaces);
  }
}

export async function down(knex: Knex): Promise<void> {
  // Update data in `workspaces` from `organisations`
  const organisations = await knex
    .select(
      'workspace_id as id',
      'name',
      'created_at',
      'updated_at',
      'status',
      'hq_location',
      'registration_number',
      'country_id',
      'email',
      'website',
      'phone_number',
      'zip_code',
      'about_us',
      'instagram_link',
      'facebook_link',
      'twitter_link',
      'linkedin_link',
      'youtube_link',
      'tiktok_link',
    )
    .from('organisations');

  for (const org of organisations) {
    // Update only the matching row in `workspaces` where id matches `workspace_id`
    await knex('workspaces').where({ id: org.id }).update({
      name: org.name,
      hq_location: org.hq_location,
      registration_number: org.registration_number,
      country_id: org.country_id,
      email: org.email,
      website: org.website,
      phone_number: org.phone_number,
      zip_code: org.zip_code,
      about_us: org.about_us,
      instagram_link: org.instagram_link,
      facebook_link: org.facebook_link,
      twitter_link: org.twitter_link,
      linkedin_link: org.linkedin_link,
      youtube_link: org.youtube_link,
      tiktok_link: org.tiktok_link,
      created_at: org.created_at,
      updated_at: org.updated_at,
      status: org.status,
    });
  }

  // Remove all records from `organisations` table after transferring data back
  await knex('organisations').del();
}
