import type { Knex } from 'knex';

interface Category {
  label: string;
  children?: Category[];
}

// Define the structure of the seed data
interface SeedData {
  account_type: string;
  categories: Category[];
}

const seedData: SeedData[] = [
  // Cardiac Surgeons
  {
    account_type: 'cardiac_surgeon',
    categories: [
      {
        label: 'Senior Surgeons',
        children: [
          { label: 'Professor', children: [] },
          { label: 'Associate Professor', children: [] },
          { label: 'Assistant Professor', children: [] },
          { label: 'Consultant', children: [] },
          { label: 'Specialist', children: [] },
          { label: 'Associate Specialist', children: [] },
        ],
      },
      {
        label: 'Junior Surgeons',
        children: [
          { label: 'Specialist Registrar', children: [] },
          { label: 'Specialty Trainee', children: [] },
          { label: 'Specialty Doctor', children: [] },
          { label: 'Junior Doctor', children: [] },
        ],
      },
    ],
  },

  // Organisations
  {
    account_type: 'organisation',
    categories: [
      {
        label: 'Industry',
        children: [
          {
            label: 'Medical Manufacturers',
            children: [
              { label: 'Devices, Instruments, and Equipment', children: [] },
              { label: 'Consumables', children: [] },
            ],
          },
          {
            label: 'Medical Suppliers',
            children: [{ label: 'Consumables', children: [] }],
          },
          {
            label: 'Medical Technologies',
            children: [
              { label: 'Products', children: [] },
              { label: 'Services', children: [] },
              { label: 'Solutions', children: [] },
            ],
          },
        ],
      },
      {
        label: 'Hospitals',
        children: [
          { label: 'Hospitals', children: [] },
          { label: 'Trusts', children: [] },
          { label: 'Cardiac Centres', children: [] },
        ],
      },
      {
        label: 'Societies',
        children: [
          { label: 'ISMICS', children: [] },
          { label: 'BISMICS', children: [] },
          { label: 'SMICTSI', children: [] },
          { label: 'SCTS', children: [] },
          { label: 'EACTS', children: [] },
        ],
      },
      {
        label: 'Charities',
        children: [
          { label: 'Non-profits', children: [] },
          { label: 'Healthcare-related Charities', children: [] },
        ],
      },
      {
        label: 'Insurers',
        children: [{ label: 'Healthcare Insurance Companies', children: [] }],
      },
    ],
  },

  // Members (General Public)
  {
    account_type: 'general_public',
    categories: [
      {
        label: 'Doctor',
        children: [
          {
            label: 'Senior Doctors',
            children: [
              { label: 'Professor', children: [] },
              { label: 'Associate Professor', children: [] },
              { label: 'Assistant Professor', children: [] },
              { label: 'Consultant', children: [] },
              { label: 'Specialist', children: [] },
              { label: 'Associate Specialist', children: [] },
            ],
          },
          {
            label: 'Junior Doctors',
            children: [
              { label: 'Specialist Registrar', children: [] },
              { label: 'Specialty Trainee', children: [] },
              { label: 'Specialty Doctor', children: [] },
            ],
          },
        ],
      },
      { label: 'Perfusionist', children: [] },
      { label: 'Surgical Practitioner', children: [] },
      { label: 'Nurse', children: [] },
      { label: 'Scientist', children: [] },
      { label: 'Patient', children: [] },
      { label: 'Student', children: [] },
      { label: 'Customer', children: [] },
      { label: 'Public', children: [] },
      {
        label: 'Industry',
        children: [
          { label: 'Staff (associated with organization)', children: [] },
          { label: 'Administrator (under organization)', children: [] },
        ],
      },
    ],
  },
];

export async function up(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex('subtypes').del();

  // Recursive function to insert the categories and their children
  async function insertCategories(categories: Category[], parentId = null, accountType: string) {
    for (const category of categories) {
      // Insert the current category
      const [{ id: subtypeId }] = await knex('subtypes')
        .insert({
          label: category.label,
          parent_id: parentId, // parent_id will be null for top-level categories
          account_type: accountType,
        })
        .returning('id'); // Capture the inserted ID for recursion

      // If the category has children, recursively insert them
      if (category.children && category.children.length > 0) {
        await insertCategories(category.children, subtypeId, accountType);
      }
    }
  }

  for (const account of seedData) {
    await insertCategories(account.categories, null, account.account_type);
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('subtypes').del();
}
