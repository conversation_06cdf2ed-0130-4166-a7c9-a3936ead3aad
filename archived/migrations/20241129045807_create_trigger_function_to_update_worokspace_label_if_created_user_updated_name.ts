import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create function to generate full name
  await knex.raw(`
    CREATE OR REPLACE FUNCTION generate_full_name(first_name TEXT, middle_name TEXT, last_name TEXT)
    R<PERSON>URNS TEXT AS $$
    BEGIN
      -- Handle nullable middle name
      RETURN TRIM(
        COALESCE(first_name, '') || 
        CASE 
          WHEN middle_name IS NOT NULL AND middle_name != '' THEN ' ' || middle_name
          ELSE ''
        END || 
        CASE 
          WHEN last_name IS NOT NULL AND last_name != '' THEN ' ' || last_name
          ELSE ''
        END
      );
    END;
    $$ LANGUAGE plpgsql;

    -- Trigger function to update workspace label
    CREATE OR REPLACE FUNCTION update_workspace_label()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Update workspace labels only for SURGEON and PROFESSIONAL types
      UPDATE workspaces 
      SET label = generate_full_name(NEW.first_name, NEW.middle_name, NEW.last_name)
      WHERE created_by = NEW.id 
        AND type IN ('SURGEON', 'PROFESSIONAL');
      
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    -- Create trigger on users table
    CREATE TRIGGER update_workspace_labels_on_user_change
    AFTER UPDATE OF first_name, middle_name, last_name 
    ON users
    FOR EACH ROW
    WHEN (
      OLD.first_name IS DISTINCT FROM NEW.first_name OR
      OLD.middle_name IS DISTINCT FROM NEW.middle_name OR
      OLD.last_name IS DISTINCT FROM NEW.last_name
    )
    EXECUTE FUNCTION update_workspace_label();
  `);
}

export async function down(knex: Knex): Promise<void> {
  // Drop the trigger and functions
  await knex.raw(`
    -- Drop the trigger
    DROP TRIGGER IF EXISTS update_workspace_labels_on_user_change ON users;
    
    -- Drop the functions
    DROP FUNCTION IF EXISTS update_workspace_label();
    DROP FUNCTION IF EXISTS generate_full_name(TEXT, TEXT, TEXT);
  `);
}
