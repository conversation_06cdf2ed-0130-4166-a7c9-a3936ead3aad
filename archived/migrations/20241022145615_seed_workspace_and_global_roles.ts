import type { K<PERSON> } from 'knex';

import { Roles } from '@/constants/user-types';

// Combine global and workspace roles for inserting
const roles = [
  { name: 'General Public', key: Roles.GENERAL_PUBLIC },
  { name: 'System Administrator', key: Roles.SYSTEM_ADMIN },
  { name: 'Super Administrator', key: Roles.SUPER_ADMIN },
  { name: 'Owner', key: Roles.OWNER },
  { name: 'Organisation Admin', key: Roles.ORGANISATION_ADMIN },
  { name: 'Organisation User', key: Roles.ORGANISATION_USER },
  { name: 'Surgeon Admin', key: Roles.SURGEON_ADMIN },
  { name: 'Surgeon User', key: Roles.SURGEON_USER },
  { name: 'Professional Admin', key: Roles.PROFESSIONAL_ADMIN },
  { name: 'Professional User', key: Roles.PROFESSIONAL_USER },
];

export async function up(knex: Knex): Promise<void> {
  // Get existing roles by key
  const existingRoles = await knex('roles').select('key');

  // Filter out roles that already exist
  const existingRoleKeys = existingRoles.map((role) => role.key);
  const rolesToInsert = roles.filter((role) => !existingRoleKeys.includes(role.key));

  // Insert only roles that do not already exist
  if (rolesToInsert.length) {
    await knex('roles').insert(rolesToInsert);
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('roles')
    .whereIn(
      'key',
      roles.map((role) => role.key),
    )
    .del();
}
