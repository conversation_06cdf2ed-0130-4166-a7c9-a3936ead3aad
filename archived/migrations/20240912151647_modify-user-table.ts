import type { Knex } from 'knex';

const TABLE_NAME = 'users';

const PREVIOUS_NAME = 'name';
const NEW_NAME = 'first_name';
const MIDDLE_NAME = 'middle_name';
const LAST_NAME = 'last_name';
const PROVIDER_ID = 'provider_id';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, async (table) => {
    table.renameColumn(PREVIOUS_NAME, NEW_NAME);
    table.text(MIDDLE_NAME);
    table.text(LAST_NAME).notNullable().defaultTo('');
    await knex(TABLE_NAME).whereNull(PROVIDER_ID).update(PROVIDER_ID, '');
    table.text(PROVIDER_ID).notNullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TABLE_NAME, async (table) => {
    table.renameColumn(NEW_NAME, PREVIOUS_NAME);
    table.dropColumn(MIDDLE_NAME);
    table.dropColumn(LAST_NAME);
    table.text(PROVIDER_ID).nullable().alter();
    await knex(TABLE_NAME).where(PROVIDER_ID, '').update(PROVIDER_ID, null);
  });
}
