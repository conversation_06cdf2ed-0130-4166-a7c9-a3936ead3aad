import { NestFactory } from '@nestjs/core';
import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { json } from 'express';
import helmet from 'helmet';
import * as admin from 'firebase-admin';
import cookieParser from 'cookie-parser';

import { AppModule } from './app.module';

import { CustomConfigService } from '@/config/configuration.service';

import { UsersModule } from '@/modules/users/users.module';
import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { WorkspaceFilesModule } from '@/modules/workspace-files/workspace-files.module';
import { WorkspaceUsersModule as WorkspaceUserAdminModule } from '@/modules/workspace-users/admin/workspace-users.module';
import { WorkspaceUsersModule as WorkspaceUserWorkspaceAdminModule } from '@/modules/workspace-users/workspace-admin/workspace-users.module';
import { RolesModule } from '@/modules/roles/roles.module';
import { ModulesModule } from '@/modules/modules/modules.module';
import { PermissionsModule } from '@/modules/permissions/permissions.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { OpportunitiesModule } from '@/modules/opportunities/opportunities.module';
import { DropDownModule } from '@/modules/drop-downs/dropdowns.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { SubtypesModule } from '@/modules/subtypes/subtypes.module';
import { WaitlistsModule } from '@/modules/waitlists/waitlists.module';
import { PostsModule } from '@/modules/posts/posts.module';
import { SubscriptionsModule } from '@/modules/subscriptions/subscriptions.module';
import { UtilsModule } from '@/modules/utils/utils.module';
import { FollowersModule } from '@/modules/followers/followers.module';

import { FileStorageModule } from '@/common/file-storage/file-storage.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    forceCloseConnections: true,
    logger: ['error', 'warn', 'debug', 'log'],
  });
  app.enableShutdownHooks();

  app.use(
    helmet({
      hidePoweredBy: true,
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false, // this is to allow redoc
    }),
  );
  app.setGlobalPrefix('api');
  app.use(json({ limit: '25mb' }));
  app.use(cookieParser());
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  const configService = app.get(CustomConfigService);

  const firebaseServiceAc = configService.getFirebaseServiceAccount();

  const { isProduction } = configService.getEnvironment();

  admin.initializeApp({
    credential: admin.credential.cert(firebaseServiceAc),
  });

  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle('minicardiac')
    .setDescription('The minicardiac API description')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    //TODO: WAITLIST: HAVE TO BE MODIFIED LATER
    include: [
      ...(isProduction
        ? []
        : [
            UsersModule,
            WorkspacesModule,
            WorkspaceFilesModule,
            WorkspaceUserAdminModule,
            WorkspaceUserWorkspaceAdminModule,
            RolesModule,
            ModulesModule,
            PermissionsModule,
            UserRolesModule,
            OpportunitiesModule,
            FileStorageModule,
            SubtypesModule,
            AuthModule,
            DropDownModule,
            PostsModule,
            SubscriptionsModule,
            UtilsModule,
            FollowersModule,
          ]),

      WaitlistsModule,
    ],
    deepScanRoutes: true,
  });
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      defaultModelsExpandDepth: -1,
    },
  });

  const appConfig = configService.getAppConfig();

  const { port, feDashboardUrl, fePublicUrl } = appConfig;

  app.enableCors({
    credentials: true,
    origin: [feDashboardUrl, fePublicUrl], // TODO: has to be checked, why the cors is required like jude said here: https://octalogictech.slack.com/archives/C077D9FC5NU/p1734671967648999?thread_ts=1734420399.983039&cid=C077D9FC5NU
  });

  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}`);
}

export default bootstrap();
