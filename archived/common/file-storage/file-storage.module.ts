import { Module } from '@nestjs/common';

import { FileStorageService } from '@/common/file-storage/file-storage.service';
import { AzureStorageService } from '@/common/file-storage/services/azure-storage.service';

import { CustomConfigService } from '@/config/configuration.service';

import { FileStorageController } from '@/common/file-storage/file-storage.controller';

@Module({
  controllers: [FileStorageController],
  providers: [
    {
      provide: FileStorageService,
      useClass: AzureStorageService,
    },
    CustomConfigService,
  ],
})
export class FileStorageModule {}
