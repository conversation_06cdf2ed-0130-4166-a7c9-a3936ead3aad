import * as nodemailer from 'nodemailer';
import { Injectable } from '@nestjs/common';

import { CustomConfigService } from '@/config/configuration.service';

@Injectable()
export class MailService {
  constructor(private readonly customConfigService: CustomConfigService) {}

  mailTransport() {
    const config = this.customConfigService.getSmtpConfig();
    const transporter = nodemailer.createTransport({
      host: config.host,
      port: 587,
      auth: {
        user: config.user,
        pass: config.password,
      },
    });

    return transporter;
  }

  async sendEmail(options: {
    from?: string;
    to: string | string[];
    subject: string;
    text?: string;
    html?: string;
  }): Promise<void> {
    try {
      const transporter = this.mailTransport();

      await transporter.sendMail({
        ...options,
        from: options.from || process.env.EMAIL_FROM,
      });
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  async sendVerificationEmail(to: string, verificationLink: string): Promise<void> {
    const subject = 'Verify your email address';
    const htmlContent = `
      <h2>Email Verification</h2>
      <p>Please click the link below to verify your email address:</p>
      <a href="${verificationLink}" target="_blank">Verify Email</a>
    `;

    await this.sendEmail({
      to,
      subject,
      html: htmlContent,
    });
  }

  async sendUserCreatedEmail(email: string, data: { fullName: string; resetLink: string }) {
    const { fullName, resetLink } = data;

    const subject = 'Welcome! Set Up Your Account';

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Welcome, ${fullName}!</h2>
        <p>An administrator has created an account for you.</p>
  
        <p>To get started, you need to set up your password. Click the button below to create a new password:</p>
        
        <p style="margin: 20px 0;">
          <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Set Password
          </a>
        </p>
        
        <p>If you have any questions, please contact your administrator.</p>
        <p>Thank you,</p>
        <p>Minicardiac</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html: htmlContent,
    });
  }

  async sendUserAssignedEmail(
    email: string,
    options: {
      fullName: string;
      workspaceName: string;
      resetLink: string;
    },
  ) {
    const { fullName, workspaceName, resetLink } = options;

    const subject = 'Welcome to Your Workspace - Set Up Your Password';

    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Welcome, ${fullName}!</h2>
        <p>You have been added to the workspace <strong>${workspaceName}</strong>.</p>
        
        <p>To access your account, you need to set up your password. Click the button below to create a new password:</p>
  
        <p style="margin: 20px 0;">
          <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">
            Set Password
          </a>
        </p>
        
        <p>If you have any questions, please contact your administrator.</p>
        <p>Thank you,</p>
        <p>Minicardiac</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html: htmlContent,
    });
  }
}
