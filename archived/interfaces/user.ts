import { Permissions } from '@/constants/permissions';
import { GlobalRoles, Roles } from '@/constants/user-types';
import { WorkspacesStatus } from '@/constants/workspaces';

export interface UserRolePermission {
  id: string;
  name: string;
  key: string;
}

export interface UserOrganizationRole {
  id: string;
  name: string;
  key: string;
  permissions: UserRolePermission[];
}

export interface UserOrganization {
  id: string;
  name: string;
  role: UserOrganizationRole;
}

export interface GetUserDetails {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  organisations: Record<string, UserOrganization>;
  globalRole?: {
    id: string;
    name: string;
    key: GlobalRoles;
    permissions: UserRolePermission[];
  };
}

export type GlobalPermission = {
  id: string;
  name: string;
  roleName: string;
  roleKey: Roles;
  permissions: Permissions[];
};

export type WorkspacePermission = {
  id: string;
  name: string;
  workspaceId: string;
  roleName: string;
  status: WorkspacesStatus;
  roleKey: Roles;
  permissions: Permissions[];
};
