import { DecodedIdToken } from 'firebase-admin/auth';

import { AuthConstants } from '@/constants/auth';
import { Roles } from '@/constants/user-types';
import { Permissions } from '@/constants/permissions';

export interface UserData {
  [AuthConstants.FIREBASE_UID]?: string;
  [AuthConstants.FIREBASE_CLAIM_USER_ID]: string;
  [AuthConstants.FIREBASE_CLAIM_EMAIL]: string;
  [AuthConstants.FIREBASE_CLAIM_ROLE]: Roles;
  [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: Permissions[];
  [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: string | null | undefined;
  [AuthConstants.FIREBASE_SUBS_PAGE_DISPLAY]?: boolean | null | undefined;
  [AuthConstants.FIREBASE_SUBS_ALERT_BANNER]?: boolean | null | undefined;
}

export type authUserData = Partial<DecodedIdToken> & UserData;
