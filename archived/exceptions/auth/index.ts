import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const emailRegisteredNotVerified = () =>
  new CustomHttpException('auth.email_registered_not_verified', HttpStatus.BAD_REQUEST);

export const userCantBeCreatedWithoutAnyRole = new CustomHttpException(
  'auth.user_cannot_be_created_without_any_role',
  HttpStatus.BAD_REQUEST,
);

export const workspaceTypeRoleRequiredAWorkspaceId = new CustomHttpException(
  'auth.workspace_type_role_required_a_workspace_id',
  HttpStatus.BAD_REQUEST,
);

export const globalTypeRoleNotRequiredAWorkspaceId = new CustomHttpException(
  'auth.global_type_role_should_not_need_a_workspace_id',
  HttpStatus.BAD_REQUEST,
);

export const recentSignInRequired = new CustomHttpException(
  'auth.recent_sign_in_required',
  HttpStatus.UNAUTHORIZED,
);

export const cookieNotFound = new CustomHttpException(
  'auth.cookie_not_found',
  HttpStatus.BAD_REQUEST,
);

export const invalidCookie = new CustomHttpException('auth.invalid_cookie', HttpStatus.BAD_REQUEST);

export const sessionExpired = new CustomHttpException(
  'auth.session_expired',
  HttpStatus.UNAUTHORIZED,
);

export const invalidCredentials = new CustomHttpException(
  'auth.invalid_credentials',
  HttpStatus.UNAUTHORIZED,
);

export const accountLocked = new CustomHttpException('auth.account_locked', HttpStatus.FORBIDDEN);

export const tokenInvalid = new CustomHttpException('auth.token_invalid', HttpStatus.UNAUTHORIZED);

export const tokenExpired = new CustomHttpException('auth.token_expired', HttpStatus.UNAUTHORIZED);

export const userNoWorkspaceAccess = new CustomHttpException(
  'auth.user_no_workspace_access',
  HttpStatus.FORBIDDEN,
);
