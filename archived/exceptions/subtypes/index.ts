import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const parentSubtypeNotFound = new CustomHttpException(
  'subtypes.error_messages.parent_subtype_not_found',
  HttpStatus.NOT_FOUND,
);

export const parentSubtypeBelongsToDifferentAccountType = new CustomHttpException(
  'subtypes.error_messages.parent_subtype_belongs_to_different_account_type',
  HttpStatus.NOT_FOUND,
);

export const subtypeIdOrNameRequired = new CustomHttpException(
  'subtypes.error_messages.subtype_id_or_name_required',
  HttpStatus.BAD_REQUEST,
);
