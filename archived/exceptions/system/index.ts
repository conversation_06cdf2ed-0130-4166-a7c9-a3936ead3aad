import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

import { I18nPath } from '@/generated/i18n.generated';

export const routeNotFound = new CustomHttpException(
  'exception.route_not_found',
  HttpStatus.NOT_FOUND,
);

export const validationError = (
  message: I18nPath | string = 'exception.validation_error' as I18nPath,
  payload = {},
  httpCode: HttpStatus = HttpStatus.BAD_REQUEST,
) => new CustomHttpException(message as I18nPath, httpCode, payload);

export const unauthorized = () =>
  new CustomHttpException('exception.unauthorized', HttpStatus.UNAUTHORIZED);

export const forbidden = () => new CustomHttpException('exception.forbidden', HttpStatus.FORBIDDEN);

export const forbiddenFirebase = (message = 'ERR_UNAUTHORIZED') =>
  new CustomHttpException(message as any, HttpStatus.UNAUTHORIZED);

export const sessionExpired = () =>
  new CustomHttpException('exception.session_expired', 440 as HttpStatus);
