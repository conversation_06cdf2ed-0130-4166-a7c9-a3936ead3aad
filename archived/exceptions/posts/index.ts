import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const postScheduleDateFutureRequired = new CustomHttpException(
  'posts.post_schedule.date_future_required',
  HttpStatus.NOT_ACCEPTABLE,
);

export const postInUnsubscribedCountryException = new CustomHttpException(
  'posts.country_id.post_in_unsubscribed_country',
  HttpStatus.NOT_ACCEPTABLE,
);

export const invalidPostStatusTransitionException = new CustomHttpException(
  'posts.post_status.invalid_transition',
  HttpStatus.BAD_REQUEST, // Using BAD_REQUEST for status as it's more appropriate for a transition error
);

export const postCreatorCannotRemoveOwnPost = () =>
  new CustomHttpException(
    'posts.reported_post.post_creator_cannot_report_a_post',
    HttpStatus.BAD_REQUEST,
  );

export const reportedPostIsNotModifiable = () =>
  new CustomHttpException(
    'posts.reported_post.reported_post_is_not_modifiable',
    HttpStatus.FORBIDDEN,
  );
