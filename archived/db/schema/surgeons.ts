import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, timestamp, uuid, text } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';
import { professionalAssociations } from './professional-associations';
import { professionalAwards } from './professional-awards';
import { professionalCertificates } from './professional-certificates';

export const surgeons = pgTable('surgeons', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  yearsOfExperience: integer('years_of_experience'),
  currentHospital: text('current_hospital'),
  email: text('email'),
  profileSummary: text('profile_summary'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const surgeonsRelations = relations(surgeons, ({ one, many }) => ({
  workspace: one(workspaces, {
    fields: [surgeons.workspaceId],
    references: [workspaces.id],
  }),
  professionalAssociations: many(professionalAssociations),
  professionalAwards: many(professionalAwards),
  professionalCertificates: many(professionalCertificates),
}));

export type Surgeons = typeof surgeons.$inferSelect;
