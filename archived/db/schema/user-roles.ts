import { relations, sql } from 'drizzle-orm';
import { boolean, integer, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { UserRolesStatus } from '@/constants/user-roles';

import { roles } from './roles';
import { users } from './users';
import { userPermissions } from './user-permissions';
import { workspaces } from './workspaces';

export const userRoles = pgTable('user_roles', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  roleId: uuid('role_id')
    .notNull()
    .references(() => roles.id, { onDelete: 'cascade' }),
  workspaceId: uuid('workspace_id').references(() => workspaces.id, {
    onDelete: 'cascade',
  }),
  active: boolean('active').notNull().default(true),
  archived: boolean('archived').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(UserRolesStatus.ACTIVE),
});

export const userRolesRelations = relations(userRoles, ({ one, many }) => ({
  user: one(users, {
    fields: [userRoles.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [userRoles.roleId],
    references: [roles.id],
  }),
  workspaces: one(workspaces, {
    fields: [userRoles.workspaceId],
    references: [workspaces.id],
  }),
  userPermissions: many(userPermissions),
}));

export type UserRole = typeof userRoles.$inferSelect;
export type NewUserRole = typeof userRoles.$inferInsert;
