import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { workspaceUsers } from './workspace-users';
import { workspaceFiles } from './workspace-files';
import { workspaceFollowers } from './workspace-followers';
import { opportunities } from './opportunities';
import { userRoles } from './user-roles';
import { posts } from './posts';
import { subscriptions } from './subscriptions';
import { organisations } from './organisations';
import { surgeons } from './surgeons';
import { professionals } from './professionals';
import { users } from './users';
import { subtypes } from './subtypes';

import { WORKSPACE_RELATIONS, WorkspaceType, WorkspacesStatus } from '@/constants/workspaces';

const typeEnum = pgEnum('type', enumToPgEnum(WorkspaceType));

export const workspaces = pgTable('workspaces', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  type: typeEnum('type').notNull(),
  label: text('label').notNull(),
  workspacename: text('workspacename').notNull().unique(),
  createdBy: uuid('created_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  subtypeId: uuid('subtype_id')
    .notNull()
    .references(() => subtypes.id, { onDelete: 'cascade' }),
  instagramLink: text('instagram_link'),
  facebookLink: text('facebook_link'),
  twitterLink: text('twitter_link'),
  linkedinLink: text('linkedin_link'),
  youtubeLink: text('youtube_link'),
  tiktokLink: text('tiktok_link'),
  profileImageUrl: text('profile_image_url'),
  profileImageUrlThumbnail: text('profile_image_url_thumbnail'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(WorkspacesStatus.INACTIVE),
});

export const workspacesRelations = relations(workspaces, ({ many, one }) => ({
  workspaceUsers: many(workspaceUsers),
  workspaceFiles: many(workspaceFiles),
  workspaceFollowers: many(workspaceFollowers),
  opportunities: many(opportunities),
  userRoles: many(userRoles),
  posts: many(posts),
  subscriptions: many(subscriptions),
  createdByUser: one(users, {
    fields: [workspaces.createdBy],
    references: [users.id],
  }),
  subtype: one(subtypes, {
    fields: [workspaces.subtypeId],
    references: [subtypes.id],
  }),
  [WORKSPACE_RELATIONS.ORGANISATION]: one(organisations, {
    fields: [workspaces.id],
    references: [organisations.workspaceId],
  }),
  [WORKSPACE_RELATIONS.SURGEON]: one(surgeons, {
    fields: [workspaces.id],
    references: [surgeons.workspaceId],
  }),
  [WORKSPACE_RELATIONS.PROFESSIONAL]: one(professionals, {
    fields: [workspaces.id],
    references: [professionals.workspaceId],
  }),
}));

export type Workspaces = typeof workspaces.$inferSelect;
