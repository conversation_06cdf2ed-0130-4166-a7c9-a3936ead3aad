import { relations } from 'drizzle-orm';
import { integer, pgTable, primaryKey, timestamp, uuid } from 'drizzle-orm/pg-core';

import { users } from './users';
import { workspaces } from './workspaces';

export const workspaceUsers = pgTable(
  'workspace_users',
  {
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    workspaceId: uuid('workspace_id')
      .notNull()
      .references(() => workspaces.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.userId, table.workspaceId] }),
  }),
);

export const workspaceUsersRelations = relations(workspaceUsers, ({ one }) => ({
  user: one(users, {
    fields: [workspaceUsers.userId],
    references: [users.id],
  }),
  workspace: one(workspaces, {
    fields: [workspaceUsers.workspaceId],
    references: [workspaces.id],
  }),
}));

export type WorkspaceUsers = typeof workspaceUsers.$inferSelect;
