import { pgTable, uuid, timestamp, integer, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { countries } from './countries';
import { subscriptionWorkspaces } from './subscription-workspaces';

export const subscriptionCountries = pgTable(
  'subscription_countries',
  {
    subscriptionWorkspaceId: uuid('subscription_workspace_id')
      .notNull()
      .references(() => subscriptionWorkspaces.id, { onDelete: 'cascade' }),
    countryId: uuid('country_id')
      .notNull()
      .references(() => countries.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.subscriptionWorkspaceId, table.countryId] }),
  }),
);

export const subscriptionCountriesRelations = relations(subscriptionCountries, ({ one }) => ({
  subscription: one(subscriptionWorkspaces, {
    fields: [subscriptionCountries.subscriptionWorkspaceId],
    references: [subscriptionWorkspaces.id],
  }),
  country: one(countries, {
    fields: [subscriptionCountries.countryId],
    references: [countries.id],
  }),
}));

export type SubscriptionCountries = typeof subscriptionCountries.$inferSelect;
export type NewSubscriptionCountries = typeof subscriptionCountries.$inferInsert;
