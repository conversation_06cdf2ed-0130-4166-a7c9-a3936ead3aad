import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { RoleStatus, RoleType } from '@/constants/roles';

import { enumToPgEnum } from '@/utils/database';

import { rolePermissions } from './role-permissions';
import { userRoles } from './user-roles';

const typeEnum = pgEnum('type', enumToPgEnum(RoleType));

export const roles = pgTable('roles', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  key: text('key').unique().notNull(),
  type: typeEnum('type').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(RoleStatus.ACTIVE),
});

export const rolesRelations = relations(roles, ({ many }) => ({
  userRole: many(userRoles),
  rolesPermissions: many(rolePermissions),
}));
