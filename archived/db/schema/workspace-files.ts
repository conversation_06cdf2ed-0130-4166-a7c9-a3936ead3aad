import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { WorkspaceFilesSection } from '@/constants/workspace-files';

import { workspaces } from './workspaces';

const sectionEnum = pgEnum('section', enumToPgEnum(WorkspaceFilesSection));

export const workspaceFiles = pgTable('workspace_files', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  mediaPath: text('media_path').notNull(),
  mediaType: text('media_type').notNull(),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  label: text('label').notNull(),
  section: sectionEnum('section').notNull(),
  status: integer('status').notNull().default(1),
});

export const workspaceFilesRelations = relations(workspaceFiles, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [workspaceFiles.workspaceId],
    references: [workspaces.id],
  }),
}));
