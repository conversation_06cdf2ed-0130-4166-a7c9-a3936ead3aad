import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { surgeons } from './surgeons';

export const professionalAssociations = pgTable('professional_associations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  surgeonId: uuid('surgeon_id')
    .notNull()
    .references(() => surgeons.id, { onDelete: 'cascade' }),
  associationName: text('association_name').notNull(),
  membershipId: text('membership_id'),
  joinedDate: date('joined_date').notNull(),
  expiryDate: date('expiry_date'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalAssociationsRelations = relations(professionalAssociations, ({ one }) => ({
  surgeon: one(surgeons, {
    fields: [professionalAssociations.surgeonId],
    references: [surgeons.id],
  }),
}));
