import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, smallint, timestamp, uuid, text, pgEnum } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { WorkspaceType } from '@/constants/workspaces';
import { BillingCycle } from '@/constants/subscriptions';

import { subscriptionWorkspaces } from './subscription-workspaces';

const workspaceTypeEnum = pgEnum('workspace_type', enumToPgEnum(WorkspaceType));
const billingCycleTypeEnum = pgEnum('billing_cyle', enumToPgEnum(BillingCycle));

export const subscriptions = pgTable('subscriptions', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  title: text('title').notNull(),
  workspaceType: workspaceTypeEnum('workspace_type').notNull(),
  amount: smallint('amount').notNull(),
  billingCycle: billingCycleTypeEnum('billing_cycle').notNull(),
  maxDurationMonths: smallint('max_duration_months'),
  postsCount: smallint('posts_count').notNull().default(0),
  articlesCount: smallint('articles_count').notNull().default(0),
  opportunitiesCount: smallint('opportunities_count').notNull().default(0),
  connectsCount: smallint('connects_count').notNull().default(0),
  coldReachCount: smallint('cold_reach_count').notNull().default(0),
  teamCount: smallint('team_count').notNull().default(0),
  galleryCount: smallint('gallery_count').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const subscriptionsRelations = relations(subscriptions, ({ many }) => ({
  subscriptionWorkspaces: many(subscriptionWorkspaces),
}));

export type Subscriptions = typeof subscriptions.$inferSelect;
