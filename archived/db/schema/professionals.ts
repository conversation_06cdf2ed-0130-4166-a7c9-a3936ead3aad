import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

export const professionals = pgTable('professionals', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const professionalsRelations = relations(professionals, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [professionals.workspaceId],
    references: [workspaces.id],
  }),
}));

export type Professionals = typeof professionals.$inferSelect;
