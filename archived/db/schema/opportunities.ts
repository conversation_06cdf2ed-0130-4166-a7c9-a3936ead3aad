import { relations, sql } from 'drizzle-orm';
import { boolean, integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { OpportunityTypes } from '@/constants/opportunities';

import { opportunitiesAttachments } from './opportunities-attachments';
import { workspaces } from './workspaces';
import { users } from './users';
import { posts } from './posts';
import { opportunityApplications } from './opportunity-applications';

const typeEnum = pgEnum('type', enumToPgEnum(OpportunityTypes));

export const opportunities = pgTable('opportunities', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),
  createdByUserId: uuid('created_by_user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  type: typeEnum('type').notNull(),
  title: text('title').notNull(),
  shareToFeed: boolean('share_to_feed').notNull().default(false),
  description: text('description').notNull(),
  shortDescription: text('short_description').notNull(),
  contactPersonName: text('contact_person_name').notNull(),
  email: text('email').notNull(),
  phone: text('phone').notNull(),
  websiteLink: text('website_link').notNull(),
  expiryDate: timestamp('expiry_date').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const opportunitiesRelations = relations(opportunities, ({ many, one }) => ({
  opportunitiesAttachments: many(opportunitiesAttachments),
  workspace: one(workspaces, {
    fields: [opportunities.workspaceId],
    references: [workspaces.id],
  }),
  post: many(posts),
  applicants: many(opportunityApplications),
}));
