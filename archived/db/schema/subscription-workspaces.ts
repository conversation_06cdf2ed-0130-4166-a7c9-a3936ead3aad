import { relations, sql } from 'drizzle-orm';
import { date, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';
import { subscriptionCountries } from './subscription-countries';
import { subscriptions } from './subscriptions';

export const subscriptionWorkspaces = pgTable('subscription_workspaces', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),
  subscriptionId: uuid('subscription_id')
    .notNull()
    .references(() => subscriptions.id, { onDelete: 'cascade' }),
  startDate: date('start_date').notNull(),
  endDate: date('end_date').notNull(),
  paymentDate: date('payment_date').notNull(),
  paymentMethod: text('payment_method').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const subscriptionWorkspacesRelations = relations(
  subscriptionWorkspaces,
  ({ one, many }) => ({
    workspace: one(workspaces, {
      fields: [subscriptionWorkspaces.workspaceId],
      references: [workspaces.id],
    }),
    subscription: one(subscriptions, {
      fields: [subscriptionWorkspaces.subscriptionId],
      references: [subscriptions.id],
    }),
    subscriptionCountries: many(subscriptionCountries),
  }),
);

export type NewSubscriptionWorkspace = typeof subscriptionWorkspaces.$inferInsert;
