import { pgTable, uuid, timestamp, integer, primaryKey, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { posts } from './posts';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postLikes = pgTable(
  'post_likes',
  {
    postId: uuid('post_id')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    entityId: uuid('entity_id').notNull(),
    entityType: entityTypeEnum('entity_type').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.postId, table.entityId] }),
  }),
);

export const postLikesRelations = relations(postLikes, ({ one }) => ({
  post: one(posts, {
    fields: [postLikes.postId],
    references: [posts.id],
  }),
}));

export type PostLikes = typeof postLikes.$inferSelect;
export type NewPostLikes = typeof postLikes.$inferInsert;
