import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';
import { posts } from './posts';
import { enumToPgEnum } from '@/utils/database';
import { EntityType } from '@/constants/user-types';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postsPolls = pgTable('posts_polls', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  option: text('option').notNull(),
  entityId: uuid('entity_id').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  entityType: entityTypeEnum('entity_type').notNull(),
  status: integer('status').default(1),
});

export const postsPollsRelations = relations(postsPolls, ({ one }) => ({
  post: one(posts, {
    fields: [postsPolls.postId],
    references: [posts.id],
  }),
}));

export type PostsPolls = typeof postsPolls.$inferSelect;
export type NewpostsPolls = typeof postsPolls.$inferInsert;
