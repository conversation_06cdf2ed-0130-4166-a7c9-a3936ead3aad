import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { surgeons } from './surgeons'; // Assuming you have this file

export const professionalCertificates = pgTable('professional_certificates', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  surgeonId: uuid('surgeon_id')
    .notNull()
    .references(() => surgeons.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  issuingBody: text('issuing_body').notNull(),
  issuedDate: date('issued_date').notNull(),
  expiryDate: date('expiry_date'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalCertificatesRelations = relations(professionalCertificates, ({ one }) => ({
  surgeon: one(surgeons, {
    fields: [professionalCertificates.surgeonId],
    references: [surgeons.id],
  }),
}));
