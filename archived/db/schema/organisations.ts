import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { countries } from './countries';
import { workspaces } from './workspaces';
import { organisationCertifications } from './organisation-certifications';

export const organisations = pgTable('organisations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  hqLocation: text('hq_location'),
  registrationNumber: text('registration_number'),
  countryId: uuid('country_id').references(() => countries.id, { onDelete: 'cascade' }),
  city: text('city'),
  email: text('email').unique(),
  website: text('website'),
  phoneNumber: text('phone_number'),
  zipCode: text('zip_code'),
  aboutUs: text('about_us'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const organisationsRelations = relations(organisations, ({ one, many }) => ({
  workspace: one(workspaces, {
    fields: [organisations.workspaceId],
    references: [workspaces.id],
  }),
  country: one(countries, {
    fields: [organisations.countryId],
    references: [countries.id],
  }),
  organisationCertifications: many(organisationCertifications),
}));

export type Organisations = typeof organisations.$inferSelect;
