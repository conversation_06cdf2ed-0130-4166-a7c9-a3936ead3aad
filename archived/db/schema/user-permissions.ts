import { pgTable, uuid, timestamp, integer, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { permissions } from './permisssions';
import { userRoles } from './user-roles';

export const userPermissions = pgTable(
  'user_permissions',
  {
    userRoleId: uuid('user_role_id')
      .notNull()
      .references(() => userRoles.id, { onDelete: 'cascade' }),
    permissionId: uuid('permission_id')
      .notNull()
      .references(() => permissions.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.userRoleId, table.permissionId] }),
  }),
);

export const userPermissionsRelations = relations(userPermissions, ({ one }) => ({
  userRole: one(userRoles, {
    fields: [userPermissions.userRoleId],
    references: [userRoles.id],
  }),
  permission: one(permissions, {
    fields: [userPermissions.permissionId],
    references: [permissions.id],
  }),
}));
