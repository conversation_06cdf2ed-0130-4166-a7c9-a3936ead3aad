import { relations } from 'drizzle-orm';
import { integer, pgEnum, pgTable, primaryKey, timestamp, uuid } from 'drizzle-orm/pg-core';

import { EntityType } from '@/constants/user-types';

import { workspaces } from './workspaces';

import { enumToPgEnum } from '@/utils/database';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const workspaceFollowers = pgTable(
  'workspace_followers',
  {
    entityId: uuid('entity_id').notNull(),
    workspaceId: uuid('workspace_id')
      .notNull()
      .references(() => workspaces.id, { onDelete: 'cascade' }),
    entityType: entityTypeEnum('entity_type').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.entityId, table.workspaceId] }),
  }),
);

export const workspaceFollowersRelations = relations(workspaceFollowers, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [workspaceFollowers.workspaceId],
    references: [workspaces.id],
  }),
}));
