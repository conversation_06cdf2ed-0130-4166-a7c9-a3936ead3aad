import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { PostStatus, PostType } from '@/constants/posts';

import { enumToPgEnum } from '@/utils/database';

import { users } from './users';
import { workspaces } from './workspaces';
import { categories } from './categories';
import { postCountries } from './post-countries';
import { postComments } from './post-comments';
import { postLikes } from './post-likes';
import { postViews } from './post-views';
import { postTags } from './post-tags';
import { postMedias } from './post-medias';
import { postNotifications } from './post-notifications';
import { opportunities } from './opportunities';
import { reportedPosts } from './reported-posts';

const postStatusEnum = pgEnum('post_status', enumToPgEnum(PostStatus));
const postTypeEnum = pgEnum('post_type', enumToPgEnum(PostType));

export const postsRelationsNames = {
  publisher: 'publisher',
  updatedBy: 'updatedBy',
};

export const posts = pgTable('posts', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  publishedBy: uuid('published_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedBy: uuid('updated_by')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  content: text('content'),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, {
      onDelete: 'cascade',
    }),
  opportunityId: uuid('opportunity_id').references(() => opportunities.id, { onDelete: 'cascade' }),
  categoryId: uuid('category_id').references(() => categories.id, { onDelete: 'cascade' }),
  postStatus: postStatusEnum('post_status').notNull(),
  postType: postTypeEnum('post_type').notNull(),
  postScheduleDate: timestamp('post_schedule_date').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const postsRelations = relations(posts, ({ one, many }) => ({
  publishedBy: one(users, {
    fields: [posts.publishedBy],
    references: [users.id],
    relationName: postsRelationsNames.publisher,
  }),
  updatedBy: one(users, {
    fields: [posts.updatedBy],
    references: [users.id],
    relationName: postsRelationsNames.updatedBy,
  }),
  workspace: one(workspaces, {
    fields: [posts.workspaceId],
    references: [workspaces.id],
  }),
  category: one(categories, {
    fields: [posts.categoryId],
    references: [categories.id],
  }),
  opportunity: one(opportunities, {
    fields: [posts.opportunityId],
    references: [opportunities.id],
  }),
  postCountries: many(postCountries),
  postComments: many(postComments),
  postLikes: many(postLikes),
  postViews: many(postViews),
  postTags: many(postTags),
  postMedias: many(postMedias),
  postNotifications: many(postNotifications),
  reports: many(reportedPosts),
}));
