import { relations, sql } from 'drizzle-orm';
import { AnyPgColumn, integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { AccountType } from '@/constants/user-types';

import { users } from './users';
import { workspaces } from './workspaces';

const accountTypeEnum = pgEnum('account_type', enumToPgEnum(AccountType));

export const subtypes = pgTable('subtypes', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('label').notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => subtypes.id, { onDelete: 'cascade' }),
  accountType: accountTypeEnum('account_type').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subtypesRelations = relations(subtypes, ({ one, many }) => ({
  parent: one(subtypes, {
    fields: [subtypes.parentId],
    references: [subtypes.id],
    relationName: 'parentChild',
  }),
  children: many(subtypes, { relationName: 'parentChild' }),
  users: many(users),
  workspaces: many(workspaces),
}));
