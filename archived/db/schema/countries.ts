import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { continents } from './continents';
import { postCountries } from './post-countries';
import { subscriptionCountries } from './subscription-countries';
import { users } from './users';

export const countries = pgTable('countries', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').unique().notNull(),
  code: text('code').unique().notNull(),
  continentId: uuid('continent_id')
    .notNull()
    .references(() => continents.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const countriesRelations = relations(countries, ({ one, many }) => ({
  continent: one(continents, {
    fields: [countries.continentId],
    references: [continents.id],
  }),
  postCountries: many(postCountries),
  subscriptionCountries: many(subscriptionCountries),
  uesrs: many(users),
}));
