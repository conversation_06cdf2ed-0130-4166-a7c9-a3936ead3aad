import { pgTable, uuid, text, boolean, timestamp, integer } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

import { workspaceUsers } from './workspace-users';
import { userRoles } from './user-roles';
import { userCategories } from './user-categories';
import { posts, postsRelationsNames } from './posts';
import { postViews } from './post-views';
import { subtypes } from './subtypes';
import { countries } from './countries';
import { workspaces } from './workspaces';
import { opportunityApplications } from './opportunity-applications';
import { reportedPosts } from './reported-posts';

export const users = pgTable('users', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  firstName: text('first_name').notNull(),
  middleName: text('middle_name'),
  lastName: text('last_name').notNull(),
  email: text('email').notNull().unique(),
  about: text('about'),
  providerId: text('provider_id').notNull(),
  subtypeId: uuid('subtype_id').references(() => subtypes.id, { onDelete: 'cascade' }),
  username: text('username').notNull().unique(),
  jobTitle: text('job_title'),
  countryId: uuid('country_id').references(() => countries.id, { onDelete: 'cascade' }),
  isLive: boolean('is_live').notNull().default(false),
  active: boolean('active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const usersRelations = relations(users, ({ many, one }) => ({
  workspaceUsers: many(workspaceUsers),
  userRoles: many(userRoles),
  userCategories: many(userCategories),
  publishedPosts: many(posts, {
    relationName: postsRelationsNames.publisher,
  }),
  updatedPosts: many(posts, {
    relationName: postsRelationsNames.updatedBy,
  }),
  postViews: many(postViews),
  subtype: one(subtypes, {
    fields: [users.subtypeId],
    references: [subtypes.id],
  }),
  country: one(countries, {
    fields: [users.countryId],
    references: [countries.id],
  }),
  createdWorkspaces: many(workspaces),
  applications: many(opportunityApplications),
  reportedPosts: many(reportedPosts),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
