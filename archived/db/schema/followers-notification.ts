// database/schema.ts
import { relations, sql } from 'drizzle-orm';
import { pgTable, timestamp, boolean, uuid, integer, pgEnum } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { FollowersNotificationsStatus } from '@/constants/followers-notifications';
import { EntityType } from '@/constants/user-types';

import { users } from './users';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const followersNotifications = pgTable('followers_notification', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  entityId: uuid('entity_id').notNull(),
  entityType: entityTypeEnum('entity_type').notNull(),
  followedId: uuid('followed_id').notNull(),
  isRead: boolean('is_read').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(FollowersNotificationsStatus.ACTIVE),
});

export const followersNotificationsRelations = relations(followersNotifications, ({ one }) => ({
  followed: one(users, {
    fields: [followersNotifications.followedId],
    references: [users.id],
  }),
}));

export type FollowersNotifications = typeof followersNotifications.$inferSelect;
export type NewFollowersNotifications = typeof followersNotifications.$inferInsert;
