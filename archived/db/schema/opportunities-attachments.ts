import { relations, sql } from 'drizzle-orm';
import { pgTable, text, uuid, real, integer, timestamp } from 'drizzle-orm/pg-core';
import { opportunities } from './opportunities';

export const opportunitiesAttachments = pgTable('opportunities_attachments', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  opportunityId: uuid('opportunity_id')
    .notNull()
    .references(() => opportunities.id, { onDelete: 'cascade' }),
  filePath: text('file_path').notNull(),
  fileType: text('file_type').notNull(),
  fileSize: real('file_size').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const opportunitiesAttachmentsRelations = relations(opportunitiesAttachments, ({ one }) => ({
  opportunity: one(opportunities, {
    fields: [opportunitiesAttachments.opportunityId],
    references: [opportunities.id],
  }),
}));
