import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';

export const postMedias = pgTable('post_medias', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  mediaPath: text('media_path').notNull(),
  mediaType: text('media_type').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(1),
});

export const postMediasRelations = relations(postMedias, ({ one }) => ({
  post: one(posts, {
    fields: [postMedias.postId],
    references: [posts.id],
  }),
}));
