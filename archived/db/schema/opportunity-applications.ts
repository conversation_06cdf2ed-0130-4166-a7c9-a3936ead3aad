import { pgTable, uuid, timestamp, integer, primaryKey, text } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { opportunities } from './opportunities';
import { users } from './users';

export const opportunityApplications = pgTable(
  'opportunity_applications',
  {
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    opportunityId: uuid('opportunity_id')
      .notNull()
      .references(() => opportunities.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    email: text('email').notNull(),
    phoneNumber: text('phone_number').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    status: integer('status').notNull().default(1),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.opportunityId, table.userId] }),
  }),
);

export const opportunityApplicationsRelations = relations(opportunityApplications, ({ one }) => ({
  opportunity: one(opportunities, {
    fields: [opportunityApplications.opportunityId],
    references: [opportunities.id],
  }),
  user: one(users, {
    fields: [opportunityApplications.userId],
    references: [users.id],
  }),
}));

export type OpportunityApplications = typeof opportunityApplications.$inferSelect;
export type NewOpportunityApplications = typeof opportunityApplications.$inferInsert;
