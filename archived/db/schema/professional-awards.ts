import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { surgeons } from './surgeons';

export const professionalAwards = pgTable('professional_awards', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  surgeonId: uuid('surgeon_id')
    .notNull()
    .references(() => surgeons.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  awardingBody: text('awarding_body').notNull(),
  awardedDate: date('awarded_date').notNull(),
  description: text('description'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalAwardsRelations = relations(professionalAwards, ({ one }) => ({
  surgeon: one(surgeons, {
    fields: [professionalAwards.surgeonId],
    references: [surgeons.id],
  }),
}));
