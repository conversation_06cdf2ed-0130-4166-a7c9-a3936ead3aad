import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, sql } from 'drizzle-orm';

import { CreateModuleDto } from './dto/create-module.dto';
import { UpdateModuleDto } from './dto/update-module.dto';

import * as schema from '@/db/schema';
import { modules, permissions } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';

@Injectable()
export class ModulesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findAll() {
    return this.drizzleDev.query.modules.findMany({
      where: eq(modules.status, 1),
    });
  }

  async findOne(id: string) {
    const module = await this.drizzleDev.query.modules.findFirst({
      where: and(eq(modules.id, id), eq(modules.status, 1)),
    });

    if (!module) throw itemNotFound(EntityName.MODULE);

    return module;
  }

  async create(createModuleDto: CreateModuleDto) {
    const existingModule = await this.drizzleDev.query.modules.findFirst({
      where: and(
        eq(sql`lower(${modules.name})`, createModuleDto.name?.toLowerCase()),
        eq(modules.status, 1),
      ),
    });

    if (existingModule) {
      throw itemAlreadyExists(EntityName.MODULE);
    }
    const [newModule] = await this.drizzleDev
      .insert(modules)
      .values({
        ...createModuleDto,
      })
      .returning();

    return newModule;
  }

  async softDelete(id: string) {
    const module = await this.drizzleDev.query.modules.findFirst({
      where: and(eq(modules.id, id), eq(modules.status, 1)),
    });

    if (!module) throw itemNotFound(EntityName.MODULE);

    const [res] = await this.drizzleDev
      .update(modules)
      .set({
        status: 0,
      })
      .where(eq(modules.id, id))
      .returning();

    return res;
  }

  async update(id: string, updateModuleDto: UpdateModuleDto) {
    const module = await this.drizzleDev.query.modules.findFirst({
      where: and(eq(modules.id, id), eq(modules.status, 1)),
    });

    if (!module) {
      throw itemNotFound(EntityName.MODULE);
    }

    const existingModuleWithSameName = await this.drizzleDev.query.modules.findFirst({
      where: and(
        eq(sql`lower(${modules.name})`, updateModuleDto.name?.toLowerCase()),
        eq(modules.status, 1),
      ),
    });

    if (existingModuleWithSameName) {
      throw itemAlreadyExists(EntityName.MODULE);
    }

    const [res] = await this.drizzleDev
      .update(modules)
      .set({
        ...updateModuleDto,
      })
      .where(eq(modules.id, id))
      .returning();

    return res;
  }

  async findByModuleId(moduleId: string) {
    const permissionList = await this.drizzleDev.query.permissions.findMany({
      where: and(eq(permissions.moduleId, moduleId), eq(permissions.status, 1)),
    });

    return permissionList;
  }
}
