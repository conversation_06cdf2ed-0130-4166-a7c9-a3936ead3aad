import { Module } from '@nestjs/common';

import { OpportunitiesService } from './opportunities.service';
import { OpportunitiesController } from './opportunities.controller';

import { OpportunitiesAttachmentsService } from '@/modules/opportunities-attachments/opportunities_attachments.service';
import { PostsModule } from '@/modules/posts/posts.module';

@Module({
  controllers: [OpportunitiesController],
  providers: [OpportunitiesService, OpportunitiesAttachmentsService],
  imports: [PostsModule],
  exports: [OpportunitiesService],
})
export class OpportunitiesModule {}
