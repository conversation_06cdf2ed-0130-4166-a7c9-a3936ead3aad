import { Inject, Injectable } from '@nestjs/common';
import { and, count, desc, eq, gt, sql, SQL } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateOpportunityDto } from './dto/create-opportunity.dto';
import { UpdateOpportunityDto } from './dto/update-opportunity.dto';
import { ApplyOpportunityDto } from './dto/apply-opportunity.dto';

import * as schema from '@/db/schema';
import {
  opportunities,
  opportunityApplications,
  opportunitiesAttachments,
  workspaces,
} from '@/db/schema';

import { OpportunitiesStatus, OpportunityTypes } from '@/constants/opportunities';
import { EntityName } from '@/constants/entities';
import { AuthConstants } from '@/constants/auth';

import { itemNotFound } from '@/exceptions/common';
import {
  alreadyAppliedForThisOpportunity,
  opportunityAlreadyExpired,
} from '@/exceptions/opportunities';

import { User } from '@/decorators/user.decorator';

@Injectable()
export class OpportunitiesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findAll(workspaceId?: string, type?: OpportunityTypes) {
    const where: SQL[] = [eq(opportunities.status, OpportunitiesStatus.ACTIVE)];

    if (workspaceId) {
      where.push(eq(opportunities.workspaceId, workspaceId));
    } else {
      where.push(gt(opportunities.expiryDate, new Date()));
    }

    if (type) {
      where.push(eq(opportunities.type, type));
    }

    const [{ rowCount }] = await this.drizzleDev
      .select({ rowCount: count() })
      .from(opportunities)
      .where(and(...where));

    const data = await this.drizzleDev
      .select({
        id: opportunities.id,
        workspaceId: opportunities.workspaceId,
        createdByUserId: opportunities.createdByUserId,
        type: opportunities.type,
        title: opportunities.title,
        shareToFeed: opportunities.shareToFeed,
        description: opportunities.description,
        shortDescription: opportunities.shortDescription,
        contactPersonName: opportunities.contactPersonName,
        email: opportunities.email,
        phone: opportunities.phone,
        websiteLink: opportunities.websiteLink,
        expiryDate: opportunities.expiryDate,
        createdAt: opportunities.createdAt,
        updatedAt: opportunities.updatedAt,
        status: opportunities.status,
        applicantCount: sql<number>`
        CAST(COUNT(DISTINCT ${opportunityApplications.userId}) AS INTEGER)
      `.as('applicant_count'),
        workspace: {
          label: workspaces.label,
          profileImageUrlThumbnail: workspaces.profileImageUrlThumbnail,
          workspacename: workspaces.workspacename,
        },
      })
      .from(opportunities)
      .leftJoin(
        opportunityApplications,
        eq(opportunities.id, opportunityApplications.opportunityId),
      )
      .leftJoin(workspaces, eq(opportunities.workspaceId, workspaces.id))
      .where(and(...where))
      .groupBy(
        opportunities.id,
        workspaces.label,
        workspaces.profileImageUrlThumbnail,
        workspaces.workspacename,
      )
      .orderBy(desc(opportunities.createdAt));

    return {
      data,
      count: rowCount,
    };
  }

  async findOne(id: string) {
    const opportunity = await this.drizzleDev
      .select({
        id: opportunities.id,
        workspaceId: opportunities.workspaceId,
        createdByUserId: opportunities.createdByUserId,
        type: opportunities.type,
        title: opportunities.title,
        shareToFeed: opportunities.shareToFeed,
        description: opportunities.description,
        shortDescription: opportunities.shortDescription,
        contactPersonName: opportunities.contactPersonName,
        email: opportunities.email,
        phone: opportunities.phone,
        websiteLink: opportunities.websiteLink,
        expiryDate: opportunities.expiryDate,
        createdAt: opportunities.createdAt,
        updatedAt: opportunities.updatedAt,
        status: opportunities.status,
        applicantCount: sql<number>`count(${opportunityApplications.userId})`.as('applicant_count'),
        workspace: {
          label: workspaces.label,
          profileImageUrlThumbnail: workspaces.profileImageUrlThumbnail,
        },
        opportunitiesAttachments,
      })
      .from(opportunities)
      .leftJoin(
        opportunityApplications,
        eq(opportunities.id, opportunityApplications.opportunityId),
      )
      .leftJoin(workspaces, eq(opportunities.workspaceId, workspaces.id))
      .leftJoin(
        opportunitiesAttachments,
        eq(opportunities.id, opportunitiesAttachments.opportunityId),
      )
      .where(and(eq(opportunities.id, id), eq(opportunities.status, OpportunitiesStatus.ACTIVE)))
      .groupBy(
        opportunities.id,
        workspaces.label,
        workspaces.profileImageUrlThumbnail,
        opportunitiesAttachments.id,
      )
      .execute();

    if (!opportunity[0]) throw itemNotFound(EntityName.OPPORTUNITY);

    return opportunity[0];
  }

  async create(
    createOpportunityDto: CreateOpportunityDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const [newOpportunity] = await dbOrTransaction
      .insert(opportunities)
      .values({
        workspaceId,
        createdByUserId: userId,
        ...createOpportunityDto,
      })
      .returning();

    return newOpportunity;
  }

  async update(
    id: string,
    workspaceId: string,
    updateOpportunityDto: UpdateOpportunityDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const [res] = await dbOrTransaction
      .update(opportunities)
      .set({
        ...updateOpportunityDto,
      })
      .where(
        and(
          eq(opportunities.status, OpportunitiesStatus.ACTIVE),
          eq(opportunities.workspaceId, workspaceId),
          eq(opportunities.id, id),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.OPPORTUNITY);

    return res;
  }

  async softDelete(
    id: string,
    workspaceId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const [res] = await db
      .update(opportunities)
      .set({
        status: OpportunitiesStatus.INACTIVE,
      })
      .where(
        and(
          eq(opportunities.id, id),
          eq(opportunities.workspaceId, workspaceId),
          eq(opportunities.status, OpportunitiesStatus.ACTIVE),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.OPPORTUNITY);

    return res;
  }

  async applyForOpportunity(
    applyOpportunityDto: ApplyOpportunityDto,
    opportunityId: string,
    userId: string,
  ) {
    // Check if opportunity exists and is active
    const opportunity = await this.findOne(opportunityId);

    // Validate expiry date
    if (opportunity.expiryDate && new Date(opportunity.expiryDate) < new Date()) {
      throw opportunityAlreadyExpired;
    }

    // Check if user has already applied
    const existingApplication = await this.drizzleDev.query.opportunityApplications.findFirst({
      where: and(
        eq(opportunityApplications.opportunityId, opportunityId),
        eq(opportunityApplications.userId, userId),
      ),
    });

    if (existingApplication) {
      throw alreadyAppliedForThisOpportunity;
    }

    // Create application
    return this.drizzleDev.insert(opportunityApplications).values({
      opportunityId,
      userId,
      ...applyOpportunityDto,
    });
  }

  getApplicantsOfAnOpportunity(opportunityId: string) {
    return this.drizzleDev.query.opportunityApplications.findMany({
      where: eq(opportunityApplications.opportunityId, opportunityId),
    });
  }
}
