import {
  IsEmail,
  IsNotEmpty,
  IsDate,
  IsString,
  IsUrl,
  ValidationOptions,
  registerDecorator,
  ValidationArguments,
  // Validate,
  IsArray,
  ValidateNested,
  ArrayMaxSize,
  IsOptional,
  IsEnum,
  IsBoolean,
  // MinDate,
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { OpportunityTypes } from '@/constants/opportunities';
import { OpportunitiesAttachments } from '@/constants/opportunities-attachments';
import { CreateOpportunitiesAttachmentDto } from '@/modules/opportunities-attachments/dto/create-opportunities-attachment.dto';

export function IsFutureDate(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'IsFutureDate',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          // Check if the value is a valid date
          if (!(value instanceof Date) || isNaN(value.getTime())) return false;

          const currentDate = new Date();
          // Ensure the value is greater than the current date and time
          return value > currentDate;
        },
        defaultMessage(args: ValidationArguments) {
          // Custom error message
          return `${args.property} must be a future date and time.`;
        },
      },
    });
  };
}

export class CreateOpportunityDto {
  @ApiProperty({
    name: 'type',
    type: 'string',
    required: true,
    example: OpportunityTypes.CERTIFICATIONS,
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsEnum(OpportunityTypes)
  type: OpportunityTypes;

  @ApiProperty({
    name: 'title',
    type: 'string',
    required: true,
    example: 'Software Developer Opportunity',
  })
  @IsString()
  @Transform(({ value }: TransformFnParams) => (typeof value === 'string' ? value.trim() : value))
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    name: 'description',
    type: 'string',
    required: true,
    example: 'We are looking for a skilled software developer...',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  description: string;

  @ApiProperty({
    name: 'shortDescription',
    type: 'string',
    required: true,
    example: 'Software Developer Job Opportunity at Acme Corp.',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  shortDescription: string;

  @ApiProperty({
    name: 'contactPersonName',
    type: 'string',
    required: true,
    example: 'John Doe',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  contactPersonName: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email: string;

  @ApiProperty({
    name: 'phone',
    type: 'string',
    required: true,
    example: '******-456-7890',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phone: string;

  @ApiProperty({
    name: 'websiteLink',
    type: 'string',
    required: true,
    example: 'https://www.acme.com',
  })
  @IsUrl()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  websiteLink: string;

  @ApiProperty({
    name: 'shareToFeed',
    type: 'boolean',
    required: true,
    example: true,
  })
  @IsBoolean()
  @IsNotEmpty()
  shareToFeed: boolean;

  @ApiProperty({
    name: 'expiryDate',
    type: 'date',
    required: true,
    example: '2024-09-30T23:59:59Z',
  })
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  @IsFutureDate()
  expiryDate: Date;

  @ApiProperty({
    name: 'uploadDetails',
    type: [CreateOpportunitiesAttachmentDto],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateOpportunitiesAttachmentDto)
  @ArrayMaxSize(OpportunitiesAttachments.MAX_NO_OF_FILES, {
    message: 'You can upload a maximum of 5 files.',
  })
  uploadDetails: CreateOpportunitiesAttachmentDto[];
}
