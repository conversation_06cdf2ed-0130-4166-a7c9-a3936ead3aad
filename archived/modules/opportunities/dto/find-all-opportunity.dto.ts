import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';

import { OpportunityTypes } from '@/constants/opportunities';

export class FindAllOpportunitiesDto {
  @ApiPropertyOptional({
    description: 'The opportunity type',
    example: OpportunityTypes.CERTIFICATIONS,
  })
  @IsOptional()
  @IsEnum(OpportunityTypes, {
    message: `type must be one of the following values: ${Object.values(OpportunityTypes).join(', ')}`,
  })
  type?: OpportunityTypes;

  @ApiPropertyOptional({
    description: 'The workspace Id',
  })
  @IsOptional()
  @IsUUID()
  workspaceId?: string;
}
