import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Inject,
  Query,
  ParseEnumPipe,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { OpportunitiesService } from './opportunities.service';

import { CreateOpportunityDto } from './dto/create-opportunity.dto';
import { UpdateOpportunityDto } from './dto/update-opportunity.dto';
import { FindAllOpportunitiesDto } from './dto/find-all-opportunity.dto';
import { ApplyOpportunityDto } from './dto/apply-opportunity.dto';

import { OpportunityTypes } from '@/constants/opportunities';
import { AuthConstants } from '@/constants/auth';

import { User } from '@/decorators/user.decorator';
import { ActiveWorkspace } from '@/decorators/active-workspace.decorator';
import { Public } from '@/decorators/public.decorator';

import { OpportunitiesAttachmentsService } from '@/modules/opportunities-attachments/opportunities_attachments.service';
import { PostsService } from '@/modules/posts/posts.service';

import * as schema from '@/db/schema';

@Controller('opportunities')
@ApiBearerAuth()
@ApiTags('opportunities')
export class OpportunitiesController {
  constructor(
    private readonly opportunityAttachmentService: OpportunitiesAttachmentsService,
    private readonly opportunitiesService: OpportunitiesService,
    private readonly postsService: PostsService,
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
  ) {}

  @Post()
  @ActiveWorkspace()
  async create(
    @Body() createOpportunityDto: CreateOpportunityDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const newOpportunity = await this.opportunitiesService.create(
        createOpportunityDto,
        workspaceId,
        userId,
        txn,
      );

      if (createOpportunityDto.uploadDetails && createOpportunityDto.uploadDetails.length > 0) {
        await this.opportunityAttachmentService.createOrUpdateMultipleAttachment(
          newOpportunity.id,
          createOpportunityDto.uploadDetails,
          txn,
          true,
        );
      }

      if (newOpportunity.shareToFeed) {
        await this.postsService.addPostOfOpportuntiy(newOpportunity.id, userId, workspaceId, txn);
      }

      return newOpportunity;
    });
  }

  @Get('own')
  findAllOwnWorkspace(
    @Query() query: FindAllOpportunitiesDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { type } = query;
    return this.opportunitiesService.findAll(workspaceId, type);
  }

  @Get()
  findAllOpporutnites(@Query() query: FindAllOpportunitiesDto) {
    const { type, workspaceId } = query;
    return this.opportunitiesService.findAll(workspaceId, type);
  }

  @Get('type/:type')
  @Public()
  getOpportunitiesByType(
    @Param('type', new ParseEnumPipe(OpportunityTypes)) type: OpportunityTypes,
  ) {
    return this.opportunitiesService.findAll(undefined, type);
  }

  @Post('apply/:opportunityId')
  applyForOpportunity(
    @Body() applyOpportunityDto: ApplyOpportunityDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Param('opportunityId') opportunityId: string,
  ) {
    return this.opportunitiesService.applyForOpportunity(
      applyOpportunityDto,
      opportunityId,
      userId,
    );
  }

  @Get('apply/:opportunityId')
  async getApplicantsOfAnOpportunity(
    @Param('opportunityId') opportunityId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const opportunityDeatils = await this.opportunitiesService.findOne(opportunityId);
    if (opportunityDeatils.workspaceId !== workspaceId) throw new Error("you don't have permision");

    return this.opportunitiesService.getApplicantsOfAnOpportunity(opportunityId);
  }

  @Get(':id')
  @Public()
  findOne(@Param('id') id: string) {
    return this.opportunitiesService.findOne(id);
  }

  @Patch(':opportunityId')
  @ActiveWorkspace()
  update(
    @Param('opportunityId') opportunityId: string,
    @Body() updateOpportunityDto: UpdateOpportunityDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const newOpportunity = await this.opportunitiesService.update(
        opportunityId,
        workspaceId,
        updateOpportunityDto,
        txn,
      );

      if (updateOpportunityDto.uploadDetails && updateOpportunityDto.uploadDetails.length > 0) {
        await this.opportunityAttachmentService.createOrUpdateMultipleAttachment(
          newOpportunity.id,
          updateOpportunityDto.uploadDetails,
          txn,
        );
      }

      if (typeof updateOpportunityDto.shareToFeed !== 'undefined') {
        // if have to share the opportunity to feed
        await (updateOpportunityDto.shareToFeed
          ? this.postsService.addPostOfOpportuntiy(
              opportunityId,
              newOpportunity.createdByUserId,
              workspaceId,
              txn,
            )
          : this.postsService.removePostOfOpportuntiy(opportunityId, txn));
      }

      return newOpportunity;
    });
  }

  @Delete(':opportunityId')
  @ActiveWorkspace()
  remove(
    @Param('opportunityId') opportunityId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      await this.postsService.removePostOfOpportuntiy(opportunityId, txn);

      return this.opportunitiesService.softDelete(opportunityId, workspaceId, txn);
    });
  }
}
