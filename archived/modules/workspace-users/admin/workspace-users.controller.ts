import { Controller, Get, Post, Body, Delete, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { WorkspaceUsersService } from './workspace-users.service';
import { CreateWorkspaceUserDto } from './dto/create-workspace-users.dto';

@Controller('workspace-users')
@ApiBearerAuth()
@ApiTags('workspace users')
export class WorkspaceUsersController {
  constructor(private readonly workspaceUsersService: WorkspaceUsersService) {}

  @Post()
  create(@Body() createWorkspaceDto: CreateWorkspaceUserDto) {
    return this.workspaceUsersService.create(createWorkspaceDto);
  }

  @Get()
  findAll() {
    return this.workspaceUsersService.findAll();
  }

  @Delete(':workspaceId/:userId')
  async remove(@Param('userId') userId: string, @Param('workspaceId') workspaceId: string) {
    return this.workspaceUsersService.softDelete(userId, workspaceId);
  }
}
