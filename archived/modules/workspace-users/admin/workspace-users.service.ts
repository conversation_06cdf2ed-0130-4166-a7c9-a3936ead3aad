import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateWorkspaceUserDto } from './dto/create-workspace-users.dto';

import * as schema from '@/db/schema';
import { workspaceUsers } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';

@Injectable()
export class WorkspaceUsersService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(createWorkspaceUserDto: CreateWorkspaceUserDto) {
    const workspaceUser = await this.drizzleDev.query.workspaceUsers.findFirst({
      where: and(
        eq(workspaceUsers.workspaceId, createWorkspaceUserDto.workspaceId),
        eq(workspaceUsers.userId, createWorkspaceUserDto.userId),
      ),
    });

    if (workspaceUser) throw itemAlreadyExists(EntityName.WORKSPACE);

    const [newOrgUser] = await this.drizzleDev
      .insert(workspaceUsers)
      .values({
        ...createWorkspaceUserDto,
      })
      .returning();

    return newOrgUser;
  }

  async findAll() {
    return this.drizzleDev.query.workspaceUsers.findMany({
      where: eq(workspaceUsers.status, 1),
    });
  }

  async softDelete(userId: string, workspaceId: string) {
    const [res] = await this.drizzleDev
      .update(workspaceUsers)
      .set({
        status: 0,
      })
      .where(
        and(
          eq(workspaceUsers.workspaceId, workspaceId),
          eq(workspaceUsers.userId, userId),
          eq(workspaceUsers.status, 1),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.WORKSPACE_USER);

    return res;
  }
}
