import { Modu<PERSON> } from '@nestjs/common';

import { WorkspaceUsersService } from './workspace-users.service';
import { WorkspaceUsersController } from './workspace-users.controller';

import { AuthModule } from '@/modules/auth/auth.module';
import { UsersModule } from '@/modules/users/users.module';
import { RolesModule } from '@/modules/roles/roles.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { MailModule } from '@/common/mail/mail.module';

import { CustomConfigService } from '@/config/configuration.service';

@Module({
  controllers: [WorkspaceUsersController],
  providers: [WorkspaceUsersService, CustomConfigService],
  imports: [AuthModule, UsersModule, RolesModule, UserRolesModule, MailModule],
  exports: [WorkspaceUsersService],
})
export class WorkspaceUsersModule {}
