import { Inject, Injectable } from '@nestjs/common';
import { and, eq, ne, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateWorkspaceUserDto } from '../admin/dto/create-workspace-users.dto';

import { itemAlreadyExists } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { WorkspaceUsersStatus } from '@/constants/workspaces';

import * as schema from '@/db/schema';
import { workspaceUsers, users, roles, userRoles } from '@/db/schema';
// import { CreateUserDto } from './dto/create-workspace-users.dto';
// import { UpdateWorkspaceUserDto } from './dto/update-workspace-users.dto';
// import { UsersService } from '@/modules/users/users.service';

@Injectable()
export class WorkspaceUsersService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(
    createWorkspaceUserDto: CreateWorkspaceUserDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const workspaceUser = await dbOrTransaction.query.workspaceUsers.findFirst({
      where: and(
        eq(workspaceUsers.workspaceId, createWorkspaceUserDto.workspaceId),
        eq(workspaceUsers.userId, createWorkspaceUserDto.userId),
      ),
    });

    if (workspaceUser) throw itemAlreadyExists(EntityName.WORKSPACE_USER);

    const [newWorkspaceUser] = await dbOrTransaction
      .insert(workspaceUsers)
      .values({
        ...createWorkspaceUserDto,
      })
      .returning();

    return newWorkspaceUser;
  }

  getWorkspaceUsers(workspaceId: string) {
    return this.drizzleDev
      .select({
        id: users.id,
        fullName: sql<string>`trim(concat(
          ${users.firstName}, 
          ' ', 
          coalesce(${users.middleName} || ' ', ''), 
          ${users.lastName}
        ))`,
        email: users.email,
        role: sql<string>`(
          SELECT ${roles.key}
          FROM ${userRoles}
          LEFT JOIN ${roles} ON ${roles.id} = ${userRoles.roleId}
          WHERE ${userRoles.userId} = ${users.id}
          AND ${userRoles.workspaceId} = ${workspaceId}
          AND ${userRoles.active} = true
          AND ${userRoles.archived} = false
          LIMIT 1
        )`,
        status: workspaceUsers.status,
      })
      .from(workspaceUsers)
      .leftJoin(users, eq(workspaceUsers.userId, users.id))
      .where(
        and(
          eq(workspaceUsers.workspaceId, workspaceId),
          ne(workspaceUsers.status, WorkspaceUsersStatus.ARCHIVED),
        ),
      );
  }
  // This method handles updating the user's details in an organization
  // async update(id: string, updateOrganizationUserDto: UpdateWorkspaceUserDto) {
  //   // Logic for updating the user in the organization
  //   return `This action updates a #${id} organization user`;
  // }
  // // Helper method to create a user
  // private async createUser(userDto: CreateUserWithWorkspaceDto['user']) {
  //   // Logic to create a user in your database
  //   return { id: 'generated-user-id', ...userDto }; // Example return value
  // }
}
