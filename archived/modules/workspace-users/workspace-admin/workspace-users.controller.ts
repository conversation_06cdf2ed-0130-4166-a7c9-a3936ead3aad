import { Controller, Post, Body, Inject, Get } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';

import { WorkspaceUsersService } from './workspace-users.service';

import { CreateWorkspaceUserDto } from './dto/create-workspace-users.dto';

import { User } from '@/decorators/user.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import { Roles, WorkspaceRoles } from '@/constants/user-types';
import { AuthConstants } from '@/constants/auth';
import { EntityName } from '@/constants/entities';
import { GUEST_USER_PRE_DEFINED_VAL } from '@/constants/users';

import { getWorkspaceRoleFromRoleAndLevel } from '@/utils/user-roles';

import { itemNotFound } from '@/exceptions/common';

import { AuthService } from '@/modules/auth/auth.service';
import { UsersService } from '@/modules/users/users.service';
import { RolesService } from '@/modules/roles/roles.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';

import { CustomConfigService } from '@/config/configuration.service';

import { MailService } from '@/common/mail/mail.service';

@ApiTags('org-admin/workspace-users')
@ApiBearerAuth()
@Controller('org-admin/workspace-users')
export class WorkspaceUsersController {
  constructor(
    private readonly workspaceUsersService: WorkspaceUsersService,
    private readonly authService: AuthService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
    private readonly usersService: UsersService,
    private readonly customConfigService: CustomConfigService,
    private readonly mailService: MailService,
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
  ) {}

  @Post()
  @UserRoles(Roles.ORGANISATION_ADMIN)
  async create(
    @Body() userData: CreateWorkspaceUserDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_ROLE) workspaceRole: WorkspaceRoles,
  ) {
    const { email, roleLevel } = userData;

    return this.drizzleDev.transaction(async (tx) => {
      let userDetails = await this.usersService.findUserByEmail(userData.email, tx);

      const workspaceRoleToAssociate = getWorkspaceRoleFromRoleAndLevel(workspaceRole, roleLevel);
      if (!workspaceRoleToAssociate) throw itemNotFound(EntityName.ROLE);

      if (!userDetails) {
        const firebaseStatus =
          await this.authService.checkFirebaseUserRegistrationAndVerification(email);

        // Create a basic user record
        const newUser = await this.authService.createUserRecord(
          {
            email,
            firstName: GUEST_USER_PRE_DEFINED_VAL.firstName,
            lastName: GUEST_USER_PRE_DEFINED_VAL.lastName,
            providerId: firebaseStatus.userUid ?? '',
          },
          undefined,
          tx,
        );

        // Get role details
        const roleDetails = await this.rolesService.findRoleByKey(workspaceRoleToAssociate);
        if (!roleDetails) throw new Error(EntityName.ROLE);

        // Assign workspace role
        await this.userRolesService.createUserRole(
          {
            userId: newUser.id,
            roleId: roleDetails?.id,
            workspaceId,
          },
          tx,
        );

        // Get complete user with permissions
        userDetails = await this.usersService.findUserById(newUser.id, tx);
        if (!userDetails) throw itemNotFound(EntityName.USER);

        // Set Firebase claims
        await this.authService.setUserFirebaseClaims(userDetails);

        // Send invitation email
        const resetLink = await getAuth().generatePasswordResetLink(newUser.email, {
          url: this.customConfigService.getFrontendHost() + '/auth/firebase/sign-in',
        });

        await this.mailService.sendUserAssignedEmail(newUser.email, {
          fullName: `${newUser.firstName} ${newUser.lastName}`,
          workspaceName: workspaceId, // TODO: we might want to get the actual workspace name
          resetLink,
        });
      } else {
        // User exists, just assign the role
        const roleDetails = await this.rolesService.findRoleByKey(workspaceRoleToAssociate);
        if (!roleDetails) throw new Error(EntityName.ROLE);

        await this.userRolesService.createUserRole(
          { userId: userDetails.id, roleId: roleDetails?.id, workspaceId },
          tx,
        );
      }

      // Create workspace user association
      await this.workspaceUsersService.create({ workspaceId, userId: userDetails.id }, tx);

      return userDetails;
    });
  }

  @Get()
  findAll(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    return this.workspaceUsersService.getWorkspaceUsers(workspaceId);
  }
}
