import { IsEmail, <PERSON>E<PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { WorkspaceRoleLevel } from '@/constants/workspaces';

export class CreateWorkspaceUserDto {
  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    name: 'roleLevel',
    enum: WorkspaceRoleLevel,
    required: true,
    example: WorkspaceRoleLevel.USER,
  })
  @IsNotEmpty()
  @IsEnum(WorkspaceRoleLevel)
  roleLevel: WorkspaceRoleLevel;
}
