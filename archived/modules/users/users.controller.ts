import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { CreateUserDto } from './dto/create-user.dto';
import { BaseUserUpdateDto, UpdateUserDto } from './dto/update-user.dto';

import { User } from '@/db/schema';

import { UsersService } from './users.service';

import { AuthConstants } from '@/constants/auth';
import { EntityName } from '@/constants/entities';
import { Roles } from '@/constants/user-types';
import { Permissions } from '@/constants/permissions';

import { itemNotFound } from '@/exceptions/common';

import { Permissions as PermissionsDec } from '@/decorators/permissions.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';
import { User as UserDec } from '@/decorators/user.decorator';
import { Public } from '@/decorators/public.decorator';

@Controller('users')
@ApiTags('users')
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Create a new user
   * @param createUserDto - The data for creating a new user
   * @returns The created user
   */
  @Post()
  @UserRoles(Roles.SUPER_ADMIN)
  @PermissionsDec(Permissions.CREATE_USER)
  async createUser(@Body() createUserDto: CreateUserDto) {
    return this.usersService.createUser(createUserDto);
  }

  /**
   * Retrieve all users
   * @returns An array of all users
   */
  @Get()
  @PermissionsDec(Permissions.VIEW_USERS)
  findAllUsers(): Promise<User[]> {
    return this.usersService.findAllUsers();
  }

  /**
   * Retrieve own user details
   * @userDec userId - The userId of the user from the user object set on req.user
   * @returns An user details
   */
  @Get('profile')
  async findUserOwn(@UserDec(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string) {
    const user = await this.usersService.findUserById(userId);
    if (!user) throw itemNotFound(EntityName.USER);

    return user;
  }

  /**
   * Update own user details
   * @userDec userId - The userId of the user from the user object set on req.user
   * @param updateUserDto - The data to update the user with
   * @returns updated user details
   */
  @Post('profile')
  updateUserOwn(
    @UserDec(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Body() updateUserDto: BaseUserUpdateDto,
  ) {
    return this.usersService.updateUser(userId, updateUserDto);
  }

  @Get(':id')
  @PermissionsDec(Permissions.VIEW_USERS)
  async findUserById(@Param('id') id: string) {
    if (!id) throw new BadRequestException('User ID is required');
    const user = await this.usersService.findUserById(id);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Find a user by their username
   * @param username - The username of the user to find
   * @returns The user with the specified username
   * @throws NotFoundException if user is not found
   */
  @Public()
  @Get('username/:username')
  async findUserByUsername(@Param('username') username: string) {
    const user = await this.usersService.findUserByUsername(username);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Find a user by their email
   * @param email - The email of the user to find
   * @returns The user with the specified email
   * @throws BadRequestException if email is not provided
   * @throws NotFoundException if user is not found
   */
  @Get(':email')
  @UserRoles(Roles.SUPER_ADMIN)
  @PermissionsDec(Permissions.VIEW_USERS)
  async findUserByEmail(@Param('email') email: string) {
    if (!email) throw new BadRequestException('User email is required');
    const user = await this.usersService.findUserByEmail(email);
    if (!user) throw itemNotFound(EntityName.USER);
    return user;
  }

  /**
   * Update a user's information
   * @param id - The ID of the user to update
   * @param updateUserDto - The data to update the user with
   * @returns The updated user
   */
  @Patch(':id')
  @PermissionsDec(Permissions.UPDATE_USER)
  updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.updateUser(id, updateUserDto);
  }

  /**
   * Remove a user
   * @param id - The ID of the user to remove
   * @returns The removed user
   */
  @Delete(':id')
  @PermissionsDec(Permissions.DELETE_USER)
  removeUser(@Param('id') id: string): Promise<User> {
    return this.usersService.removeUser(id);
  }
}
