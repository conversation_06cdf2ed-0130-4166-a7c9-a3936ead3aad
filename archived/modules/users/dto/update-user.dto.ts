import { IsOptional, IsString, IsUUID, IsEmail } from 'class-validator';
import { ApiProperty, PartialType } from '@nestjs/swagger';

import { CreateUserDto } from './create-user.dto';

// Base DTO with common fields
export class BaseUserUpdateDto {
  @ApiProperty({
    required: false,
    example: '<PERSON>',
    description: "User's first name",
  })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty({
    required: false,
    example: '<PERSON>',
    description: "User's middle name",
  })
  @IsString()
  @IsOptional()
  middleName?: string;

  @ApiProperty({
    required: false,
    example: '<PERSON>',
    description: "User's last name",
  })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty({
    required: false,
    example: 'Senior Software Engineer',
    description: "User's job title",
  })
  @IsString()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({
    required: false,
    example: 'a dev, who is developing something',
    description: 'about user',
  })
  @IsString()
  @IsOptional()
  about?: string;

  @ApiProperty({
    required: false,
    example: '23e4567-e89b-12d3-a456-************',
    description: "User's country identifier",
  })
  @IsUUID()
  @IsOptional()
  countryId?: string;

  @ApiProperty({
    required: false,
    example: '123e4567-e89b-12d3-a456-************',
    description: "User's subtype identifier",
  })
  @IsUUID()
  @IsOptional()
  subtypeId?: string;

  @ApiProperty({
    required: false,
    example: '<EMAIL>',
    description: "User's email address",
  })
  @IsEmail()
  @IsOptional()
  email?: string;
}

export class UpdateUserDto extends PartialType(CreateUserDto) {}
