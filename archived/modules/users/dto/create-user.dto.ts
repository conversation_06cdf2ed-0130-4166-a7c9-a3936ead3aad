import {
  <PERSON><PERSON><PERSON>,
  IsS<PERSON>,
  IsNot<PERSON>mpty,
  IsO<PERSON>al,
  IsUUID,
  IsArray,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

// export class OrganizationRoleDto {
//   @ApiProperty({
//     description: 'Organization ID',
//     example: '123e4567-e89b-12d3-a456-************',
//   })
//   @IsUUID()
//   organisationId: string;

//   @ApiProperty({
//     description: 'Role ID for the user in this organization',
//     example: '123e4567-e89b-12d3-a456-************',
//   })
//   @IsUUID()
//   roleId: string;

//   @ApiProperty({
//     description: 'Additional permission IDs for the user in this organization',
//     required: false,
//     example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
//     type: [String],
//   })
//   @IsArray()
//   @IsUUID(4, { each: true })
//   @IsOptional()
//   additionalPermissionIds?: string[];
// }

export class GlobalRoleDto {
  @ApiProperty({
    description: 'Global role ID for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'permission ids for the role',
    required: false,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  permissionIds: string[];
}

export class WorkspaceRoleDto {
  @ApiProperty({
    description: 'Workspace role ID for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'Workspace ID for the role',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  workspaceId: string;

  @ApiProperty({
    description: 'permission ids for the role',
    required: false,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  permissionIds: string[];
}

export class CreateUserDto {
  @ApiProperty({
    name: 'firstName',
    type: 'string',
    required: true,
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  firstName: string;

  @ApiProperty({
    name: 'middleName',
    type: 'string',
    required: false,
    example: 'Doe',
  })
  @IsString()
  @IsOptional()
  middleName?: string;

  @ApiProperty({
    name: 'lastName',
    type: 'string',
    required: true,
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    name: 'globalRoles',
    required: true,
    type: [GlobalRoleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GlobalRoleDto)
  @IsOptional()
  @ArrayMinSize(1)
  globalRoles: GlobalRoleDto[];

  @ApiProperty({
    name: 'workspaceRoles',
    required: true,
    type: [WorkspaceRoleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkspaceRoleDto)
  @IsOptional()
  workspaceRoles: WorkspaceRoleDto[];
}
