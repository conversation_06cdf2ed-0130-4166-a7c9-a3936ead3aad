import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, ne, SQL } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';
import { users, userPermissions, userRoles } from '@/db/schema';

import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { emailRegisteredNotVerified } from '@/exceptions/auth';

import { EntityName } from '@/constants/entities';
import { Roles } from '@/constants/user-types';
import { Permissions } from '@/constants/permissions';
import { UserStatus } from '@/constants/users';
import { UserRolesStatus } from '@/constants/user-roles';
import { UserPermissionStatus } from '@/constants/user-permissions';

import { GlobalPermission, WorkspacePermission } from '@/interfaces/user';

import { getFullName } from '@/utils/users';

import { MailService } from '@/common/mail/mail.service';

import { CustomConfigService } from '@/config/configuration.service';

import { AuthService } from '@/modules/auth/auth.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';

@Injectable()
export class UsersService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly customConfigService: CustomConfigService,
    private readonly mailService: MailService,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    private readonly userRolesService: UserRolesService,
  ) {}

  async createUser(createUserDto: CreateUserDto, transaction?: PostgresJsDatabase<typeof schema>) {
    const dbOrTransaction = transaction || this.drizzleDev;
    const { firstName, middleName, lastName, email, globalRoles, workspaceRoles } = createUserDto;

    // Reuse the Firebase check from AuthService
    const firebaseStatus =
      await this.authService.checkFirebaseUserRegistrationAndVerification(email);

    if (firebaseStatus.isRegistered) {
      throw firebaseStatus.isVerified
        ? itemAlreadyExists(EntityName.USER)
        : emailRegisteredNotVerified();
    }

    return dbOrTransaction.transaction(async (tx) => {
      try {
        // Use the centralized user creation method
        const newUser = await this.authService.createUserRecord(
          {
            ...createUserDto,
            providerId: firebaseStatus.userUid ?? '',
          },
          undefined,
          tx,
        );

        // Process global roles if provided
        for (const globalRole of globalRoles) {
          await this.userRolesService.createUserRole(
            {
              userId: newUser.id,
              roleId: globalRole.roleId,
              permissionIds: globalRole.permissionIds,
            },
            tx,
          );
        }

        // Process workspace roles if provided
        for (const workspaceRole of workspaceRoles) {
          await this.userRolesService.createUserRole(
            {
              userId: newUser.id,
              roleId: workspaceRole.roleId,
              workspaceId: workspaceRole.workspaceId,
              permissionIds: workspaceRole.permissionIds,
            },
            tx,
          );
        }

        // Fetch the user with their permissions
        const user = await this.findUserById(newUser.id, tx);

        // Use the centralized method to set Firebase claims
        await this.authService.setUserFirebaseClaims(user);

        // Generate password reset link
        const resetLink = await getAuth().generatePasswordResetLink(newUser.email, {
          url: this.customConfigService.getFrontendHost() + '/auth/firebase/sign-in',
        });

        // Send email with temporary password and reset link
        await this.mailService.sendUserCreatedEmail(newUser.email, {
          fullName: firstName + (middleName ? ' ' + middleName : '') + ' ' + lastName,
          resetLink,
        });

        return user;
      } catch (error) {
        throw error;
      }
    });
  }

  async findAllUsers() {
    return this.drizzleDev.query.users.findMany({
      where: eq(users.status, 1),
    });
  }

  findUserById(id: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.id, id), txn);
  }

  findUserByUsername(username: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.username, username), txn);
  }

  findUserByEmail(email: string, txn?: PostgresJsDatabase<typeof schema>) {
    return this.findUserByCondition(eq(users.email, email), txn);
  }

  private async findUserByCondition(condition: SQL, txn?: PostgresJsDatabase<typeof schema>) {
    const dbOrTransaction = txn || this.drizzleDev;

    //TODO: ENSURE THAT THE JOINING TABLE HAS ACTIVE STATUS
    const user = await dbOrTransaction.query.users.findFirst({
      where: and(condition, eq(users.status, 1)),
      with: {
        userRoles: {
          where: eq(userRoles.status, UserRolesStatus.ACTIVE),
          columns: {
            id: true,
          },
          with: {
            role: {
              columns: {
                name: true,
                key: true,
              },
            },
            userPermissions: {
              where: eq(userPermissions.status, UserPermissionStatus.ACTIVE),
              with: {
                permission: {
                  columns: {
                    key: true,
                  },
                },
              },
              columns: {},
            },
            workspaces: {
              columns: {
                id: true,
                label: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!user) return null;

    // Define the structure of the updated format
    type UpdatedFormat = Omit<typeof user, 'userRoles'> & {
      permissions: {
        global: GlobalPermission[];
        workspace: WorkspacePermission[];
      };
      userRoles?: (typeof user)['userRoles']; // Dynamically reference the type from user
    };

    const updatedFormat: UpdatedFormat = {
      ...user,
      permissions: {
        global: [] as GlobalPermission[], // Explicit type for global roles
        workspace: [] as WorkspacePermission[], // Explicit type for workspace roles
      },
    };

    // Process the roles and permissions
    user?.userRoles?.forEach((userRole) => {
      const roleKey = userRole.role.key as Roles;
      const roleName = userRole.role.name;
      const permissions = userRole.userPermissions.map(
        (permission) => permission.permission.key as Permissions,
      );

      if (!userRole.workspaces?.id) {
        // It's a global role, add to the global section
        updatedFormat.permissions.global.push({
          id: userRole.id,
          name: getFullName(user),
          roleName,
          roleKey,
          permissions,
        });
      } else {
        // It's a workspace role, add to the workspace section
        updatedFormat.permissions.workspace.push({
          id: userRole.id,
          workspaceId: userRole.workspaces?.id ?? '',
          name: userRole.workspaces?.label,
          status: userRole.workspaces?.status,
          roleName,
          roleKey,
          permissions,
        });
      }
    });

    delete updatedFormat.userRoles;

    return updatedFormat;
  }

  async updateUser(userId: string, updateUserDto: UpdateUserDto) {
    return this.drizzleDev.transaction(async (tx) => {
      const { email, globalRoles, workspaceRoles } = updateUserDto;
      const user = await this.findUserById(userId, tx);

      if (!user) throw itemNotFound(EntityName.USER);

      // Check if email is being updated and verify it's not already in use
      if (email && email !== user.email) {
        const userExist = await this.findUserByEmail(email, tx);
        if (userExist && userExist.id !== user.id) {
          throw itemAlreadyExists(EntityName.USER);
        }
      }

      // Update user details
      await tx
        .update(users)
        .set({
          ...user,
          ...updateUserDto,
        })
        .where(eq(users.id, userId))
        .returning();

      // Handle email update in Firebase if applicable
      if (email && email !== user.email) {
        await getAuth().updateUser(user.providerId, {
          email,
          emailVerified: false,
        });

        const link = await getAuth().generateEmailVerificationLink(email, {
          url: this.customConfigService.getFrontendHost() + '/auth/firebase/sign-in',
        });
        await this.mailService.sendVerificationEmail(email, link);
      }

      // Update global roles if provided in the DTO (even if empty array)
      if (globalRoles) {
        await this.userRolesService.updateUserRoles(userId, globalRoles, {
          transaction: tx,
          isGlobal: true,
        });
      }

      // Update workspace roles if provided in the DTO (even if empty array)
      if (workspaceRoles) {
        await this.userRolesService.updateUserRoles(userId, workspaceRoles, {
          transaction: tx,
          isGlobal: false,
        });
      }

      // Fetch updated user with all permissions
      const updatedUserWithPermissions = await this.findUserById(userId, tx);

      // Update Firebase claims
      await this.authService.setUserFirebaseClaims(updatedUserWithPermissions);

      return updatedUserWithPermissions;
    });
  }

  async removeUser(id: string) {
    const [deletedUser] = await this.drizzleDev
      .update(users)
      .set({
        status: UserStatus.ACTIVE,
      })
      .where(and(eq(users.id, id), ne(users.status, UserStatus.ARCHIVED)))
      .returning();

    if (!deletedUser) throw itemNotFound(EntityName.USER);

    return deletedUser;
  }
}
