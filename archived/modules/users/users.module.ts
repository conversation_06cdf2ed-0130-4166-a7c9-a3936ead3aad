import { Module, forwardRef } from '@nestjs/common';

import { UsersService } from './users.service';
import { UsersController } from './users.controller';

import { CustomConfigService } from '@/config/configuration.service';

import { MailModule } from '@/common/mail/mail.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';

@Module({
  imports: [MailModule, UserRolesModule, forwardRef(() => AuthModule), UserRolesModule],
  controllers: [UsersController],
  providers: [UsersService, CustomConfigService],
  exports: [UsersService],
})
export class UsersModule {}
