import { Module, forwardRef } from '@nestjs/common';

import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';

import { UsersModule } from '@/modules/users/users.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { RolesModule } from '@/modules/roles/roles.module';
import { SubtypesModule } from '@/modules/subtypes/subtypes.module';
import { MailModule } from '@/common/mail/mail.module';

import { CustomConfigService } from '@/config/configuration.service';

@Module({
  controllers: [AuthController],
  providers: [AuthService, CustomConfigService],
  imports: [
    forwardRef(() => UsersModule),
    UserRolesModule,
    RolesModule,
    MailModule,
    SubtypesModule,
  ],
  exports: [AuthService],
})
export class AuthModule {}
