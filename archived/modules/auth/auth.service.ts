import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { getAuth } from 'firebase-admin/auth';

import { UserData, authUserData } from '@/interfaces/auth';

import { CreateUserRegisterDto } from './dto/create-register.dto';

import * as schema from '@/db/schema';
import { users, NewUser } from '@/db/schema';

import { verifyToken } from '@/helpers/auth.helpers';

import { UserRolesService } from '@/modules/user-roles/user-roles.service';
import { RolesService } from '@/modules/roles/roles.service';
import { SubtypesService } from '@/modules/subtypes/subtypes.service';
import { UsersService } from '@/modules/users/users.service';

import { Roles } from '@/constants/user-types';
import { AuthConstants } from '@/constants/auth';
import { EntityName } from '@/constants/entities';
import { SUBTYPES } from '@/constants/subtypes';

import { MailService } from '@/common/mail/mail.service';

import { CustomConfigService } from '@/config/configuration.service';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { emailRegisteredNotVerified } from '@/exceptions/auth';

@Injectable()
export class AuthService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly userRolesService: UserRolesService,
    private readonly subtypesService: SubtypesService,
    private readonly rolesService: RolesService,
    private readonly mailService: MailService,
    private readonly customConfigService: CustomConfigService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
  ) {}

  async checkFirebaseUserRegistrationAndVerification(email: string) {
    try {
      // Attempt to retrieve the user from Firebase
      const user = await getAuth().getUserByEmail(email);

      const isRegistered = Boolean(user?.customClaims?.[AuthConstants.FIREBASE_CLAIM_USER_ID]);
      const isVerified = isRegistered && user.emailVerified;

      return {
        userUid: user.uid,
        isRegistered,
        isVerified,
      };
    } catch (error) {
      // Handle only "user not found" errors gracefully
      if (error.code === 'auth/user-not-found') {
        return {
          userUid: '',
          isRegistered: false,
          isVerified: false,
        };
      }

      // Re-throw other errors
      throw error;
    }
  }

  // Core functionality for creating a Firebase user
  async createFirebaseUser(
    email: string,
    name?: string,
    password?: string,
    emailVerified = false,
  ): Promise<string> {
    const firebaseUser = await getAuth().createUser({
      email,
      displayName: name,
      password,
      emailVerified,
    });

    return firebaseUser.uid;
  }

  // Centralized method to set Firebase custom claims
  async setUserFirebaseClaims(user: any) {
    if (!user) throw itemNotFound(EntityName.USER);

    const { permissions, roleKey, workspaceId } =
      user.permissions.workspace[0] ?? user.permissions.global[0];

    const additionalClaims: UserData = {
      [AuthConstants.FIREBASE_CLAIM_USER_ID]: user.id,
      [AuthConstants.FIREBASE_CLAIM_EMAIL]: user.email,
      [AuthConstants.FIREBASE_CLAIM_ROLE]: roleKey,
      [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: permissions,
      [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: workspaceId,
      [AuthConstants.FIREBASE_SUBS_PAGE_DISPLAY]: workspaceId ? false : true,
      [AuthConstants.FIREBASE_SUBS_ALERT_BANNER]: workspaceId ? false : true,
    };

    await getAuth().setCustomUserClaims(user.providerId, additionalClaims);
    return additionalClaims;
  }

  // Core method for creating a user in your database
  async createUserRecord(
    userDetails: Omit<NewUser, 'username'>,
    password?: string,
    txn?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = txn || this.drizzleDev;
    const { firstName, lastName, email, middleName, providerId } = userDetails;

    try {
      const firebaseUid = Boolean(providerId)
        ? providerId
        : await this.createFirebaseUser(
            email,
            firstName + (middleName ? ' ' + middleName : '') + ' ' + lastName,
            password,
          );

      // Generate UUID first
      const userId = crypto.randomUUID();

      // Generate username using the UUID
      const cleanedUsername =
        `${firstName
          .trim()
          .replace(/[^a-zA-Z0-9_-]/g, '')
          .toLowerCase()}` +
        `-${lastName
          .trim()
          .replace(/[^a-zA-Z0-9_-]/g, '')
          .toLowerCase()}`;

      const username = `${cleanedUsername}-${userId}`;

      const [insertedUser] = await db
        .insert(users)
        .values({
          ...userDetails,
          id: userId,
          username,
          providerId: firebaseUid,
        })
        .returning();

      return insertedUser;
    } catch (error) {
      throw error;
    }
  }

  // Unified method for public user registration
  async registerUser(
    createRegisterDto: CreateUserRegisterDto,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;
    const { email, token, password } = createRegisterDto;

    // Check Firebase status
    const firebaseStatus = await this.checkFirebaseUserRegistrationAndVerification(email);

    if (firebaseStatus.isRegistered) {
      throw firebaseStatus.isVerified
        ? itemAlreadyExists(EntityName.USER)
        : emailRegisteredNotVerified();
    }

    let providerId: string = '';

    try {
      return await dbOrTransaction.transaction(async (txn) => {
        // Get public subtypes
        const publicSubtypeDetails = await this.subtypesService.findOne(
          undefined,
          SUBTYPES.GeneralPublic.Public.label,
        );

        // Create user
        const newUser = await this.createUserRecord(
          {
            ...createRegisterDto,
            subtypeId: publicSubtypeDetails!.id,
            providerId: token
              ? (await verifyToken<authUserData>(token)).uid!
              : (firebaseStatus.userUid ?? ''),
          },
          token ? undefined : password,
          txn,
        );

        providerId = newUser.providerId;

        // Assign default public role
        const roleDetails = await this.rolesService.findRoleByKey(Roles.GENERAL_PUBLIC);
        if (!roleDetails) throw new Error(EntityName.ROLE);

        await this.userRolesService.createUserRole(
          { userId: newUser.id, roleId: roleDetails?.id },
          txn,
        );

        // Get complete user with permissions
        const user = await this.usersService.findUserById(newUser.id, txn);

        // Set Firebase claims
        await this.setUserFirebaseClaims(user);

        // Send verification email if not using token
        if (!token) {
          const link = await getAuth().generateEmailVerificationLink(newUser.email, {
            url: this.customConfigService.getFrontendHost() + '/auth/firebase/sign-in',
          });
          await this.mailService.sendVerificationEmail(newUser.email, link);
        }

        return user;
      });
    } catch (error) {
      if (providerId) {
        await getAuth().deleteUser(providerId);
      }
      throw error;
    }
  }
}
