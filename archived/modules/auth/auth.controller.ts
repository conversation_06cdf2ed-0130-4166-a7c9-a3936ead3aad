import { <PERSON>, <PERSON>, Body, Param, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { getAuth } from 'firebase-admin/auth';
import { CookieOptions, Response } from 'express';

import { CreateUserRegisterDto } from './dto/create-register.dto';
import { SessionLoginDto } from './dto/session-login.dto';

import { AuthService } from './auth.service';

import { forbidden } from '@/exceptions/system';
import { recentSignInRequired, userNoWorkspaceAccess } from '@/exceptions/auth';

import { CookieKey } from '@/constants/auth';

import { Public } from '@/decorators/public.decorator';
import { User } from '@/decorators/user.decorator';
import { SessionOnly } from '@/decorators/session-only.decorator';
import { TokenOnly } from '@/decorators/token-only.decorator';

import { UsersService } from '@/modules/users/users.service';

import { UserData } from '@/interfaces/auth';

import { CustomConfigService } from '@/config/configuration.service';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly usersSerivce: UsersService,
    private readonly customConfigService: CustomConfigService,
  ) {}

  @Post('register')
  @Public()
  async create(@Body() createRegisterDto: CreateUserRegisterDto) {
    return this.authService.registerUser(createRegisterDto);
  }

  @Post('claims/:workspaceId')
  async updateCustomClaims(@Param('workspaceId') workspaceId: string, @User() user: UserData) {
    const { userId, uid } = user;

    const userDetails = await this.usersSerivce.findUserById(userId);

    if (!userDetails) return forbidden(); // If no user found, return forbidden

    // Check if the user has access to the provided workspaceId
    const workspaceAccess = userDetails.permissions.workspace.find(
      (workspace) => workspace.workspaceId === workspaceId,
    );

    if (!workspaceAccess) {
      throw userNoWorkspaceAccess;
    }

    // If the user has access, proceed to update the custom claims
    await getAuth().setCustomUserClaims(uid!, {
      workspaceId,
      roleKey: workspaceAccess.roleKey,
      permissions: workspaceAccess.permissions, // Set the permissions for the workspace
    });

    return true;
  }

  @Public()
  @Post('session-login')
  async login(@Res({ passthrough: true }) res: Response, @Body() sessionLoginDto: SessionLoginDto) {
    // Verify Firebase ID token
    const { idToken } = sessionLoginDto;
    const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days

    const decodedToken = await getAuth().verifyIdToken(idToken, true);

    const currentTime = new Date().getTime() / 1000;
    if (currentTime - decodedToken.auth_time > 5 * 60) {
      throw recentSignInRequired;
    }

    // Create session cookie
    const sessionCookie = await getAuth().createSessionCookie(idToken, { expiresIn });

    const { isLocal } = this.customConfigService.getEnvironment();

    // Set cookie options
    const options: CookieOptions = {
      maxAge: expiresIn,
      httpOnly: true,
      path: '/',
      secure: isLocal === false,
      sameSite: 'lax',
      domain: this.customConfigService.getAppConfig()?.hostDomain,
    };

    res.cookie(CookieKey, sessionCookie, options);
  }

  @SessionOnly()
  @Post('verify-session')
  async checkAuthStatus(@Res({ passthrough: true }) res: Response, @User() user: UserData) {
    try {
      // Generate custom token for cross-domain auth
      const customToken = await getAuth().createCustomToken(user.uid!);

      return { customToken };
    } catch (error) {
      // Clear invalid cookie
      res.clearCookie(CookieKey, {
        domain: this.customConfigService.getAppConfig()?.hostDomain,
      });
      throw new Error(error);
    }
  }

  @TokenOnly()
  @Post('logout')
  async logout(@Res({ passthrough: true }) res: Response, @User() user: UserData) {
    // Revoke all refresh tokens
    await getAuth().revokeRefreshTokens(user.uid!);

    // Clear session cookie
    res.clearCookie(CookieKey, {
      domain: this.customConfigService.getAppConfig()?.hostDomain,
    });
  }
}
