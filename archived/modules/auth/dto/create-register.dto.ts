import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, ValidateIf } from 'class-validator';

export class CreateUserRegisterDto {
  @ApiProperty({
    name: 'firstName',
    type: 'string',
    required: true,
    example: 'jose',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  firstName: string;

  @ApiProperty({
    name: 'lastName',
    type: 'string',
    required: true,
    example: 'santos',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  lastName: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    name: 'password',
    type: 'string',
    required: false,
    example: 'abcefg',
    minimum: 6,
    description: 'if there is a token, the password will be ignored',
  })
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => !o.token)
  password?: string;

  @ApiProperty({
    name: 'token',
    type: 'string',
    required: false,
    example:
      'eyJhbGciOiJSUzI1NiIsImtpZCI6IjI3YTc1ZjhkNmQ5ODZmNTA1MzVjOTdjMTliMjA2MzcxNzMwNDA3MTgiLCJ0eXAiOiJKV1QifQ...',
    description: 'if there is a token, the password will be ignored',
  })
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => !o.password)
  token?: string;
}
