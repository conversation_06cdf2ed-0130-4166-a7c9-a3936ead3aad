import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { countries, continents, roles, modules } from '@/db/schema';

import { RoleStatus } from '@/constants/roles';
import { ModulesStatus } from '@/constants/modules';
import { ContinentsStatus } from '@/constants/continents';
import { CountriesStatus } from '@/constants/countries';
import { OpportunityTypes } from '@/constants/opportunities';

@Injectable()
export class DropDownService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async getCountries() {
    const countriesList = await this.drizzleDev.query.countries.findMany({
      where: eq(countries.status, CountriesStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
        code: true,
      },
    });
    return countriesList;
  }

  async getContinents() {
    const continentsList = await this.drizzleDev.query.continents.findMany({
      where: eq(continents.status, ContinentsStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return continentsList;
  }

  async getRoles() {
    const rolesList = await this.drizzleDev.query.roles.findMany({
      where: eq(roles.status, RoleStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return rolesList;
  }

  async getModules() {
    const modulesList = await this.drizzleDev.query.modules.findMany({
      where: eq(modules.status, ModulesStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return modulesList;
  }

  async getOpportunities() {
    return Object.values(OpportunityTypes);
  }
}
