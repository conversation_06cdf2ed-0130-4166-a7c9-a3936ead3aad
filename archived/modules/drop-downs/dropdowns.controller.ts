import { <PERSON>, Get, Param, ParseEnumPipe } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { DropDownService } from './dropdowns.service';

import { Public } from '@/decorators/public.decorator';

import { AccountType } from '@/constants/user-types';

import { SubtypesService } from '@/modules/subtypes/subtypes.service';

@Controller('drop-downs')
@ApiBearerAuth()
@ApiTags('drop-downs')
export class DropDownController {
  constructor(
    private readonly dropDownService: DropDownService,
    private readonly subtypesService: SubtypesService,
  ) {}

  @Get('/countries')
  getCountries() {
    return this.dropDownService.getCountries();
  }

  @Get('/continents')
  getContinents() {
    return this.dropDownService.getContinents();
  }

  @Get('/roles')
  getRoles() {
    return this.dropDownService.getRoles();
  }

  @Get('/modules')
  getModules() {
    return this.dropDownService.getModules();
  }

  @Public()
  @Get('/subtypes/:accountType')
  getSubtypes(@Param('accountType', new ParseEnumPipe(AccountType)) accountType: AccountType) {
    return this.subtypesService.getSubtypes({ accountType });
  }

  @Get('/opportunities')
  getOpportunities() {
    return this.dropDownService.getOpportunities();
  }
}
