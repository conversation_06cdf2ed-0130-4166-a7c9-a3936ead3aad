import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { User } from '@/decorators/user.decorator';

import { AuthConstants } from '@/constants/auth';

import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { UpdateSubscriptionStatusDto } from './dto/update-subscription-status.dto';
import { GetSubscriptionsQueryDto } from './dto/get-subscriptions.dto';

import { SubscriptionsService } from './subscriptions.service';

@Controller('subscriptions')
@ApiBearerAuth()
@ApiTags('subscriptions')
export class SubscriptionsController {
  constructor(private readonly subscriptionsService: SubscriptionsService) {}

  @Get()
  getSubscriptionDetails(@Query() query: GetSubscriptionsQueryDto) {
    return this.subscriptionsService.findSubscriptionsByWorkspaceTypeAndBillingCycle(query);
  }

  @Post()
  createSubscription(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ) {
    return this.subscriptionsService.createSubscripton(createSubscriptionDto, userId, firebaseUid);
  }

  @Post('update-status')
  updateSubscriptionStatus(
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @Body() updateSubscriptionStatus: UpdateSubscriptionStatusDto,
  ) {
    return this.subscriptionsService.updateClaims(firebaseUid, updateSubscriptionStatus);
  }
}
