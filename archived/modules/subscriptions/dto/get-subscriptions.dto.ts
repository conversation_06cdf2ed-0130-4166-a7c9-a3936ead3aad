import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';

import { WorkspaceType } from '@/constants/workspaces';
import { BillingCycle } from '@/constants/subscriptions';

export class GetSubscriptionsQueryDto {
  @ApiProperty({
    name: 'workspaceType',
    description: 'The specific workspace type',
    enum: WorkspaceType,
    example: WorkspaceType.ORGANISATION,
  })
  @IsEnum(WorkspaceType, {
    message: `the workspace type must be one of the following values: ${Object.values(WorkspaceType).join(', ')}`,
  })
  workspaceType: WorkspaceType;

  @ApiProperty({
    name: 'billingCycle',
    description: 'The billing cycle type',
    enum: BillingCycle,
    example: BillingCycle.YEARLY,
  })
  @IsEnum(BillingCycle, {
    message: `billing cycle must be one of the following values: ${Object.values(BillingCycle).join(', ')}`,
  })
  billingCycle: BillingCycle;
}
