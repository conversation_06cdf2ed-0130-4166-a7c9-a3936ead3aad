import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, IsUUID, ValidateIf } from 'class-validator';

import { WorkspaceType } from '@/constants/workspaces';

export class CreateSubscriptionDto {
  @ApiProperty({
    name: 'subtypeId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  subtypeId: string;

  @ApiProperty({
    name: 'subscriptionPlanId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  subscriptionPlanId: string;

  @ApiProperty({
    name: 'orgName',
    type: 'string',
    required: false,
    example: 'Heart Hospital',
    description: `required if usertype is ${WorkspaceType.ORGANISATION}`,
  })
  @IsString()
  @IsNotEmpty({
    message: `organisation name is required if choose ${WorkspaceType.ORGANISATION}`,
  })
  @ValidateIf((o) => o.userType === WorkspaceType.ORGANISATION)
  orgName?: string;

  @ApiProperty({
    name: 'type',
    enum: [Object.values(WorkspaceType)],
    required: true,
    example: WorkspaceType.ORGANISATION,
    description: `account type must be one of ${Object.values(WorkspaceType)}`,
  })
  @IsEnum(WorkspaceType, {
    message: `account type must be one of ${Object.values(WorkspaceType)}`,
  })
  type: WorkspaceType;
}
