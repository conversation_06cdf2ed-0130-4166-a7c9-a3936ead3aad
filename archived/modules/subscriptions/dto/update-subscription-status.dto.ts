import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, ValidateIf, IsNotEmpty, IsOptional } from 'class-validator';

export class UpdateSubscriptionStatusDto {
  @ApiProperty({
    name: 'subsPageDisplay',
    type: 'boolean',
    required: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  subsPageDisplay?: boolean;

  @ApiProperty({
    name: 'subsAlertBanner',
    type: 'string',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  subsAlertBanner?: boolean;

  @ValidateIf(
    (dto: UpdateSubscriptionStatusDto) =>
      dto.subsPageDisplay !== undefined && dto.subsAlertBanner !== undefined,
  )
  @IsNotEmpty({ message: 'At least one of subsPageDisplay or subsAlertBanner must be provided.' })
  atLeastOneField?: boolean;
}
