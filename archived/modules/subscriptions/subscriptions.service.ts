import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, sql } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';
import { subscriptions, subscriptionCountries, subscriptionWorkspaces } from '@/db/schema';

import { SubscriptionCountriesStatus } from '@/constants/subscription-countries';
import { Roles } from '@/constants/user-types';
import { AuthConstants } from '@/constants/auth';
import { WorkspaceType, WorkspaceRoleLevel } from '@/constants/workspaces';
import { EntityName } from '@/constants/entities';
import { BillingCycle, SubscriptionsStatus } from '@/constants/subscriptions';

import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { UpdateSubscriptionStatusDto } from './dto/update-subscription-status.dto';

import { WorkspaceUsersService } from '@/modules/workspace-users/workspace-admin/workspace-users.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';
import { UsersService } from '@/modules/users/users.service';
import { RolesService } from '@/modules/roles/roles.service';

import { itemNotFound } from '@/exceptions/common';

import { UserData } from '@/interfaces/auth';

import { getWorkspaceRole } from '@/utils/user-roles';
import { calculateEndDateByMultiplyingWithMonthCount } from '@/utils/subscriptions';

@Injectable()
export class SubscriptionsService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly workspacesService: WorkspacesService,
    private readonly workspaceUsersService: WorkspaceUsersService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
    private readonly usersService: UsersService,
  ) {}

  findSubscriptionsByWorkspaceTypeAndBillingCycle({
    workspaceType,
    billingCycle,
  }: {
    workspaceType: WorkspaceType;
    billingCycle: BillingCycle;
  }) {
    return this.drizzleDev.query.subscriptions.findMany({
      where: and(
        eq(subscriptions.workspaceType, workspaceType),
        eq(subscriptions.billingCycle, billingCycle),
        eq(subscriptions.status, SubscriptionsStatus.ACTIVE),
      ),
      orderBy: (sub, { asc }) => [asc(sub.amount)],
    });
  }

  async findSubscriptionById(subscriptionId: string) {
    return this.drizzleDev.query.subscriptions.findFirst({
      where: and(
        eq(subscriptions.id, subscriptionId),
        eq(subscriptions.status, SubscriptionsStatus.ACTIVE),
      ),
    });
  }

  async findSubscriptionDetailsWithWorkspaceId(workspaceId: string) {
    const subscriptionDetails = await this.drizzleDev
      .select({
        id: subscriptionWorkspaces.id,
        paymentDate: subscriptionWorkspaces.paymentDate,
        paymentMethod: subscriptionWorkspaces.paymentMethod,
        startDate: subscriptionWorkspaces.startDate,
        endDate: subscriptionWorkspaces.endDate,
        countryIds: sql<string[]>`array_agg(${subscriptionCountries.countryId})`,
      })
      .from(subscriptionWorkspaces)
      .leftJoin(
        subscriptionCountries,
        eq(subscriptionCountries.subscriptionWorkspaceId, subscriptionWorkspaces.id),
      )
      .where(
        and(
          eq(subscriptionWorkspaces.workspaceId, workspaceId),
          eq(subscriptionCountries.status, SubscriptionCountriesStatus.ACTIVE),
        ),
      )
      .groupBy(subscriptionWorkspaces.id)
      .limit(1);

    return subscriptionDetails[0];
  }

  async createSubscription(workspaceId: string) {
    const subscriptionDetails = await this.drizzleDev
      .select({
        id: subscriptionWorkspaces.id,
        paymentDate: subscriptionWorkspaces.paymentDate,
        paymentMethod: subscriptionWorkspaces.paymentMethod,
        startDate: subscriptionWorkspaces.startDate,
        endDate: subscriptionWorkspaces.endDate,
        countryIds: sql<string[]>`array_agg(${subscriptionCountries.countryId})`,
      })
      .from(subscriptionWorkspaces)
      .leftJoin(
        subscriptionCountries,
        eq(subscriptionCountries.subscriptionWorkspaceId, subscriptionWorkspaces.id),
      )
      .where(
        and(
          eq(subscriptionWorkspaces.workspaceId, workspaceId),
          eq(subscriptionCountries.status, SubscriptionCountriesStatus.ACTIVE),
        ),
      )
      .groupBy(subscriptionWorkspaces.id)
      .limit(1);

    return subscriptionDetails[0];
  }

  async createSubscripton(
    createSubscriptionDto: CreateSubscriptionDto,
    userId: string,
    firebaseUid: string,
  ) {
    const { type, subscriptionPlanId } = createSubscriptionDto;
    return this.drizzleDev.transaction(async (txn) => {
      const subscriptionDetails = await this.findSubscriptionById(subscriptionPlanId);

      if (!subscriptionDetails || subscriptionDetails.workspaceType !== type) {
        throw itemNotFound(EntityName.SUBSCRIPTION);
      }

      //if type except public, then only make the workspace
      const newWorkspace = await this.workspacesService.create(createSubscriptionDto, userId, txn);

      const subscriptionWorkspacesData = {
        workspaceId: newWorkspace.id as any,
        subscriptionId: subscriptionPlanId as any,
        // TODO: UPDATE BELOW DETAILS
        paymentMethod: 'free',
        endDate: calculateEndDateByMultiplyingWithMonthCount(
          subscriptionDetails.maxDurationMonths!,
        ).toLocaleString(),
        paymentDate: new Date().toLocaleString(),
        startDate: new Date().toLocaleString(),
        status: SubscriptionsStatus.ACTIVE,
      };

      await txn.insert(subscriptionWorkspaces).values(subscriptionWorkspacesData);

      await this.workspaceUsersService.create({ workspaceId: newWorkspace.id, userId }, txn);

      const workspaceAdminRoleKey = getWorkspaceRole(type, WorkspaceRoleLevel.ADMIN);

      const role = await this.rolesService.findRoleByKey(workspaceAdminRoleKey);
      if (!role) throw itemNotFound(workspaceAdminRoleKey);

      // Create user role and associate it
      await this.userRolesService.createUserRole(
        { userId, roleId: role.id, workspaceId: newWorkspace.id },
        txn,
      );

      const user = await this.usersService.findUserById(userId, txn);
      if (!user) throw itemNotFound(EntityName.USER);

      const workspaceRole = role?.key as Roles;
      const workspacePermissions = role
        ? user.permissions.workspace.find((works) => works.workspaceId === newWorkspace.id)!
            .permissions
        : [];

      // here please check exisitng, and

      const userRecord = await getAuth().getUser(firebaseUid);
      const currentClaims: UserData = userRecord.customClaims as UserData;

      // workspace details
      const newWorkspaceDetails = {
        [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: newWorkspace?.id,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: workspaceRole,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: workspacePermissions,
      };

      // Merge the new claims with the existing ones
      const updatedClaims = {
        ...currentClaims, // Existing claims
        ...newWorkspaceDetails,
        [AuthConstants.FIREBASE_SUBS_PAGE_DISPLAY]: false,
        [AuthConstants.FIREBASE_SUBS_ALERT_BANNER]: false,
      };

      // Update the user’s claims in Firebase
      await getAuth().setCustomUserClaims(firebaseUid, updatedClaims);

      return newWorkspaceDetails;
    });
  }

  async updateClaims(
    firebaseUid: string,
    updateSubscriptionStatusDto: UpdateSubscriptionStatusDto,
  ) {
    const user = await getAuth().getUser(firebaseUid);
    const existingClaims = user.customClaims || {};

    const updatedClaims = {
      ...existingClaims,
      ...(updateSubscriptionStatusDto.subsPageDisplay !== undefined && {
        subsPageDisplay: updateSubscriptionStatusDto.subsPageDisplay,
      }),
      ...(updateSubscriptionStatusDto.subsAlertBanner !== undefined && {
        subsAlertBanner: updateSubscriptionStatusDto.subsAlertBanner,
      }),
    };

    await getAuth().setCustomUserClaims(firebaseUid, updatedClaims);
    return true;
  }
}
