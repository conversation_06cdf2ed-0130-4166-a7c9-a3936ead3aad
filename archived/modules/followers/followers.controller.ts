import { Controller, Get, Post, Body, Param, Delete, Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateFollowerDto } from './dto/create-follower.dto';

import * as schema from '@/db/schema';

import { AuthConstants } from '@/constants/auth';
import { EntityType } from '@/constants/user-types';
import { EntityName } from '@/constants/entities';

import { User } from '@/decorators/user.decorator';

import { itemNotFound } from '@/exceptions/common';
import { selfFollowNotAllowed } from '@/exceptions/followers';

import { NotificationsService } from '../notifications/notifications.service';
import { FollowersService } from './followers.service';
import { WorkspacesService } from '../workspaces/workspaces.service';
import { UsersService } from '../users/users.service';

@Controller('followers')
export class FollowersController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly followersService: FollowersService,
    private readonly notificationService: NotificationsService,
    private readonly workspacesService: WorkspacesService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  async createFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() createFollowerDto: CreateFollowerDto,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      if (workspaceId === createFollowerDto.workspaceId) throw selfFollowNotAllowed();

      const isExist = await (workspaceId
        ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
        : this.usersService.findUserById(userId));

      if (!isExist) throw itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

      const workspaceFollowersData = await this.followersService.createFollower(
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        createFollowerDto,
      );

      await this.notificationService.createFollowerNotification(
        {
          entityId: workspaceId ?? userId,
          entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
          followedId: createFollowerDto.workspaceId,
          isRead: false,
        },
        txn,
      );

      return workspaceFollowersData;
    });
  }

  @Get('follow-stats')
  findOwnFollowStatsOfAWorkspace(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) currUserWorkspaceId: string,
  ) {
    return this.followersService.findFollowStatsOfAWorkspaceById(currUserWorkspaceId);
  }

  @Get('follow-stats/:workspaceId')
  findFollowStatsOfAWorkspace(
    @Param('workspaceId') workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) currUserWorkspaceId: string,
  ) {
    return this.followersService.findFollowStatsOfAWorkspaceById(
      workspaceId,
      currUserWorkspaceId ?? userId,
    );
  }

  @Get('following/:entityId')
  findAllFollowing(@Param('entityId') entityId: string) {
    return this.followersService.findAllFollowingForId(entityId);
  }

  @Get('followers')
  findAllOwnWorkspaceFollowers(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.followersService.findAllFollowersForId(workspaceId);
  }

  @Get('followers/:workspaceId')
  findAllFollowersOfAWorkspaceById(@Param('workspaceId') workspaceId: string) {
    return this.followersService.findAllFollowersForId(workspaceId);
  }

  @Get('following/:workspaceId')
  findiFUserFollowingParticularId(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Param('workspaceId') workspaceIdInParam: string,
  ) {
    return this.followersService.isUserOrWorkspaceFollowingParticularWorkspaceById(
      workspaceId ?? userId,
      workspaceIdInParam,
    );
  }

  @Delete(':workspaceId')
  removeWorkSpaceFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Param('workspaceId') wokrspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const followData = await this.followersService.softDeleteWorkspaceFollower(
        workspaceId ?? userId,
        wokrspaceId,
        txn,
      );

      await this.notificationService.invalidateFollowNotification(
        {
          entityId: workspaceId ?? userId,
          followedId: wokrspaceId,
        },
        txn,
      );

      return followData;
    });
  }
}
