import { Inject, Injectable } from '@nestjs/common';
// import { CreateUserFollowerDto } from './dto/create-user-follower.dto';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, count, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { workspaceFollowers } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { FollowerStatus } from '@/constants/followers';
import { EntityType } from '@/constants/user-types';

import { CreateFollowerDto } from './dto/create-follower.dto';

@Injectable()
export class FollowersService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createFollower(
    entityId: string,
    entityType: EntityType,
    createFollowerDto: CreateFollowerDto,
  ) {
    const workspaceFollower = await this.drizzleDev.query.workspaceFollowers.findFirst({
      where: and(
        eq(workspaceFollowers.entityId, entityId),
        eq(workspaceFollowers.workspaceId, createFollowerDto.workspaceId),
      ),
    });

    if (workspaceFollower) {
      if (workspaceFollower.status === FollowerStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.FOLLOWER);
      } else if (workspaceFollower.status === FollowerStatus.INACTIVE) {
        const [updatedWorkspaceFollower] = await this.drizzleDev
          .update(workspaceFollowers)
          .set({ status: FollowerStatus.ACTIVE })
          .where(
            and(
              eq(workspaceFollowers.entityId, entityId),
              eq(workspaceFollowers.workspaceId, createFollowerDto.workspaceId),
            ),
          )
          .returning();

        return updatedWorkspaceFollower;
      }
    }

    const [newWorkspaceFollower] = await this.drizzleDev
      .insert(workspaceFollowers)
      .values({
        ...createFollowerDto,
        entityId,
        entityType,
        status: FollowerStatus.ACTIVE,
      })
      .returning();

    return newWorkspaceFollower;
  }

  async findFollowStatsOfAWorkspaceById(workspaceId: string, entityId?: string) {
    const [{ followingCount }] = await this.drizzleDev
      .select({ followingCount: count() })
      .from(workspaceFollowers)
      .where(
        and(
          eq(workspaceFollowers.entityId, workspaceId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      );

    const [{ followersCount }] = await this.drizzleDev
      .select({ followersCount: count() })
      .from(workspaceFollowers)
      .where(
        and(
          eq(workspaceFollowers.workspaceId, workspaceId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      );

    const response: { followersCount: number; followingCount: number; isFollowing?: boolean } = {
      followersCount,
      followingCount,
    };

    if (entityId) {
      const isExist = await this.drizzleDev.query.workspaceFollowers.findFirst({
        where: and(
          eq(workspaceFollowers.entityId, entityId),
          eq(workspaceFollowers.workspaceId, workspaceId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      });

      if (isExist) response.isFollowing = true;
    }

    return response;
  }

  async findAllFollowingForId(entityId: string) {
    return this.drizzleDev.query.workspaceFollowers.findMany({
      where: and(
        eq(workspaceFollowers.entityId, entityId),
        eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
      ),
      columns: {},
      // eslint-disable-next-line @typescript-eslint/no-shadow
      orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
      with: {
        workspace: {
          columns: {
            id: true,
            type: true,
            workspacename: true,
            label: true,
          },
        },
      },
    });
  }

  findAllFollowersForId(workspaceId: string) {
    return this.drizzleDev.query.workspaceFollowers.findMany({
      where: and(
        eq(workspaceFollowers.workspaceId, workspaceId),
        eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
      ),
      columns: {},
      // eslint-disable-next-line @typescript-eslint/no-shadow
      orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
      with: {
        workspace: {
          columns: {
            id: true,
            type: true,
            workspacename: true,
            label: true,
          },
        },
      },
    });
  }

  async isUserOrWorkspaceFollowingParticularWorkspaceById(entityId: string, workspaceId: string) {
    const workspaceFollowerQuery = await this.drizzleDev.query.workspaceFollowers.findFirst({
      where: and(
        eq(workspaceFollowers.entityId, entityId),
        eq(workspaceFollowers.workspaceId, workspaceId),
        eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
      ),
    });

    if (workspaceFollowerQuery) return true;

    return false;
  }

  async softDeleteWorkspaceFollower(
    entityId: string,
    workspaceId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const [deletedWorkspaceFollower] = await db
      .update(workspaceFollowers)
      .set({ status: FollowerStatus.INACTIVE })
      .where(
        and(
          eq(workspaceFollowers.entityId, entityId),
          eq(workspaceFollowers.workspaceId, workspaceId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      )
      .returning();

    if (!deletedWorkspaceFollower) throw itemNotFound(EntityName.FOLLOWER);

    return deletedWorkspaceFollower;
  }
}
