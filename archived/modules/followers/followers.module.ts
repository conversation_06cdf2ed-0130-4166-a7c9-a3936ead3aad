import { Modu<PERSON> } from '@nestjs/common';
import { FollowersService } from './followers.service';
import { FollowersController } from './followers.controller';
import { NotificationsService } from '../notifications/notifications.service';
import { WorkspacesModule } from '../workspaces/workspaces.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [WorkspacesModule, UsersModule],
  controllers: [FollowersController],
  providers: [FollowersService, NotificationsService],
  exports: [FollowersService],
})
export class FollowersModule {}
