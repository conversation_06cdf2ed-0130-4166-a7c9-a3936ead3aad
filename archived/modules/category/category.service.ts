import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { categories } from '@/db/schema';

import { CreateCategoryDto } from './dto/create-category.dto';

import { CategoriesStatus } from '@/constants/categories';
import { EntityName } from '@/constants/entities';

import { itemAlreadyExists } from '@/exceptions/common';

@Injectable()
export class CategoryService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findOne(id: string) {
    return this.drizzleDev.query.categories.findFirst({
      where: and(eq(categories.id, id), eq(categories.status, CategoriesStatus.ACTIVE)),
    });
  }

  async create(createSubtypeDto: CreateCategoryDto) {
    const existingCategory = await this.drizzleDev.query.categories.findFirst({
      where: eq(sql`LOWER(${categories.name})`, createSubtypeDto.name),
    });

    if (existingCategory) {
      if (existingCategory.status === CategoriesStatus.INACTIVE) {
        await this.drizzleDev
          .update(categories)
          .set({ status: CategoriesStatus.ACTIVE })
          .where(eq(categories.id, existingCategory.id));

        return { ...existingCategory, status: CategoriesStatus.ACTIVE };
      } else {
        throw itemAlreadyExists(EntityName.CATEGORY);
      }
    } else {
      // Insert new tag if not found
      const [newCategory] = await this.drizzleDev
        .insert(categories)
        .values(createSubtypeDto)
        .returning();

      return newCategory;
    }
  }
}
