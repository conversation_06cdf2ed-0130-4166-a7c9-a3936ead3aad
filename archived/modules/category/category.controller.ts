import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { CreateCategoryDto } from './dto/create-category.dto';

import { CategoryService } from './category.service';

@Controller('category')
@ApiBearerAuth()
@ApiTags('category')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.categoryService.findOne(id);
  }

  @Post()
  create(@Body() createSubtypeDto: CreateCategoryDto) {
    return this.categoryService.create(createSubtypeDto);
  }
}
