import { Inject, Injectable, NotAcceptableException } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { SQL, and, desc, eq, inArray, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  postNotifications,
  NewPostNotifications,
  postMedias,
  users,
  posts,
  postComments,
  followersNotifications,
  NewFollowersNotifications,
  workspaceFollowers,
  workspaces,
} from '@/db/schema';

import { PostNotificationsStatus, PostNotificationsType } from '@/constants/post-notifications';
import { FollowersNotificationsStatus } from '@/constants/followers-notifications';
import { PostActiveStatus, PostStatus } from '@/constants/posts';
import { UserStatus } from '@/constants/users';
import { PostMediaStatus } from '@/constants/post-media';
import { WorkspacesStatus } from '@/constants/workspaces';
import { EntityType } from '@/constants/user-types';

@Injectable()
export class NotificationsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  // Create post notification
  async createPostNotification(
    data: NewPostNotifications,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;
    return db.insert(postNotifications).values(data).returning();
  }

  async createLikeNotification(
    data: NewPostNotifications,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    await this.invalidatePostNotification(
      { entityId: data.entityId, postId: data.postId, type: PostNotificationsType.LIKE },
      transaction,
    );
    return this.createPostNotification(data, transaction);
  }

  async createCommentNotification(
    data: NewPostNotifications,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    return this.createPostNotification(data, transaction);
  }

  // Create follower notification
  async createFollowerNotification(
    data: NewFollowersNotifications,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    await this.invalidateFollowNotification(
      { entityId: data.entityId, followedId: data.followedId },
      transaction,
    );
    const db = transaction || this.drizzleDev;
    return db.insert(followersNotifications).values(data).returning();
  }

  // Find a single post notification
  findOnePostNotification(notificationId: string) {
    return this.drizzleDev.query.postNotifications.findFirst({
      where: and(
        eq(postNotifications.id, notificationId),
        eq(postNotifications.status, PostNotificationsStatus.ACTIVE),
      ),
      with: {
        post: { columns: { workspaceId: true } },
      },
    });
  }

  // Find a single follower notification
  findOneFollowerNotification(notificationId: string) {
    return this.drizzleDev.query.followersNotifications.findFirst({
      where: and(
        eq(followersNotifications.id, notificationId),
        eq(followersNotifications.status, FollowersNotificationsStatus.ACTIVE),
      ),
      with: {
        followed: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            middleName: true,
          },
        },
      },
    });
  }

  async findAllNotifications(workspaceId: string, isUnreadOnly?: boolean) {
    const postWhere: SQL[] = [
      eq(posts.workspaceId, workspaceId),
      eq(posts.status, PostActiveStatus.ACTIVE),
      eq(postNotifications.status, PostNotificationsStatus.ACTIVE),
      eq(posts.postStatus, PostStatus.PUBLISHED),
      sql`(
        (${postNotifications.entityType} = ${EntityType.USER} AND EXISTS(
          SELECT 1 FROM ${users} 
          WHERE ${users.id} = ${postNotifications.entityId} 
          AND ${users.status} = ${UserStatus.ACTIVE}
        )) 
        OR 
        (${postNotifications.entityType} = ${EntityType.WORKSPACE} AND EXISTS(
          SELECT 1 FROM ${workspaces} 
          WHERE ${workspaces.id} = ${postNotifications.entityId} 
          AND ${workspaces.status} = ${WorkspacesStatus.ACTIVE}
        ))
      )`,
    ];

    const followerWhere: SQL[] = [
      eq(followersNotifications.followedId, workspaceId),
      eq(followersNotifications.status, FollowersNotificationsStatus.ACTIVE),
      sql`(
        (${followersNotifications.entityType} = ${EntityType.USER} AND EXISTS(
          SELECT 1 FROM ${users} 
          WHERE ${users.id} = ${followersNotifications.entityId} 
          AND ${users.status} = ${UserStatus.ACTIVE}
        )) 
        OR 
        (${followersNotifications.entityType} = ${EntityType.WORKSPACE} AND EXISTS(
          SELECT 1 FROM ${workspaces} 
          WHERE ${workspaces.id} = ${followersNotifications.entityId} 
          AND ${workspaces.status} = ${WorkspacesStatus.ACTIVE}
        ))
      )`,
    ];

    if (isUnreadOnly) {
      postWhere.push(eq(postNotifications.isRead, false));
      followerWhere.push(eq(followersNotifications.isRead, false));
    }

    const postNotificationsQuery = this.drizzleDev
      .select({
        id: postNotifications.id,
        actedUser: sql`COALESCE(
          (SELECT STRING_AGG(DISTINCT ${users.firstName}, ', ')
           FROM ${users}
           WHERE ${users.id} = ${postNotifications.entityId}),
          NULL
        )`.as('actedUser'),
        actedWorkspace: sql`COALESCE(
          (SELECT STRING_AGG(DISTINCT ${workspaces.label}, ', ')
           FROM ${workspaces}
           WHERE ${workspaces.id} = ${postNotifications.entityId}),
          NULL
        )`.as('actedWorkspace'),
        notificationType: postNotifications.type,
        isRead: postNotifications.isRead,
        postId: posts.id,
        postComment: postComments.comment,
        postContent: posts.content,
        postMedia: sql<{ mediaPath: string; mediaType: string }>`(
          SELECT json_build_object(
            'mediaPath', ${postMedias.mediaPath},
            'mediaType', ${postMedias.mediaType}
          )
          FROM ${postMedias} 
          WHERE ${postMedias.postId} = ${posts.id} 
          AND ${postMedias.status} = ${PostMediaStatus.ACTIVE} 
          LIMIT 1
        )`.as('first_media'),
        createdAt: postNotifications.createdAt,
        entityId: postNotifications.entityId,
        entityType: postNotifications.entityType,
      })
      .from(postNotifications)
      .leftJoin(posts, eq(postNotifications.postId, posts.id))
      .leftJoin(postComments, eq(postNotifications.commentId, postComments.id))
      .where(and(...postWhere))
      .orderBy(desc(postNotifications.createdAt));

    const followerNotificationsQuery = this.drizzleDev
      .select({
        id: followersNotifications.id,
        notificationType: sql`'follow'`.as('notificationType'),
        isRead: followersNotifications.isRead,
        actedUser: sql`COALESCE(
          (SELECT STRING_AGG(DISTINCT ${users.firstName}, ', ')
           FROM ${users}
           WHERE ${users.id} = ${followersNotifications.entityId}),
          NULL
        )`.as('actedUser'),
        actedWorkspace: sql`COALESCE(
          (SELECT STRING_AGG(DISTINCT ${workspaces.label}, ', ')
           FROM ${workspaces}
           WHERE ${workspaces.id} = ${followersNotifications.entityId}),
          NULL
        )`.as('actedWorkspace'),
        createdAt: followersNotifications.createdAt,
        entityId: followersNotifications.entityId,
        entityType: followersNotifications.entityType,
      })
      .from(followersNotifications)
      .where(and(...followerWhere))
      .orderBy(desc(followersNotifications.createdAt));

    const [postNotificationsResult, followerNotificationsResult] = await Promise.all([
      postNotificationsQuery,
      followerNotificationsQuery,
    ]);

    const combinedNotifications = [...postNotificationsResult, ...followerNotificationsResult].sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );

    return combinedNotifications;
  }

  async findTotalUnreadCount(workspaceId: string): Promise<number> {
    const postUnreadCountPromise = this.drizzleDev
      .select({ count: sql<number>`count(*)` })
      .from(postNotifications)
      .leftJoin(posts, eq(postNotifications.postId, posts.id))
      .leftJoin(users, eq(postNotifications.entityId, users.id))
      .where(
        and(
          eq(posts.workspaceId, workspaceId),
          eq(posts.status, PostActiveStatus.ACTIVE),
          eq(posts.postStatus, PostStatus.PUBLISHED),
          eq(postNotifications.isRead, false),
          eq(postNotifications.status, PostNotificationsStatus.ACTIVE),
          sql`(
            (${postNotifications.entityType} = ${EntityType.USER} AND EXISTS(
              SELECT 1 FROM ${users} 
              WHERE ${users.id} = ${postNotifications.entityId} 
              AND ${users.status} = ${UserStatus.ACTIVE}
            )) 
            OR 
            (${postNotifications.entityType} = ${EntityType.WORKSPACE} AND EXISTS(
              SELECT 1 FROM ${workspaces} 
              WHERE ${workspaces.id} = ${postNotifications.entityId} 
              AND ${workspaces.status} = ${WorkspacesStatus.ACTIVE}
            ))
          )`,
        ),
      );

    const followerUnreadCountPromise = this.drizzleDev
      .select({ count: sql<number>`count(*)` })
      .from(followersNotifications)
      .leftJoin(users, eq(followersNotifications.entityId, users.id))
      .where(
        and(
          eq(followersNotifications.followedId, workspaceId),
          eq(followersNotifications.isRead, false),
          eq(followersNotifications.status, FollowersNotificationsStatus.ACTIVE),
          sql`(
            (${followersNotifications.entityType} = ${EntityType.USER} AND EXISTS(
              SELECT 1 FROM ${users} 
              WHERE ${users.id} = ${followersNotifications.entityId} 
              AND ${users.status} = ${UserStatus.ACTIVE}
            )) 
            OR 
            (${followersNotifications.entityType} = ${EntityType.WORKSPACE} AND EXISTS(
              SELECT 1 FROM ${workspaces} 
              WHERE ${workspaces.id} = ${followersNotifications.entityId} 
              AND ${workspaces.status} = ${WorkspacesStatus.ACTIVE}
            ))
          )`,
          sql`EXISTS (
            SELECT 1 FROM ${workspaceFollowers} 
            WHERE ${workspaceFollowers.workspaceId} = ${followersNotifications.followedId}
          )`,
        ),
      );

    const [postUnreadCount, followerUnreadCount] = await Promise.all([
      postUnreadCountPromise,
      followerUnreadCountPromise,
    ]);

    return Number(postUnreadCount[0]?.count || 0) + Number(followerUnreadCount[0]?.count || 0);
  }

  // Mark a post notification as read
  async markPostNotificationAsRead(notificationId: string) {
    await this.drizzleDev
      .update(postNotifications)
      .set({ isRead: true })
      .where(eq(postNotifications.id, notificationId));
    return true;
  }

  // Mark all post notifications as read
  async markAllPostNotificationsAsRead(workspaceId: string) {
    await this.drizzleDev
      .update(postNotifications)
      .set({ isRead: true })
      .where(
        and(
          eq(postNotifications.isRead, false),
          eq(postNotifications.status, PostNotificationsStatus.ACTIVE),
          inArray(
            postNotifications.postId,
            this.drizzleDev
              .select({ id: posts.id })
              .from(posts)
              .where(
                and(eq(posts.workspaceId, workspaceId), eq(posts.status, PostActiveStatus.ACTIVE)),
              ),
          ),
        ),
      );
    return true;
  }

  // Mark a follower notification as read
  async markFollowerNotificationAsRead(notificationId: string) {
    await this.drizzleDev
      .update(followersNotifications)
      .set({ isRead: true })
      .where(eq(followersNotifications.id, notificationId));
    return true;
  }

  // Mark all follower notifications as read
  async markAllFollowerNotificationsAsRead(followedId: string) {
    await this.drizzleDev
      .update(followersNotifications)
      .set({ isRead: true })
      .where(
        and(
          eq(followersNotifications.isRead, false),
          eq(followersNotifications.followedId, followedId),
          eq(followersNotifications.status, FollowersNotificationsStatus.ACTIVE),
        ),
      );
    return true;
  }

  // Invalidate post notification
  invalidatePostNotification(
    {
      entityId,
      postId,
      type,
      commentId,
    }: { entityId: string; postId: string; type: PostNotificationsType; commentId?: string },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;
    const where: SQL[] = [
      eq(postNotifications.entityId, entityId),
      eq(postNotifications.postId, postId),
      eq(postNotifications.type, type),
    ];

    if (type === PostNotificationsType.COMMENT) {
      if (!commentId) throw new NotAcceptableException();
      where.push(eq(postNotifications.commentId, commentId));
    }

    return db
      .update(postNotifications)
      .set({ status: PostNotificationsStatus.INACTIVE })
      .where(and(...where));
  }

  // Invalidate follower notification
  invalidateFollowNotification(
    { entityId, followedId }: { entityId: string; followedId: string },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;
    return db
      .update(followersNotifications)
      .set({ status: FollowersNotificationsStatus.INACTIVE })
      .where(
        and(
          eq(followersNotifications.followedId, followedId),
          eq(followersNotifications.entityId, entityId),
        ),
      );
  }
}
