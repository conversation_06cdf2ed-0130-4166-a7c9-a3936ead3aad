import { <PERSON>, <PERSON>, Param, <PERSON>, Query } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { User } from '@/decorators/user.decorator';
import { AuthConstants } from '@/constants/auth';
import { NotificationsQueryDto } from './dto/notification-query-dto';
import { unauthorized } from '@/exceptions/system';

@Controller('notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}
  @Get()
  async findAll(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: NotificationsQueryDto,
  ) {
    return this.notificationsService.findAllNotifications(
      workspaceId,
      query?.isUnreadOnly === 'true',
    );
  }

  @Get('unread-count')
  async findUnreadCount(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    return this.notificationsService.findTotalUnreadCount(workspaceId);
  }

  @Patch('posts/mark-as-read/:notificationId')
  async markPostNotificationAsRead(
    @Param('notificationId') notificationId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const postNotification =
      await this.notificationsService.findOnePostNotification(notificationId);
    if (postNotification?.post?.workspaceId !== workspaceId) {
      throw unauthorized();
    }
    return this.notificationsService.markPostNotificationAsRead(notificationId);
  }

  @Patch('followers/mark-as-read/:notificationId')
  async markFollowersNotificationAsRead(
    @Param('notificationId') notificationId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const followerNotification =
      await this.notificationsService.findOneFollowerNotification(notificationId);
    if (followerNotification?.followedId !== workspaceId) {
      throw unauthorized();
    }
    return this.notificationsService.markFollowerNotificationAsRead(notificationId);
  }

  @Patch('mark-all-as-read')
  async markAllAsRead(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    return [
      this.notificationsService.markAllPostNotificationsAsRead(workspaceId),
      this.notificationsService.markAllFollowerNotificationsAsRead(workspaceId),
    ];
  }
}
