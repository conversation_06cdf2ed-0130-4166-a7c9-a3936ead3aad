import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { WorkspaceFilesSection } from '@/constants/workspace-files';

export class CreateWorkspaceFileDto {
  @ApiProperty({
    name: 'mediaPath',
    type: 'string',
    required: true,
    example: 'uploads/files/document.pdf',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  mediaPath: string;

  @ApiProperty({
    name: 'mediaType',
    type: 'string',
    required: true,
    example: 'pdf',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  mediaType: string;

  @ApiProperty({
    name: 'label',
    type: 'string',
    required: true,
    example: 'The impact of climate change',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  label: string;

  @ApiProperty({
    name: 'section',
    type: 'string',
    required: true,
    example: WorkspaceFilesSection.GALLERY,
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(WorkspaceFilesSection, {
    message: `Section must be one of this following ${Object.values(WorkspaceFilesSection).join(', ')}`,
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  section: WorkspaceFilesSection;
}
