import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import { CreateWorkspaceFileDto } from './dto/create-workspace-file.dto';
import { UpdateWorkspaceFileDto } from './dto/update-workspace-file.dto';

import * as schema from '@/db/schema';
import { workspaceFiles } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { WorkspaceFilesSection, WorkspaceFilesStatus } from '@/constants/workspace-files';

@Injectable()
export class WorkspaceFilesService {
  constructor(@Inject('DB_DEV') private readonly drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(createWorkspaceFileDto: CreateWorkspaceFileDto, workspaceId: string) {
    const orgFile = await this.drizzleDev.query.workspaceFiles.findFirst({
      where: and(eq(workspaceFiles.mediaPath, createWorkspaceFileDto.mediaPath)),
    });

    if (orgFile) throw itemAlreadyExists(EntityName.WORKSPACE_FILE);

    const [newOrgFile] = await this.drizzleDev
      .insert(workspaceFiles)
      .values({
        workspaceId,
        ...createWorkspaceFileDto,
      })
      .returning();

    return newOrgFile;
  }

  async findOne(id: string) {
    const workspaceFile = await this.drizzleDev.query.workspaceFiles.findFirst({
      where: and(eq(workspaceFiles.id, id), eq(workspaceFiles.status, WorkspaceFilesStatus.ACTIVE)),
    });

    if (!workspaceFile) throw itemNotFound(EntityName.WORKSPACE_FILE);

    return workspaceFile;
  }

  async findWorkspaceFilesWithOrgIdAndSection(workspaceId: string, section: WorkspaceFilesSection) {
    const workspaceFile = await this.drizzleDev.query.workspaceFiles.findMany({
      where: and(
        eq(workspaceFiles.workspaceId, workspaceId),
        eq(workspaceFiles.section, section),
        eq(workspaceFiles.status, WorkspaceFilesStatus.ACTIVE),
      ),
    });

    return workspaceFile;
  }

  async update(id: string, updateWorkspaceFileDto: UpdateWorkspaceFileDto, workspaceId: string) {
    const [res] = await this.drizzleDev
      .update(workspaceFiles)
      .set({
        ...updateWorkspaceFileDto,
      })
      .where(
        and(
          eq(workspaceFiles.id, id),
          eq(workspaceFiles.status, WorkspaceFilesStatus.ACTIVE),
          eq(workspaceFiles.workspaceId, workspaceId),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.WORKSPACE_FILE);

    return res;
  }

  async softDelete(id: string, workspaceId: string) {
    const [res] = await this.drizzleDev
      .update(workspaceFiles)
      .set({
        status: WorkspaceFilesStatus.INACTIVE,
      })
      .where(
        and(
          eq(workspaceFiles.id, id),
          eq(workspaceFiles.status, WorkspaceFilesStatus.ACTIVE),
          eq(workspaceFiles.workspaceId, workspaceId),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.WORKSPACE_FILE);

    return res;
  }
}
