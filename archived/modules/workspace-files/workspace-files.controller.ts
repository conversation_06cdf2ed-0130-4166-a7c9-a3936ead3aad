import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { WorkspaceFilesService } from './workspace-files.service';

import { CreateWorkspaceFileDto } from './dto/create-workspace-file.dto';
import { UpdateWorkspaceFileDto } from './dto/update-workspace-file.dto';

import { User } from '@/decorators/user.decorator';

import { AuthConstants } from '@/constants/auth';
import { WorkspaceFilesSection } from '@/constants/workspace-files';

@Controller('workspace-files')
@ApiBearerAuth()
@ApiTags('workspace files')
export class WorkspaceFilesController {
  constructor(private readonly workspaceFilesService: WorkspaceFilesService) {}

  @Post()
  create(
    @Body() createWorkspaceFileDto: CreateWorkspaceFileDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.workspaceFilesService.create(createWorkspaceFileDto, workspaceId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.workspaceFilesService.findOne(id);
  }

  @Get('/:workspaceId/:section')
  findWorkspaceFilesWithWorkspaceIdAndSection(
    @Param('workspaceId') workspaceId: string,
    @Param('section') section: WorkspaceFilesSection,
  ) {
    return this.workspaceFilesService.findWorkspaceFilesWithOrgIdAndSection(workspaceId, section);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateWorkspaceDto: UpdateWorkspaceFileDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.workspaceFilesService.update(id, updateWorkspaceDto, workspaceId);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.workspaceFilesService.softDelete(id, workspaceId);
  }
}
