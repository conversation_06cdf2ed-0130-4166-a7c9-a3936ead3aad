import { Module } from '@nestjs/common';

import { CustomConfigService } from '@/config/configuration.service';

import { FileUploadController } from './utils.controller';
import { FileUploadService } from './utils.service';

// Module Setup
@Module({
  controllers: [FileUploadController],
  providers: [FileUploadService, CustomConfigService],
  exports: [FileUploadService],
})
export class UtilsModule {}
