import * as nanoid from 'nanoid';
import { BadRequestException, Injectable } from '@nestjs/common';
import { createPresignedPost } from '@aws-sdk/s3-presigned-post';
import { Conditions } from '@aws-sdk/s3-presigned-post/dist-types/types';
import { DeleteObjectCommand } from '@aws-sdk/client-s3';

import { GenerateUploadUrlDto } from './dto/generate-upload-url.dto';

import { EntityTypesType, MEDIA_TYPES_INFO } from '@/constants/posts';

import { CustomConfigService } from '@/config/configuration.service';
import { s3Client } from '@/config/aws-s3';

import { getMediaCategoryByMimeType } from '@/utils/files';

@Injectable()
export class FileUploadService {
  constructor(private readonly configService: CustomConfigService) {}

  // Generate a unique file path
  private generateFilePath(mediaType: string, entityType: EntityTypesType, userId: string): string {
    const mediaCategory = getMediaCategoryByMimeType(mediaType);

    const mediaDetails = MEDIA_TYPES_INFO[mediaCategory!];
    const extension = mediaDetails.extensions[mediaType];

    const uniqueId = nanoid.nanoid();
    return `public/${userId}/${entityType}/${mediaType}/${uniqueId}.${extension}`;
  }

  // Create Presigned URL for file upload
  async createPresignedUploadUrl(generalUploadUrldto: GenerateUploadUrlDto, userId: string) {
    const { entityType, mediaType } = generalUploadUrldto;

    try {
      const mediaCategory = getMediaCategoryByMimeType(mediaType);

      // Generate unique file path
      const key = this.generateFilePath(mediaType, entityType, userId);

      const { assetsBucketName } = this.configService.getAwsConfig();

      // More robust error handling and configuration
      const presignedPostOptions = {
        Bucket: assetsBucketName,
        Key: key,
        Expires: 60,
        Conditions: [
          ['content-length-range', 1, MEDIA_TYPES_INFO[mediaCategory!].maxSize],
          ['eq', '$bucket', assetsBucketName],
          ['eq', '$key', key],
        ] as Conditions[],
        // Fields: {
        //   "Content-Type": data.mimeType,
        // },
      };

      return await createPresignedPost(s3Client, presignedPostOptions as any);
    } catch (error) {
      console.error('Presigned URL generation error:', error);
      throw new BadRequestException('Failed to generate upload URL');
    }
  }

  async deleteFile(key: string) {
    try {
      const { assetsBucketName } = this.configService.getAwsConfig();

      const deleteCommand = new DeleteObjectCommand({
        Bucket: assetsBucketName,
        Key: key,
      });

      await s3Client.send(deleteCommand);
      return true;
    } catch (error) {
      console.error('File deletion error:', error);
      throw new BadRequestException('Failed to delete file');
    }
  }

  // Batch delete files from S3
  async deleteFiles(keys: string[]) {
    try {
      const deletionPromises = keys.map((key) => this.deleteFile(key));
      await Promise.all(deletionPromises);
      return true;
    } catch (error) {
      console.error('Batch file deletion error:', error);
      throw new BadRequestException('Failed to delete files');
    }
  }
}
