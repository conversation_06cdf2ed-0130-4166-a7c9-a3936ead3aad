import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, count, eq, inArray, ne, sql } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { reportedPosts, posts, users, postComments, postMedias, workspaces } from '@/db/schema';

import { CreateReportedPostDto } from '../dto/create-reported-post.dto';

import { PostCommentsStatus } from '@/constants/post-comments';
import { PostMediaStatus } from '@/constants/post-media';
import { PostActiveStatus, PostStatus, ReportedPostStatus } from '@/constants/posts';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';

import { itemNotFound } from '@/exceptions/common';

@Injectable()
export class PostReportService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(dto: CreateReportedPostDto, reporterId: string) {
    const { postId, reportedReason } = dto;

    let report = await this.drizzleDev.query.reportedPosts.findFirst({
      where: and(eq(reportedPosts.postId, postId), eq(reportedPosts.reporterId, reporterId)),
    });

    if (report) {
      if (report.reportedReason === reportedReason) {
        return report;
      } else {
        [report] = await this.drizzleDev
          .update(reportedPosts)
          .set({ reportedReason })
          .where(and(eq(reportedPosts.postId, postId), eq(reportedPosts.reporterId, reporterId)))
          .returning();
      }
    } else {
      [report] = await this.drizzleDev
        .insert(reportedPosts)
        .values({
          postId,
          reporterId,
          reportedReason,
        })
        .returning();
    }

    return report;
  }

  async findAll() {
    const reports = await this.drizzleDev
      .select({
        reportCount: count(reportedPosts.postId),
        postId: posts.id,
        postType: posts.postType,
        latestReportCreatedAt: sql<Date>`MAX(${reportedPosts.createdAt})`,
      })
      .from(reportedPosts)
      .leftJoin(posts, eq(reportedPosts.postId, posts.id))
      .where(ne(posts.postStatus, PostStatus.DELETED))
      .groupBy(posts.id);

    return reports;
  }

  async findOne(postId: string) {
    const reportedPost = await this.drizzleDev.query.posts.findFirst({
      where: and(eq(posts.id, postId), ne(posts.postStatus, PostStatus.DELETED)),
      columns: {
        id: true,
        content: true,
        postType: true,
        postStatus: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            id: true,
            mediaPath: true,
            mediaType: true,
          },
        },
        publishedBy: {
          columns: {
            id: true,
            email: true,
          },
          extras: {
            fullName:
              sql<string>`concat(${users.firstName},' ',coalesce(${users.middleName} || ' ', ''), ${users.lastName})`.as(
                'full_name',
              ),
          },
        },
        workspace: {
          columns: {
            id: true,
            label: true,
            type: true,
          },
        },
        postComments: {
          where: eq(postComments.status, PostCommentsStatus.ACTIVE),
          columns: {
            id: true,
            comment: true,
            entityId: true,
            entityType: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: (postComment, { desc: commDesc }) => [commDesc(postComment.createdAt)],
        },
      },
    });

    if (!reportedPost) throw itemNotFound(EntityName.POST);

    // Fetch all reports for this post
    const allReports = await this.drizzleDev.query.reportedPosts.findMany({
      where: and(
        eq(reportedPosts.postId, postId),
        eq(reportedPosts.status, ReportedPostStatus.ACTIVE),
      ),
      columns: {
        reportedReason: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        reporter: {
          columns: {
            id: true,
          },
          extras: {
            fullName:
              sql<string>`concat(${users.firstName},' ',coalesce(${users.middleName} || ' ', ''), ${users.lastName})`.as(
                'full_name',
              ),
          },
        },
      },
      orderBy: (rp, { desc }) => [desc(rp.createdAt)],
    });

    if (!allReports.length) throw itemNotFound(EntityName.POST);

    const userIds = reportedPost.postComments
      .filter((comment) => comment.entityType === EntityType.USER)
      .map((comment) => comment.entityId);

    // Fetch unique user info in a single query
    const usersInfo =
      userIds.length > 0
        ? await this.drizzleDev.query.users.findMany({
            where: inArray(users.id, [...new Set(userIds)]),
            columns: {
              id: true,
              email: true,
            },
            extras: {
              fullName:
                sql<string>`concat(${users.firstName},' ',coalesce(${users.middleName} || ' ', ''), ${users.lastName})`.as(
                  'full_name',
                ),
            },
          })
        : [];

    const userMap = new Map(usersInfo.map((user) => [user.id, user]));

    const workspaceIds = reportedPost.postComments
      .filter((comment) => comment.entityType === EntityType.WORKSPACE)
      .map((comment) => comment.entityId);

    const workspacesInfo =
      workspaceIds.length > 0
        ? await this.drizzleDev.query.workspaces.findMany({
            where: inArray(workspaces.id, [...new Set(workspaceIds)]),
            columns: {
              id: true,
              label: true,
              // Include other relevant workspace fields
            },
          })
        : [];

    const workspaceMap = new Map(workspacesInfo.map((workspace) => [workspace.id, workspace]));

    const groupedReports = Object.entries(
      allReports.reduce(
        (acc, report) => {
          const reason = report.reportedReason;
          if (!acc[reason]) {
            acc[reason] = [];
          }
          acc[reason].push({
            reporterId: report.reporter.id,
            fullName: report.reporter.fullName,
            reportedAt: report.updatedAt,
          });
          return acc;
        },
        {} as Record<string, { reporterId: string; fullName: string; reportedAt: Date }[]>,
      ),
    ).map(([label, usersDetails]) => ({ label, users: usersDetails }));

    return {
      postId: reportedPost.id,
      postType: reportedPost.postType,
      content: reportedPost.content,
      status: reportedPost.postStatus,
      createdAt: reportedPost.createdAt,
      updatedAt: reportedPost.updatedAt,
      publisher: {
        id: reportedPost.publishedBy.id,
        fullName: reportedPost.publishedBy.fullName,
        email: reportedPost.publishedBy.email,
      },
      medias: reportedPost.postMedias.map((postMedia) => ({
        id: postMedia.id,
        mediaPath: postMedia.mediaPath,
        mediaType: postMedia.mediaType,
      })),
      workspace: {
        id: reportedPost.workspace.id,
        label: reportedPost.workspace.label,
        type: reportedPost.workspace.type,
      },
      comments: reportedPost.postComments.map((comment) => {
        let entityInfo = null;

        if (comment.entityType === EntityType.USER) {
          entityInfo = userMap.get(comment.entityId);
        } else if (comment.entityType === EntityType.WORKSPACE) {
          entityInfo = workspaceMap.get(comment.entityId);
        }

        return {
          ...comment,
          entity: entityInfo,
        };
      }),
      allReports: groupedReports,
    };
  }

  async softDelete(postId: string) {
    await this.findOne(postId);

    const [res] = await this.drizzleDev
      .update(posts)
      .set({
        postStatus: PostStatus.DELETED,
        status: PostActiveStatus.INACTIVE,
      })
      .where(eq(posts.id, postId))
      .returning();

    //TODO: if we have to inactive all rows where have data associted to this post, have to do that.

    return res;
  }

  async isReported(postId: string) {
    const conditions = [eq(reportedPosts.postId, postId)];

    const report = await this.drizzleDev.query.reportedPosts.findFirst({
      where: and(...conditions),
    });

    return Boolean(report);
  }
}
