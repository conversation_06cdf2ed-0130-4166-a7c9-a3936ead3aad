import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '@/db/schema';

import { postsPolls } from '@/db/schema';
import { itemAlreadyExists } from '@/exceptions/common';
import { EntityName } from '@/constants/entities';
import { PostsPollsStatus } from '@/constants/posts-polls';
import { and, eq, sql } from 'drizzle-orm';
import { EntityType } from '@/constants/user-types';

@Injectable()
export class PostPollsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async createVote(postId: string, option: string, entityId: string, entityType: string) {
    const pollExists = await this.drizzleDev.query.postsPolls.findFirst({
      where: and(
        eq(postsPolls.postId, postId),
        eq(postsPolls.option, option),
        eq(postsPolls.entityId, entityId),
        eq(postsPolls.status, PostsPollsStatus.ACTIVE),
      ),
    });

    if (pollExists) {
      throw itemAlreadyExists(EntityName.POST_POLL);
    }

    const differentOptionPolls = await this.drizzleDev.query.postsPolls.findFirst({
      where: and(
        eq(postsPolls.postId, postId),
        eq(postsPolls.entityId, entityId),
        eq(postsPolls.status, PostsPollsStatus.ACTIVE),
      ),
    });

    if (differentOptionPolls) {
      const [updatedPoll] = await this.drizzleDev
        .update(postsPolls)
        .set({ option })
        .where(
          and(
            eq(postsPolls.postId, postId),
            eq(postsPolls.entityId, entityId),
            eq(postsPolls.status, PostsPollsStatus.ACTIVE),
          ),
        )
        .returning();

      return updatedPoll;
    }

    const [postPoll] = await this.drizzleDev
      .insert(postsPolls)
      .values({
        postId,
        option,
        entityType: entityType as EntityType,
        entityId,
      })
      .returning();

    return postPoll;
  }

  // async getPollsForOption(postId: string, option: string) {
  //   const polls = await this.drizzleDev.query.postsPolls.findMany({
  //     where: and(
  //       eq(postsPolls.postId, postId),
  //       eq(postsPolls.option, option),
  //       eq(postsPolls.status, PostsPollsStatus.ACTIVE),
  //     ),
  //   });

  //   return polls;
  // }

  // async getPollsForPost(postId: string) {
  //   const polls = await this.drizzleDev.query.postsPolls.findMany({
  //     where: and(eq(postsPolls.postId, postId), eq(postsPolls.status, PostsPollsStatus.ACTIVE)),
  //   });

  //   return polls;
  // }

  async getVoteForUser(entityId: string, postId: string) {
    const poll = await this.drizzleDev.query.postsPolls.findFirst({
      where: and(
        eq(postsPolls.postId, postId),
        eq(postsPolls.entityId, entityId),
        eq(postsPolls.status, PostsPollsStatus.ACTIVE),
      ),
    });

    return poll;
  }

  async removeVoteForUser(postId: string, entityId: string) {
    const [removedPoll] = await this.drizzleDev
      .update(postsPolls)
      .set({ status: PostsPollsStatus.INACTIVE })
      .where(
        and(
          eq(postsPolls.postId, postId),
          eq(postsPolls.entityId, entityId),
          eq(postsPolls.status, PostsPollsStatus.ACTIVE),
        ),
      )
      .returning();

    return removedPoll;
  }

  async getPollsCountForPost(postId: string) {
    const pollCounts = await this.drizzleDev
      .select({
        option: postsPolls.option,
        count: sql<number>`COUNT(*)`,
      })
      .from(postsPolls)
      .where(and(eq(postsPolls.postId, postId), eq(postsPolls.status, PostsPollsStatus.ACTIVE)))
      .groupBy(postsPolls.option);

    return pollCounts;
  }

  async getCountForOption(postId: string, option: string) {
    const count = await this.drizzleDev
      .select({ count: sql<number>`COUNT(*)` })
      .from(postsPolls)
      .where(
        and(
          eq(postsPolls.postId, postId),
          eq(postsPolls.option, option),
          eq(postsPolls.status, PostsPollsStatus.ACTIVE),
        ),
      )
      .groupBy(postsPolls.option);

    return count;
  }
}
