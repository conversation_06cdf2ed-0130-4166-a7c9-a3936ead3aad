import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, desc, eq, exists, gt, ilike, inArray, lte, or, SQL, sql } from 'drizzle-orm';

import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { PostMediaDto } from './dto/create-post-media.dto';

import * as schema from '@/db/schema';
import {
  postCountries,
  postMedias,
  posts,
  postTags,
  tags,
  countries,
  postLikes,
  postViews,
  postComments,
  workspaceFollowers,
  opportunities,
  users,
  workspaces,
} from '@/db/schema';

import { itemNotFound } from '@/exceptions/common';
import {
  // postInUnsubscribedCountryException,
  postScheduleDateFutureRequired,
  invalidPostStatusTransitionException,
} from '@/exceptions/posts';
import { unauthorized } from '@/exceptions/system';

import { CategoryService } from '@/modules/category/category.service';
// import { SubscriptionsService } from '@/modules/subscriptions/subscriptions.service';

import { EntityName } from '@/constants/entities';
import { PostActiveStatus, PostStatus, PostType } from '@/constants/posts';
import { PostMediaStatus } from '@/constants/post-media';
import { PostTagsStatus } from '@/constants/post-tags';
// import { PostCountriesStatus } from '@/constants/post-countries';
import { PostMediasStatus } from '@/constants/post-medias';
import { TagsStatus } from '@/constants/tags';
import { CountriesStatus } from '@/constants/countries';
import { PostLikesStatus } from '@/constants/post-likes';
import { PostViewsStatus } from '@/constants/post-views';
import { PostCommentsStatus } from '@/constants/post-comments';
import { FollowerStatus } from '@/constants/followers';
import { EntityType } from '@/constants/user-types';

import { FollowersService } from '@/modules/followers/followers.service';
import { FileUploadService } from '@/modules/utils/utils.service';
import { PostPollsService } from './post-polls/post-polls.service';

@Injectable()
export class PostsService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly categoryService: CategoryService,
    // private readonly subscriptionService: SubscriptionsService,
    private readonly fileUploadService: FileUploadService,
    private readonly followersService: FollowersService,
    private readonly postsPollsService: PostPollsService,
  ) {}

  // async _createTagsCountriesMediaForPost(postId: string, createPostDto: CreatePostDto) {
  //   const tagsArray = createPostDto.tags.split(',');

  //   tagsArray.forEach(async (elem) => {
  //     const tag = await this.drizzleDev.query.tags.findFirst({
  //       where: eq(sql`LOWER(${tags.label})`, sql`LOWER(${elem})`),
  //     });

  //     if (tag) {
  //       await this.drizzleDev.insert(postTags).values({
  //         postId,
  //         tagId: tag.id,
  //       });
  //     } else {
  //       const [newTag] = await this.drizzleDev
  //         .insert(tags)
  //         .values({
  //           label: elem,
  //         })
  //         .returning();

  //       await this.drizzleDev.insert(postTags).values({
  //         postId,
  //         tagId: newTag?.id,
  //       });
  //     }
  //   });

  //   await this.drizzleDev.insert(postCountries).values({
  //     postId,
  //     countryId: createPostDto.countryId,
  //   });

  //   if (createPostDto.postmediaDetails.length > 0) {
  //     createPostDto.postmediaDetails.forEach(async (elem) => {
  //       await this.createPostMedia(postId, elem);
  //     });
  //   }
  // }

  async findAll(limit?: number, offset?: number, tag?: string, entityId?: string) {
    // Fetch posts with related data, including likes information
    const postsQuery = await this.drizzleDev.query.posts.findMany({
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        // Add tag filtering condition if tag is provided
        ...(tag
          ? [
              exists(
                this.drizzleDev
                  .select()
                  .from(postTags)
                  .where(
                    and(
                      eq(postTags.postId, posts.id),
                      eq(postTags.status, PostTagsStatus.ACTIVE),
                      exists(
                        this.drizzleDev
                          .select()
                          .from(tags)
                          .where(
                            and(
                              eq(tags.id, postTags.tagId),
                              eq(sql`LOWER(${tags.label})`, tag.toLowerCase()),
                            ),
                          ),
                      ),
                    ),
                  ),
              ),
            ]
          : []),
        or(
          // Regular posts (with category_id)
          inArray(posts.postType, [PostType.POST, PostType.ARTICLE, PostType.POLL]),
          // Opportunity posts that haven't expired
          and(
            eq(posts.postType, PostType.OPPORTUNITY),
            exists(
              this.drizzleDev
                .select()
                .from(opportunities)
                .where(
                  and(
                    eq(opportunities.id, posts.opportunityId),
                    gt(opportunities.expiryDate, new Date()),
                  ),
                ),
            ),
          ),
        ),
      ),
      orderBy: (post, { desc: postDesc }) => [postDesc(post.postScheduleDate)],
      with: {
        publishedBy: {
          columns: {
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
            username: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
            profileImageUrlThumbnail: true,
            workspacename: true,
          },
        },
        opportunity: {
          columns: {
            id: true,
            title: true,
            shortDescription: true,
            contactPersonName: true,
          },
        },
        postLikes: {
          where: eq(postLikes.status, PostLikesStatus.ACTIVE),
          columns: {
            entityId: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostViewsStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postComments: {
          where: eq(postComments.status, PostCommentsStatus.ACTIVE),
          columns: {
            id: true,
            comment: true,
            entityId: true,
            entityType: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: (postComment, { desc: commDesc }) => [commDesc(postComment.createdAt)],
        },
        postCountries: {
          columns: {},
          with: {
            country: {
              columns: {
                code: true,
                name: true,
              },
            },
          },
          where: eq(countries.status, CountriesStatus.ACTIVE),
        },
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            mediaPath: true,
            mediaType: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
      limit,
      offset,
    });

    const userIds = postsQuery
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.USER)
      .map((comment) => comment.entityId);

    // Fetch unique user info in a single query
    const usersInfo =
      userIds.length > 0
        ? await this.drizzleDev.query.users.findMany({
            where: inArray(users.id, [...new Set(userIds)]),
            columns: {
              id: true,
              firstName: true,
              middleName: true,
              lastName: true,
              email: true,
            },
          })
        : [];

    const userMap = new Map(usersInfo.map((user) => [user.id, user]));

    const workspaceIds = postsQuery
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.WORKSPACE)
      .map((comment) => comment.entityId);

    const workspacesInfo =
      workspaceIds.length > 0
        ? await this.drizzleDev.query.workspaces.findMany({
            where: inArray(workspaces.id, [...new Set(workspaceIds)]),
            columns: {
              id: true,
              label: true,
              // Include other relevant workspace fields
            },
          })
        : [];

    const workspaceMap = new Map(workspacesInfo.map((workspace) => [workspace.id, workspace]));

    const processedPosts = await Promise.all(
      postsQuery.map(async (post) => {
        let userFollows = false;
        if (entityId) {
          userFollows =
            await this.followersService.isUserOrWorkspaceFollowingParticularWorkspaceById(
              entityId,
              post.workspaceId,
            );
        }

        let pollCount = null;
        let userVoted = null;
        if (post.postType === PostType.POLL) {
          pollCount = await this.postsPollsService.getPollsCountForPost(post.id);
          if (entityId) {
            userVoted = await this.postsPollsService.getVoteForUser(entityId, post.id);
          }
        }

        return {
          ...post,
          isFollowing: userFollows,
          pollCount,
          userVoted,
          postComments: post.postComments.map((comment) => {
            let entityInfo = null;

            if (comment.entityType === EntityType.USER) {
              entityInfo = userMap.get(comment.entityId);
            } else if (comment.entityType === EntityType.WORKSPACE) {
              entityInfo = workspaceMap.get(comment.entityId);
            }

            return {
              ...comment,
              entity: entityInfo,
            };
          }),
        };
      }),
    );
    return processedPosts;
  }

  async findAllByPostType(
    currentUserId: string,
    postType: PostType,
    limit?: number,
    offset?: number,
  ) {
    const fetchedPosts = await this.drizzleDev.query.posts.findMany({
      where: and(
        eq(posts.postStatus, PostStatus.PUBLISHED),
        eq(posts.status, PostActiveStatus.ACTIVE),
        eq(posts.postType, postType),
      ),
      // eslint-disable-next-line @typescript-eslint/no-shadow
      orderBy: (posts, { desc }) => [desc(posts.createdAt)],
      with: {
        publishedBy: {
          columns: {
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
          },
        },
        postLikes: {
          where: eq(postLikes.status, PostLikesStatus.ACTIVE),
          columns: {
            entityId: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostViewsStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postComments: {
          where: eq(postComments.status, PostCommentsStatus.ACTIVE),
          columns: {
            id: true,
            comment: true,
            entityId: true,
            entityType: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: (postComment, { desc: commDesc }) => [commDesc(postComment.createdAt)],
        },
        postCountries: {
          columns: {},
          with: {
            country: {
              columns: {
                code: true,
                name: true,
              },
            },
          },
          where: eq(countries.status, CountriesStatus.ACTIVE),
        },
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            mediaPath: true,
            mediaType: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
      limit,
      offset,
    });

    const userIds = fetchedPosts
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.USER)
      .map((comment) => comment.entityId);

    // Fetch unique user info in a single query
    const usersInfo =
      userIds.length > 0
        ? await this.drizzleDev.query.users.findMany({
            where: inArray(users.id, [...new Set(userIds)]),
            columns: {
              id: true,
              firstName: true,
              middleName: true,
              lastName: true,
              email: true,
            },
          })
        : [];

    const userMap = new Map(usersInfo.map((user) => [user.id, user]));

    const workspaceIds = fetchedPosts
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.WORKSPACE)
      .map((comment) => comment.entityId);

    const workspacesInfo =
      workspaceIds.length > 0
        ? await this.drizzleDev.query.workspaces.findMany({
            where: inArray(workspaces.id, [...new Set(workspaceIds)]),
            columns: {
              id: true,
              label: true,
              // Include other relevant workspace fields
            },
          })
        : [];

    const workspaceMap = new Map(workspacesInfo.map((workspace) => [workspace.id, workspace]));

    const processedPosts = await Promise.all(
      fetchedPosts.map(async (post) => {
        let userFollows = false;
        if (currentUserId) {
          userFollows =
            await this.followersService.isUserOrWorkspaceFollowingParticularWorkspaceById(
              currentUserId,
              post.workspaceId,
            );
        }

        let pollCount = null;
        let userVoted = null;
        if (post.postType === PostType.POLL) {
          pollCount = await this.postsPollsService.getPollsCountForPost(post.id);
          if (currentUserId) {
            userVoted = await this.postsPollsService.getVoteForUser(currentUserId, post.id);
          }
        }

        return {
          ...post,
          isFollowing: userFollows,
          pollCount,
          userVoted,
          postComments: post.postComments.map((comment) => {
            let entityInfo = null;

            if (comment.entityType === EntityType.USER) {
              entityInfo = userMap.get(comment.entityId);
            } else if (comment.entityType === EntityType.WORKSPACE) {
              entityInfo = workspaceMap.get(comment.entityId);
            }

            return {
              ...comment,
              entity: entityInfo,
            };
          }),
        };
      }),
    );

    return processedPosts;
  }

  async findByUser(userId: string, postStatus?: PostStatus, limit?: number, offset?: number) {
    const where: SQL[] = [eq(posts.publishedBy, userId)];

    if (postStatus) {
      where.push(eq(posts.postStatus, postStatus));
    }

    return this.drizzleDev.query.posts.findMany({
      where: and(...where),
      // eslint-disable-next-line @typescript-eslint/no-shadow
      orderBy: (posts, { desc }) => [desc(posts.createdAt)],
      limit,
      offset,
    });
  }

  async findByWorkspace(workspaceId: string, postStatus?: PostStatus) {
    const postWhere = [eq(posts.workspaceId, workspaceId)];

    if (postStatus) {
      postWhere.push(eq(posts.postStatus, postStatus));
    }

    return this.drizzleDev.query.posts.findMany({
      where: and(...postWhere),
      orderBy: desc(posts.postScheduleDate),
      columns: {
        id: true,
        content: true,
        createdAt: true,
        postScheduleDate: true,
      },
      extras: {
        date: sql<Date>`posts.post_schedule_date`.as('date'),
      },
      with: {
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            id: true,
            mediaPath: true,
            mediaType: true,
          },
        },
        publishedBy: {
          columns: {
            id: true,
            email: true,
          },
          extras: {
            fullName:
              sql<string>`concat(${users.firstName},' ',coalesce(${users.middleName} || ' ', ''), ${users.lastName})`.as(
                'full_name',
              ),
          },
        },
        workspace: {
          columns: {
            id: true,
            label: true,
            type: true,
          },
        },
      },
    });
  }

  async findByWorkspaceAndPostType(
    workspaceId: string,
    userId: string,
    postType: PostType,
    limit?: number,
    offset?: number,
  ) {
    const fetchedPosts = await this.drizzleDev.query.posts.findMany({
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postType, postType),
        eq(posts.postStatus, PostStatus.PUBLISHED),
        eq(posts.status, PostActiveStatus.ACTIVE),
      ),
      // eslint-disable-next-line @typescript-eslint/no-shadow
      orderBy: (posts, { desc }) => [desc(posts.createdAt)],
      with: {
        publishedBy: {
          columns: {
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
            username: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
          },
        },
        postLikes: {
          where: eq(postLikes.status, PostLikesStatus.ACTIVE),
          columns: {
            entityId: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostViewsStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postComments: {
          where: eq(postComments.status, PostCommentsStatus.ACTIVE),
          columns: {
            id: true,
            comment: true,
            entityId: true,
            entityType: true,
            createdAt: true,
            updatedAt: true,
          },
          // eslint-disable-next-line @typescript-eslint/no-shadow
          orderBy: (postComments, { desc }) => [desc(postComments.createdAt)],
        },
        postCountries: {
          columns: {},
          with: {
            country: {
              columns: {
                code: true,
                name: true,
              },
            },
          },
          where: eq(countries.status, CountriesStatus.ACTIVE),
        },
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            mediaPath: true,
            mediaType: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
      limit,
      offset,
    });

    const userIds = fetchedPosts
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.USER)
      .map((comment) => comment.entityId);

    // Fetch unique user info in a single query
    const usersInfo =
      userIds.length > 0
        ? await this.drizzleDev.query.users.findMany({
            where: inArray(users.id, [...new Set(userIds)]),
            columns: {
              id: true,
              firstName: true,
              middleName: true,
              lastName: true,
              email: true,
            },
          })
        : [];

    const userMap = new Map(usersInfo.map((user) => [user.id, user]));

    const workspaceIds = fetchedPosts
      .flatMap((post) => post.postComments)
      .filter((comment) => comment.entityType === EntityType.WORKSPACE)
      .map((comment) => comment.entityId);

    const workspacesInfo =
      workspaceIds.length > 0
        ? await this.drizzleDev.query.workspaces.findMany({
            where: inArray(workspaces.id, [...new Set(workspaceIds)]),
            columns: {
              id: true,
              label: true,
              // Include other relevant workspace fields
            },
          })
        : [];

    const workspaceMap = new Map(workspacesInfo.map((workspace) => [workspace.id, workspace]));

    const processedPosts = await Promise.all(
      fetchedPosts.map(async (post) => {
        const userLiked = post.postLikes.some((like) => like.entityId === userId);
        const userFollows =
          await this.followersService.isUserOrWorkspaceFollowingParticularWorkspaceById(
            userId,
            post.workspaceId,
          );

        let pollCount = null;
        let userVoted = null;
        if (post.postType === PostType.POLL) {
          pollCount = await this.postsPollsService.getPollsCountForPost(post.id);
          if (userId) {
            userVoted = await this.postsPollsService.getVoteForUser(userId, post.id);
          }
        }

        return {
          ...post,
          currentUserLiked: userLiked,
          isFollowing: userFollows,
          pollCount,
          userVoted,
          postComments: post.postComments.map((comment) => {
            let entityInfo = null;

            if (comment.entityType === EntityType.USER) {
              entityInfo = userMap.get(comment.entityId);
            } else if (comment.entityType === EntityType.WORKSPACE) {
              entityInfo = workspaceMap.get(comment.entityId);
            }

            return {
              ...comment,
              entity: entityInfo,
            };
          }),
        };
      }),
    );

    return processedPosts;
  }

  async findOne(id: string, entityId?: string) {
    const post = await this.drizzleDev.query.posts.findFirst({
      where: and(eq(posts.id, id), eq(posts.status, PostActiveStatus.ACTIVE)),
      with: {
        publishedBy: {
          columns: {
            id: true,
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
            username: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
            profileImageUrlThumbnail: true,
            workspacename: true,
          },
        },
        postLikes: {
          where: eq(postLikes.status, PostLikesStatus.ACTIVE),
          columns: {
            entityId: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostViewsStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postComments: {
          where: eq(postComments.status, PostCommentsStatus.ACTIVE),
          columns: {
            comment: true,
            createdAt: true,
            updatedAt: true,
            entityId: true,
            entityType: true,
          },
          // eslint-disable-next-line @typescript-eslint/no-shadow
          orderBy: (postComments, { desc }) => [desc(postComments.createdAt)],
        },
        postCountries: {
          columns: {},
          with: {
            country: {
              columns: {
                code: true,
                name: true,
              },
            },
          },
          where: eq(countries.status, CountriesStatus.ACTIVE),
        },
        postMedias: {
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          columns: {
            mediaPath: true,
            mediaType: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
    });

    if (!post) throw itemNotFound(EntityName.POST);

    const userIds = post.postComments
      .filter((comment) => comment.entityType === EntityType.USER)
      .map((comment) => comment.entityId);

    // Fetch unique user info in a single query
    const usersInfo =
      userIds.length > 0
        ? await this.drizzleDev.query.users.findMany({
            where: inArray(users.id, [...new Set(userIds)]),
            columns: {
              id: true,
              firstName: true,
              middleName: true,
              lastName: true,
              email: true,
            },
          })
        : [];

    const userMap = new Map(usersInfo.map((user) => [user.id, user]));

    const workspaceIds = post.postComments
      .filter((comment) => comment.entityType === EntityType.WORKSPACE)
      .map((comment) => comment.entityId);

    const workspacesInfo =
      workspaceIds.length > 0
        ? await this.drizzleDev.query.workspaces.findMany({
            where: inArray(workspaces.id, [...new Set(workspaceIds)]),
            columns: {
              id: true,
              label: true,
              // Include other relevant workspace fields
            },
          })
        : [];

    const workspaceMap = new Map(workspacesInfo.map((workspace) => [workspace.id, workspace]));

    let userLiked = false;
    let isFollowing = false;

    if (entityId) {
      userLiked = post.postLikes.some((like) => like.entityId === entityId);

      const userFollows = await this.drizzleDev.query.workspaceFollowers.findFirst({
        where: and(
          eq(workspaceFollowers.entityId, entityId),
          eq(workspaceFollowers.workspaceId, post.workspaceId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      });

      if (userFollows) isFollowing = true;
    }

    return {
      ...post,
      currentUserLiked: userLiked,
      isFollowing,
      postComments: post.postComments.map((comment) => {
        let entityInfo = null;

        if (comment.entityType === EntityType.USER) {
          entityInfo = userMap.get(comment.entityId);
        } else if (comment.entityType === EntityType.WORKSPACE) {
          entityInfo = workspaceMap.get(comment.entityId);
        }

        return {
          ...comment,
          entity: entityInfo,
        };
      }),
    };
  }

  async create(userId: string, workspaceId: string, createPostDto: CreatePostDto) {
    const { categoryId, countryIds, tags: postAssociateTags, medias } = createPostDto;

    // Validate category
    const categoryIdExist = await this.categoryService.findOne(categoryId);
    if (!categoryIdExist) throw itemNotFound(EntityName.CATEGORY);

    return this.drizzleDev.transaction(async (txn) => {
      const [newPost] = await txn
        .insert(posts)
        .values({
          publishedBy: userId,
          updatedBy: userId,
          workspaceId,
          ...createPostDto,
        })
        .returning();

      // Add tags to post
      if (postAssociateTags && postAssociateTags.length > 0) {
        await Promise.all(
          postAssociateTags.map(async (tagLabel) => {
            const tag = await txn.query.tags.findFirst({
              where: eq(sql`LOWER(${tags.label})`, sql`${tagLabel.toLowerCase()}`),
            });

            let tagId;
            if (tag) {
              // Check if the tag is inactive and activate if necessary
              if (tag.status === TagsStatus.INACTIVE) {
                await txn
                  .update(tags)
                  .set({ status: TagsStatus.ACTIVE })
                  .where(eq(tags.id, tag.id));
              }
              tagId = tag.id;
            } else {
              // Insert new tag if not found
              const [newTag] = await txn.insert(tags).values({ label: tagLabel }).returning();
              tagId = newTag.id;
            }

            // Add tag to post
            await txn.insert(postTags).values({ postId: newPost.id, tagId });
          }),
        );
      }

      //TODO: HAVE TO USE THIS AFTER SUBSCRIPTION MODULE IS THERE
      // Get active subscription countries for validation
      /* const subscriptionDetails =
        await this.subscriptionService.findSubscriptionDetailsWithWorkspaceId(workspaceId);

      // Verify and associate subscribed countries
      const areAllCountriesSubscribed = countryIds.every((country) =>
        subscriptionDetails.countryIds.includes(country),
      );
      if (!areAllCountriesSubscribed) throw postInUnsubscribedCountryException; */

      // Insert countries for the post
      await txn
        .insert(postCountries)
        .values(countryIds.map((countryId) => ({ countryId, postId: newPost.id })));

      if (medias && medias.length > 0) {
        await txn.insert(postMedias).values(
          medias.map((media) => ({
            postId: newPost.id,
            ...media,
          })),
        );
      }

      return newPost;
    });
  }

  async findTags(label = '') {
    const tagsDetails = await this.drizzleDev.query.tags.findMany({
      where: and(ilike(tags.label, `%${label}%`), eq(tags.status, TagsStatus.ACTIVE)),
      columns: {
        id: true,
        label: true,
      },
    });

    return tagsDetails.map((td) => td.label);
  }

  async addPostOfOpportuntiy(
    opportunityId: string,
    userId: string,
    workspaceId: string,
    transaction: PostgresJsDatabase<typeof schema>,
  ) {
    const isOpportunityExist = await this.drizzleDev.query.posts.findFirst({
      where: eq(posts.opportunityId, opportunityId),
    });

    if (isOpportunityExist) return;

    const insertValue = {
      opportunityId,
      updatedBy: userId,
      publishedBy: userId,
      workspaceId,
      postStatus: PostStatus.PUBLISHED,
      postType: PostType.OPPORTUNITY,
      postScheduleDate: new Date(),
    };

    await transaction.insert(posts).values(insertValue);
    return;
  }

  async removePostOfOpportuntiy(
    opportunityId: string,
    transaction: PostgresJsDatabase<typeof schema>,
  ) {
    await transaction.delete(posts).where(eq(posts.opportunityId, opportunityId));
    return;
  }

  async update(postId: string, userId: string, workspaceId: string, updatePostDto: UpdatePostDto) {
    const post = await this.findOne(postId, userId);
    if (post.workspaceId !== workspaceId) throw unauthorized();

    const {
      categoryId,
      // countryIds,
      postStatus,
      postScheduleDate,
      tags: postAssociateTags,
      medias,
    } = updatePostDto;

    // Check if the category exists
    if (categoryId) {
      const categoryIdExist = await this.categoryService.findOne(categoryId);
      if (!categoryIdExist) throw itemNotFound(EntityName.CATEGORY);
    }

    const currentStatus = post.postStatus;

    const isValidTransition = postStatus
      ? (postStatus === PostStatus.SCHEDULED &&
          [PostStatus.DRAFT, PostStatus.SCHEDULED].includes(currentStatus)) ||
        (postStatus === PostStatus.DRAFT && currentStatus === PostStatus.DRAFT) ||
        (postStatus === PostStatus.PUBLISHED &&
          [PostStatus.DRAFT, PostStatus.SCHEDULED, PostStatus.PUBLISHED].includes(currentStatus))
      : true;

    if (!isValidTransition) throw invalidPostStatusTransitionException;

    // Post schedule date validation
    if (postScheduleDate) {
      // Validate that the new postScheduleDate is not in the past if the post is scheduled
      if (currentStatus === PostStatus.SCHEDULED && postStatus !== PostStatus.DRAFT) {
        const currentDate = new Date(); // Get the current date
        const scheduledDate = new Date(postScheduleDate); // Convert postScheduleDate to a Date object

        // Validate that the scheduled date is not in the past
        if (scheduledDate < currentDate) {
          throw postScheduleDateFutureRequired; // Scheduled date must be in the future
        }
      } else if (currentStatus === PostStatus.PUBLISHED) {
        // If the post is already published, remove postScheduleDate
        delete updatePostDto.postScheduleDate;
      }
    } else if (postStatus === PostStatus.SCHEDULED) {
      // If postStatus is SCHEDULED but no postScheduleDate is provided,
      // it implies the post may still have a future scheduled date if in draft. so check is it future or not
      const currentDate = new Date(); // Get the current date
      const scheduledDate = new Date(post.postScheduleDate); // Convert postScheduleDate to a Date object

      if (scheduledDate < currentDate) {
        throw postScheduleDateFutureRequired; // Throw an error or handle it as needed
      }
    }

    return this.drizzleDev.transaction(async (txn) => {
      // Update post details
      const [updatedPost] = await txn
        .update(posts)
        .set({ updatedBy: userId, ...updatePostDto })
        .where(eq(posts.id, postId))
        .returning();

      if (postAssociateTags) {
        // Handle tag updates
        const existingTags = await txn.query.postTags.findMany({
          where: and(eq(postTags.status, PostTagsStatus.ACTIVE), eq(postTags.postId, postId)),
          columns: {},
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        });

        const existingTagLabels = existingTags.map(({ tag: { label } }) => label.toLowerCase());

        const tagsToAdd = postAssociateTags.filter((tag) => !existingTagLabels.includes(tag));
        const tagsToRemove = existingTagLabels.filter((tag) => !postAssociateTags.includes(tag));

        await Promise.all([
          ...tagsToAdd.map(async (label) => {
            const tag = await txn.query.tags.findFirst({
              where: eq(sql`LOWER(${tags.label})`, label),
            });

            if (tag) {
              const existingPostTag = await txn.query.postTags.findFirst({
                where: and(
                  eq(postTags.postId, postId),
                  eq(postTags.tagId, tag.id),
                  eq(postTags.status, PostTagsStatus.INACTIVE),
                ),
              });

              // If the tag is inactive, update its status to active
              if (tag.status === TagsStatus.INACTIVE) {
                await txn
                  .update(tags) // Update the tag's status to active
                  .set({ status: TagsStatus.ACTIVE })
                  .where(eq(tags.id, tag.id));
              }

              await (existingPostTag
                ? txn
                    .update(postTags)
                    .set({ status: PostTagsStatus.ACTIVE })
                    .where(and(eq(postTags.postId, postId), eq(postTags.tagId, tag.id)))
                : txn.insert(postTags).values({ postId, tagId: tag.id }));
            } else {
              // Insert the tag if it does not exist and associate it with the post
              const [newTag] = await txn.insert(tags).values({ label }).returning();
              await txn.insert(postTags).values({ postId, tagId: newTag.id });
            }
          }),
          ...tagsToRemove.map(async (label) => {
            const tag = await txn.query.tags.findFirst({
              where: eq(sql`LOWER(${tags.label})`, label),
            });
            if (tag) {
              await txn
                .update(postTags)
                .set({ status: PostTagsStatus.INACTIVE })
                .where(and(eq(postTags.postId, postId), eq(postTags.tagId, tag.id)));
            }
          }),
        ]);
      }

      // TODO: HAVE TO MANAGE THIS LATER, ONCE THE COUNTRIES ASSOCIATED WITH SUBSCRIPTION
      // if (countryIds) {
      //   // Get active subscription countries for validation
      //   const subscriptionDetails =
      //     await this.subscriptionService.findSubscriptionDetailsWithWorkspaceId(workspaceId);

      //   // Verify and associate subscribed countries
      //   const areAllCountriesSubscribed = countryIds.every((country) =>
      //     subscriptionDetails.countryIds.includes(country),
      //   );
      //   if (!areAllCountriesSubscribed) throw postInUnsubscribedCountryException;

      //   // Fetch existing post countries (both active and inactive) associated with the post
      //   const existingPostCountries = await txn.query.postCountries.findMany({
      //     where: eq(postCountries.postId, postId),
      //     columns: {
      //       countryId: true,
      //       status: true,
      //     },
      //   });

      //   // Separate the countries into active and inactive sets
      //   const activeCountryIds = existingPostCountries
      //     .filter((country) => country.status === PostCountriesStatus.ACTIVE)
      //     .map((country) => country.countryId);
      //   const inactiveCountryIds = existingPostCountries
      //     .filter((country) => country.status === PostCountriesStatus.INACTIVE)
      //     .map((country) => country.countryId);

      //   // Determine countries to reactivate (if inactive but in payload) and countries to deactivate
      //   const countriesToReactivate = countryIds.filter((countryId) =>
      //     inactiveCountryIds.includes(countryId),
      //   );
      //   const countriesToDeactivate = activeCountryIds.filter(
      //     (countryId) => !countryIds.includes(countryId),
      //   );

      //   // Reactivate inactive countries that are in the current payload
      //   if (countriesToReactivate.length > 0) {
      //     await txn
      //       .update(postCountries)
      //       .set({ status: PostCountriesStatus.ACTIVE })
      //       .where(
      //         and(
      //           eq(postCountries.postId, postId),
      //           inArray(postCountries.countryId, countriesToReactivate),
      //         ),
      //       );
      //   }
      //   // Deactivate countries that are no longer in the payload
      //   if (countriesToDeactivate.length > 0) {
      //     await txn
      //       .update(postCountries)
      //       .set({ status: PostCountriesStatus.INACTIVE })
      //       .where(
      //         and(
      //           eq(postCountries.postId, postId),
      //           inArray(postCountries.countryId, countriesToDeactivate),
      //         ),
      //       );
      //   }

      //   // Insert new countries from the payload that aren’t already in postCountries (active or inactive)
      //   const newCountriesToAdd = countryIds.filter(
      //     (countryId) =>
      //       !activeCountryIds.includes(countryId) && !inactiveCountryIds.includes(countryId),
      //   );
      //   if (newCountriesToAdd.length > 0) {
      //     await txn
      //       .insert(postCountries)
      //       .values(newCountriesToAdd.map((countryId) => ({ countryId, postId })));
      //   }
      // }

      if (medias) {
        // Fetch existing media entries for the post
        const existingPostMedias = await txn.query.postMedias.findMany({
          where: eq(postMedias.postId, postId),
          columns: {
            mediaPath: true,
            status: true,
          },
        });

        // Separate media URLs by active and inactive statuses
        const activeMediaPaths = existingPostMedias
          .filter((media) => media.status === PostMediasStatus.ACTIVE)
          .map((media) => media.mediaPath);
        const inactiveMediaPaths = existingPostMedias
          .filter((media) => media.status === PostMediasStatus.INACTIVE)
          .map((media) => media.mediaPath);

        // Determine media to reactivate (if in payload but currently inactive) and to deactivate
        const mediaToReactivate = medias
          .map((media) => media.mediaPath)
          .filter((url) => inactiveMediaPaths.includes(url));
        const mediaToDeactivate = activeMediaPaths.filter(
          (url) => !medias.some((media) => media.mediaPath === url),
        );

        // Reactivate media URLs that are in the current payload but inactive
        if (mediaToReactivate.length > 0) {
          await txn
            .update(postMedias)
            .set({ status: PostMediasStatus.ACTIVE })
            .where(
              and(eq(postMedias.postId, postId), inArray(postMedias.mediaPath, mediaToReactivate)),
            );
        }

        // Deactivate media URLs no longer in the payload
        if (mediaToDeactivate.length > 0) {
          await txn
            .update(postMedias)
            .set({ status: PostMediasStatus.INACTIVE })
            .where(
              and(eq(postMedias.postId, postId), inArray(postMedias.mediaPath, mediaToDeactivate)),
            );

          // Then delete from S3
          await this.fileUploadService.deleteFiles(mediaToDeactivate);
        }

        // Insert new media entries only for URLs not found in active or inactive states
        const newMediaToAdd = medias.filter(
          (media) =>
            !activeMediaPaths.includes(media.mediaPath) &&
            !inactiveMediaPaths.includes(media.mediaPath),
        );
        if (newMediaToAdd.length > 0) {
          await txn.insert(postMedias).values(newMediaToAdd.map((media) => ({ postId, ...media })));
        }
      }

      return updatedPost;
    });
  }

  async softDelete(userId: string, workspaceId: string, postId: string) {
    const post = await this.findOne(postId, userId);
    if (post.workspaceId !== workspaceId) return unauthorized();

    if (!post) throw itemNotFound(EntityName.POST);

    const [res] = await this.drizzleDev
      .update(posts)
      .set({
        updatedBy: userId,
        postStatus: PostStatus.ARCHIVED,
        status: PostActiveStatus.INACTIVE,
      })
      .where(eq(posts.id, postId))
      .returning();

    //TODO: if we have to inactive all rows where have data associted to this post, have to do that.

    return res;
  }

  // CRUD operations for postMedia table
  async createPostMedia(postId: string, createPostMedia: PostMediaDto) {
    const [newMedia] = await this.drizzleDev
      .insert(postMedias)
      .values({
        postId,
        ...createPostMedia,
      })
      .returning();
    return newMedia;
  }

  async findPostMediaByPostId(postId: string) {
    return this.drizzleDev.query.postMedias.findMany({
      where: and(eq(postMedias.postId, postId), eq(postMedias.status, PostMediaStatus.ACTIVE)),
    });
  }
  // async softDeletePostView(id: string) {
  //   const view = await this.drizzleDev.query.postViews.findFirst({
  //     where: and(eq(postViews.id, id), eq(postViews.status, PostViewsStatus.ACTIVE)),
  //   });

  //   if (!view) throw itemNotFound(EntityName.POST_VIEW);

  //   const [res] = await this.drizzleDev
  //     .update(postViews)
  //     .set({
  //       status: PostViewsStatus.INACTIVE,
  //     })
  //     .where(eq(postViews.id, id))
  //     .returning();
  //   return res;
  // }
}
