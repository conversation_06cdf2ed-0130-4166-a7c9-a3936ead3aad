import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, count, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { postLikes } from '@/db/schema';

import { PostLikesStatus } from '@/constants/post-likes';
import { EntityType } from '@/constants/user-types';
import { EntityName } from '@/constants/entities';

import { itemNotFound } from '@/exceptions/common';

@Injectable()
export class PostLikesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findPostLikesCountByPostId(postId: string) {
    const [{ rowCount }] = await this.drizzleDev
      .select({ rowCount: count() })
      .from(postLikes)
      .where(and(eq(postLikes.postId, postId), eq(postLikes.status, PostLikesStatus.ACTIVE)));

    return rowCount;
  }

  async findPostLikesByPostId(postId: string) {
    return this.drizzleDev.query.postLikes.findMany({
      where: and(eq(postLikes.postId, postId), eq(postLikes.status, PostLikesStatus.ACTIVE)),
      columns: {},
    });
  }

  async createPostLike(
    postId: string,
    entityId: string,
    entityType: EntityType,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const existingLike = await db.query.postLikes.findFirst({
      where: and(eq(postLikes.postId, postId), eq(postLikes.entityId, entityId)),
    });

    if (existingLike) {
      if (existingLike.status === PostLikesStatus.INACTIVE) {
        await db
          .update(postLikes)
          .set({ status: PostLikesStatus.ACTIVE })
          .where(and(eq(postLikes.postId, postId), eq(postLikes.entityId, entityId)));
      }
      return { ...existingLike, status: PostLikesStatus.ACTIVE };
    }
    const [newLike] = await db
      .insert(postLikes)
      .values({ postId, entityId, entityType })
      .returning();

    return newLike;
  }

  async softDeletePostLike(
    postId: string,
    entityId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const [res] = await db
      .update(postLikes)
      .set({
        status: PostLikesStatus.INACTIVE,
      })
      .where(
        and(
          eq(postLikes.postId, postId),
          eq(postLikes.entityId, entityId),
          eq(postLikes.status, PostLikesStatus.ACTIVE),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_LIKE);

    return res;
  }
}
