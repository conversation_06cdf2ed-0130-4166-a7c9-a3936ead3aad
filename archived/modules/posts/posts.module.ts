import { Module } from '@nestjs/common';

import { PostsController } from './posts.controller';

import { PostLikesModule } from './post-likes/post-likes.module';
import { PostCommentsModule } from './post-comments/post-comments.module';
import { PostViewsModule } from './post-views/post-views.module';
import { CategoryModule } from '@/modules/category/category.module';
// import { SubscriptionsModule } from '@/modules/subscriptions/subscriptions.module';
import { FollowersModule } from '@/modules/followers/followers.module';

import { PostsService } from './posts.service';
import { NotificationsModule } from '../notifications/notifications.module';
import { NotificationsService } from '../notifications/notifications.service';
import { UtilsModule } from '../utils/utils.module';
import { PostReportModule } from './post-report/post-report.module';
import { PostPollsModule } from './post-polls/post-polls.module';
import { UsersModule } from '../users/users.module';
import { WorkspacesModule } from '../workspaces/workspaces.module';
// import { FollowersService } from '@/modules/followers/followers.service';

@Module({
  controllers: [PostsController],
  providers: [PostsService, NotificationsService],
  imports: [
    PostLikesModule,
    PostCommentsModule,
    PostViewsModule,
    NotificationsModule,
    PostReportModule,
    CategoryModule,
    UtilsModule,
    // SubscriptionsModule,
    UsersModule,
    WorkspacesModule,
    FollowersModule,
    PostPollsModule,
  ],
  exports: [PostsService],
})
export class PostsModule {}
