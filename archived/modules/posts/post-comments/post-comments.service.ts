import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, count, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { postComments } from '@/db/schema';

import { PostCommentsStatus } from '@/constants/post-comments';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';

import { itemNotFound } from '@/exceptions/common';

@Injectable()
export class PostCommentsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findPostCommentsCountByPostId(postId: string) {
    const [{ rowCount }] = await this.drizzleDev
      .select({ rowCount: count() })
      .from(postComments)
      .where(
        and(eq(postComments.postId, postId), eq(postComments.status, PostCommentsStatus.ACTIVE)),
      );

    return rowCount;
  }

  async findPostCommentsByPostId(postId: string) {
    return this.drizzleDev.query.postComments.findMany({
      where: and(
        eq(postComments.postId, postId),
        eq(postComments.status, PostCommentsStatus.ACTIVE),
      ),
      columns: {
        id: true,
        comment: true,
      },
    });
  }

  async createPostComment(
    postId: string,
    entityId: string,
    entityType: EntityType,
    comment: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const [newComment] = await db
      .insert(postComments)
      .values({
        postId,
        entityId,
        entityType,
        comment,
      })
      .returning();

    return newComment;
  }

  async updatePostComment(commentId: string, entityId: string, comment: string) {
    const [res] = await this.drizzleDev
      .update(postComments)
      .set({
        comment,
      })
      .where(
        and(
          eq(postComments.id, commentId),
          eq(postComments.entityId, entityId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }

  async softDeletePostComment(
    id: string,
    entityId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.drizzleDev;

    const [res] = await db
      .update(postComments)
      .set({
        status: PostCommentsStatus.INACTIVE,
      })
      .where(
        and(
          eq(postComments.id, id),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          eq(postComments.entityId, entityId),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }
}
