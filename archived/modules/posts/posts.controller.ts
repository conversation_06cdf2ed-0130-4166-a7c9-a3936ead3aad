import { Controller, Get, Post, Body, Param, Delete, Patch, Query, Inject } from '@nestjs/common';
import { ApiBearerAuth, ApiQ<PERSON>y, ApiTags } from '@nestjs/swagger';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import * as schema from '@/db/schema';

import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { CommentDto } from './dto/create-post-comment';
import { CreateReportedPostDto } from './dto/create-reported-post.dto';
import { FindTagsDto } from './dto/find-tags.dto';

import { AuthConstants } from '@/constants/auth';
import { PostStatus, PostType } from '@/constants/posts';
import { PostNotificationsType } from '@/constants/post-notifications';
import { EntityType, Roles } from '@/constants/user-types';
import { EntityName } from '@/constants/entities';

import { User } from '@/decorators/user.decorator';
import { ActiveWorkspace } from '@/decorators/active-workspace.decorator';
import { Public } from '@/decorators/public.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import { itemNotFound } from '@/exceptions/common';
import { postCreatorCannotRemoveOwnPost, reportedPostIsNotModifiable } from '@/exceptions/posts';

import { PostsService } from './posts.service';
import { PostViewsService } from './post-views/post-views.service';
import { PostLikesService } from './post-likes/post-likes.service';
import { PostCommentsService } from './post-comments/post-comments.service';
import { PostReportService } from './post-report/post-report.service';
import { PostPollsService } from './post-polls/post-polls.service';
import { NotificationsService } from '@/modules/notifications/notifications.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UsersService } from '@/modules/users/users.service';

@Controller('posts')
@ApiBearerAuth()
@ApiTags('posts')
export class PostsController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly postsService: PostsService,
    private readonly postCommentsService: PostCommentsService,
    private readonly postLikesService: PostLikesService,
    private readonly postViewsService: PostViewsService,
    private readonly notificationService: NotificationsService,
    private readonly reportedPostsService: PostReportService,
    private readonly postsPollsService: PostPollsService,
    private readonly workspacesService: WorkspacesService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  @ActiveWorkspace()
  create(
    @Body() createPostDto: CreatePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.postsService.create(userId, workspaceId, createPostDto);
  }

  @Patch(':postId')
  @ActiveWorkspace()
  async update(
    @Param('postId') postId: string,
    @Body() updatePostDto: UpdatePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    const isReported = await this.reportedPostsService.isReported(postId);
    if (isReported) throw reportedPostIsNotModifiable();

    return this.postsService.update(postId, userId, workspaceId, updatePostDto);
  }

  @Delete(':postId')
  @ActiveWorkspace()
  softDelete(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.postsService.softDelete(userId, workspaceId, postId);
  }

  @Get()
  @Public()
  async findAll(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('tag') tag?: string,
  ) {
    const response = await this.postsService.findAll(
      limit || 10,
      offset || 0,
      tag,
      workspaceId ?? userId,
    );
    return response;
  }

  @Get(':postId')
  @Public()
  findPost(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.postsService.findOne(postId, workspaceId ?? userId);
  }

  @Get('post-type/:postType')
  findPostsByPostType(
    @Param('postType') postType: PostType,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    if (limit === null && offset === null) {
      return this.postsService.findAllByPostType(userId, postType);
    }
    return this.postsService.findAllByPostType(userId, postType, limit || 10, offset || 0);
  }

  @Get('workspace/lists')
  @ApiQuery({
    name: 'postStatus',
    required: false,
    type: String,
    enum: PostStatus,
  })
  findByWorkspaceId(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query('postStatus') postStatus?: PostStatus,
  ) {
    return this.postsService.findByWorkspace(workspaceId, postStatus);
  }

  @Get('user/:userId')
  @ApiQuery({
    name: 'postStatus',
    required: false,
    type: String,
    enum: PostStatus,
  })
  findByUser(
    @Param('userId') userId: string,
    @Query('postStatus') postStatus?: PostStatus,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    if (limit === null && offset === null) {
      return this.postsService.findByUser(userId, postStatus);
    }
    return this.postsService.findByUser(userId, postStatus, limit || 10, offset || 0);
  }

  @Get('user/post-type/:postType')
  findByOwnWorkspaceAndPostType(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Param('postType') postType: PostType,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    if (limit === null && offset === null) {
      return this.postsService.findByWorkspaceAndPostType(workspaceId, userId, postType);
    }
    return this.postsService.findByWorkspaceAndPostType(
      workspaceId,
      userId,
      postType,
      limit || 10,
      offset || 0,
    );
  }

  @Get('user/post-type/:postType/:workspaceId')
  findByWorkspaceAndPostType(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Param('postType') postType: PostType,
    @Param('workspaceId') workspaceId: PostType,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    if (limit === null && offset === null) {
      return this.postsService.findByWorkspaceAndPostType(workspaceId, userId, postType);
    }
    return this.postsService.findByWorkspaceAndPostType(
      workspaceId,
      userId,
      postType,
      limit || 10,
      offset || 0,
    );
  }

  @Get('analytics/:postId')
  async getAnalytics(@Param('postId') postId: string) {
    const comments = await this.postCommentsService.findPostCommentsCountByPostId(postId);
    const likes = await this.postLikesService.findPostLikesCountByPostId(postId);
    const views = await this.postViewsService.findPostViewsCountByPostId(postId);

    return {
      views,
      likes,
      comments,
    };
  }

  // ---------***************Post Tags**********************------------------

  @Get('tags/lists')
  findPostTags(@Query() query: FindTagsDto) {
    return this.postsService.findTags(query.searchQuery);
  }

  // ---------***************Post comments******************------------------
  @Get('comments/:postId')
  getPostComments(@Param('postId') postId: string) {
    return this.postCommentsService.findPostCommentsByPostId(postId);
  }

  @Post('comments/:postId')
  @ActiveWorkspace()
  createPostComment(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() body: CommentDto,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const isExist = await (workspaceId
        ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
        : this.usersService.findUserById(userId));

      if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

      const commentData = await this.postCommentsService.createPostComment(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        body.comment,
        txn,
      );

      const postDetails = await this.postsService.findOne(postId);

      if (postDetails.workspaceId !== workspaceId) {
        await this.notificationService.createCommentNotification(
          {
            entityId: workspaceId ?? userId,
            entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
            postId,
            type: PostNotificationsType.COMMENT,
            commentId: commentData.id,
          },
          txn,
        );
      }

      return commentData;
    });
  }

  @Patch('comments/:commentId')
  @ActiveWorkspace()
  updatePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() body: CommentDto,
  ) {
    return this.postCommentsService.updatePostComment(
      commentId,
      workspaceId ?? userId,
      body.comment,
    );
  }

  @Delete('comments/:commentId')
  @ActiveWorkspace()
  softDeletePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const commentData = await this.postCommentsService.softDeletePostComment(
        commentId,
        workspaceId ?? userId,
        txn,
      );

      await this.notificationService.invalidatePostNotification(
        {
          entityId: workspaceId ?? userId,
          postId: commentData.postId,
          type: PostNotificationsType.COMMENT,
          commentId: commentData.id,
        },
        txn,
      );

      return commentData;
    });
  }

  // ---------***************Post likes******************------------------
  @Get('likes/:postId')
  getLikesForPost(@Param('postId') postId: string) {
    return this.postLikesService.findPostLikesByPostId(postId);
  }

  @Post('likes/:postId')
  @ActiveWorkspace()
  async createPostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const isExist = await (workspaceId
      ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
      : this.usersService.findUserById(userId));

    if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

    return this.drizzleDev.transaction(async (txn) => {
      const likeData = await this.postLikesService.createPostLike(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        txn,
      );

      const postDetails = await this.postsService.findOne(postId);

      if (postDetails.workspaceId !== workspaceId) {
        await this.notificationService.createLikeNotification(
          {
            entityId: workspaceId ?? userId,
            entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
            postId,
            type: PostNotificationsType.LIKE,
          },
          txn,
        );
      }

      return likeData;
    });
  }

  @Delete('likes/:postId')
  @ActiveWorkspace()
  softDeletePostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const likeData = await this.postLikesService.softDeletePostLike(
        postId,
        workspaceId ?? userId,
        txn,
      );

      await this.notificationService.invalidatePostNotification(
        {
          entityId: workspaceId ?? userId,
          postId: likeData.postId,
          type: PostNotificationsType.LIKE,
        },
        txn,
      );

      return likeData;
    });
  }

  // ---------***************Post Views******************------------------
  @Post('views/:postId')
  @ActiveWorkspace()
  createPostView(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.postViewsService.createOrIncrementPostView(postId, userId);
  }

  @Get('views/:postId')
  findPostViews(@Param('postId') postId: string) {
    return this.postViewsService.findPostViewsByPostId(postId);
  }

  // ---------***************Post Report******************------------------

  @Post('report-post')
  async createReportedPost(@Body() dto: CreateReportedPostDto, @User('userId') userId: string) {
    const postDetails = await this.postsService.findOne(dto.postId);
    if (postDetails.publishedBy.id === userId) throw postCreatorCannotRemoveOwnPost();

    return this.reportedPostsService.create(dto, userId);
  }

  @Get('report-post/list')
  @UserRoles(Roles.SUPER_ADMIN)
  async findAllReportedPost() {
    return this.reportedPostsService.findAll();
  }

  @Get('report-post/:postId')
  @UserRoles(Roles.SUPER_ADMIN)
  async findOneReportedPost(@Param('postId') postId: string) {
    return this.reportedPostsService.findOne(postId);
  }

  @Delete('report-post/:postId')
  @UserRoles(Roles.SUPER_ADMIN)
  async removeReportedPost(@Param('postId') postId: string) {
    await this.reportedPostsService.softDelete(postId);
  }

  // ---------***************Post Polls******************------------------

  @Post('polls/:postId/:option')
  async createVote(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Param('postId') postId: string,
    @Param('option') option: string,
  ) {
    return this.postsPollsService.createVote(
      postId,
      option,
      workspaceId ?? userId,
      workspaceId ? EntityType.WORKSPACE : EntityType.USER,
    );
  }

  @Get('polls/:postId/:option')
  async getVoteCountForOption(@Param('postId') postId: string, @Param('option') option: string) {
    return this.postsPollsService.getCountForOption(postId, option);
  }

  @Get('polls/:postId')
  async getVoteCountForPost(@Param('postId') postId: string) {
    return this.postsPollsService.getPollsCountForPost(postId);
  }

  @Get('polls/user/:postId')
  async getVoteForUser(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.postsPollsService.getVoteForUser(workspaceId ?? userId, postId);
  }

  @Delete('polls/:postId/')
  async removeVoteForUserPost(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.postsPollsService.removeVoteForUser(postId, workspaceId ?? userId);
  }
}
