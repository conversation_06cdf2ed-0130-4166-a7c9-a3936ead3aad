import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsString, IsNotEmpty } from 'class-validator';

export class CommentDto {
  @ApiProperty({
    name: 'comment',
    type: 'string',
    required: true,
    example: 'very informative',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  comment: string;
}
