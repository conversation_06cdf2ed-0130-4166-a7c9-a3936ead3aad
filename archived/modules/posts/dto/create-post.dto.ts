import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsEnum,
  IsArray,
  IsOptional,
  ValidateNested,
  IsIn,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
  ValidateIf,
  ArrayMinSize,
  ArrayUnique,
  IsDate,
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { PostStatus, PostType } from '@/constants/posts';

import { PostMediaDto } from './create-post-media.dto';

@ValidatorConstraint({ async: false })
class IsFutureDateIfScheduledConstraint implements ValidatorConstraintInterface {
  validate(postScheduleDate: string, args: ValidationArguments) {
    const { object } = args;
    if ((object as any).postStatus === PostStatus.SCHEDULED && postScheduleDate) {
      const scheduleDate = new Date(postScheduleDate);
      const now = new Date();
      now.setMinutes(now.getMinutes() + 10); // Ensure it's at least 10 minutes later
      return scheduleDate > now;
    }
    return true;
  }

  defaultMessage() {
    return `postScheduleDate must be at least 10 minutes in the future if postStatus is set to scheduled`;
  }
}

export function IsFutureDateIfScheduled(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsFutureDateIfScheduledConstraint,
    });
  };
}

export class CreatePostDto {
  @ApiProperty({
    name: 'content',
    type: 'string',
    required: false,
    example: 'This is the content of the post.',
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  content?: string;

  @ApiProperty({
    name: 'categoryId',
    type: 'string',
    required: true,
    example: '8da4d90e-df5b-4a0c-b75e-5eeb823df390',
  })
  @IsUUID()
  @IsNotEmpty()
  categoryId: string;

  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false, // marked as not required here but validated conditionally
    example: '2024-12-31T23:59:59Z',
    description: 'send current date, if there is no need for schedule a post',
  })
  @IsDate()
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED)
  @IsFutureDateIfScheduled()
  @Transform(({ obj, value }) => {
    const transformedValue = obj.postStatus === PostStatus.PUBLISHED ? new Date() : new Date(value);
    return transformedValue;
  })
  postScheduleDate: Date;

  @ApiProperty({
    name: 'countryIds',
    type: [String],
    required: true,
    example: ['678e1234-e89b-12d3-a456-************'],
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsUUID('all', { each: true }) // Validate each item as UUID v4
  @Type(() => String)
  countryIds: string[];

  @ApiProperty({
    name: 'postStatus',
    enum: PostStatus,
    required: true,
    example: PostStatus.DRAFT,
  })
  @IsNotEmpty()
  @IsEnum(PostStatus)
  @IsIn([PostStatus.DRAFT, PostStatus.PUBLISHED, PostStatus.SCHEDULED], {
    message: 'postStatus must be one of the following: draft, published, scheduled',
  })
  postStatus: PostStatus;

  @ApiProperty({
    name: 'postType',
    enum: PostType,
    required: true,
    example: PostType.POST,
  })
  @IsNotEmpty()
  @IsEnum(PostType)
  @IsIn([PostType.ARTICLE, PostType.POST, PostType.POLL], {
    message: 'post type must be either post, article or poll',
  })
  postType: PostType;

  @ApiProperty({
    name: 'tags',
    type: [String],
    required: true,
    example: ['innovation', 'news'],
  })
  @IsArray()
  @IsOptional()
  @Type(() => String)
  @Transform(({ value }: TransformFnParams) =>
    value?.map((val: string) => val?.trim().toLowerCase()),
  )
  @ArrayUnique()
  tags?: string[];

  @ApiProperty({
    name: 'medias',
    type: [PostMediaDto],
    required: false,
    description: 'List of media details including media type and path.',
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PostMediaDto)
  @ArrayUnique((media: PostMediaDto) => media.mediaPath, {
    message: 'Each media path must be unique in postMediaDetails.',
  })
  medias: PostMediaDto[];
}
