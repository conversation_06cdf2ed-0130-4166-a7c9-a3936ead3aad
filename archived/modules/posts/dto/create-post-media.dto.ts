import { IsIn, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { MEDIA_TYPES_INFO } from '@/constants/posts';

export class PostMediaDto {
  @ApiProperty({
    name: 'mediaType',
    enum: [...MEDIA_TYPES_INFO.image.accept, ...MEDIA_TYPES_INFO.video.accept],
    example: MEDIA_TYPES_INFO.image.accept[0],
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsIn([...MEDIA_TYPES_INFO.image.accept, ...MEDIA_TYPES_INFO.video.accept])
  mediaType: string;

  @ApiProperty({
    name: 'mediaPath',
    type: 'string',
    required: true,
    example: '/uploads/images/sample.png',
  })
  @IsString()
  @IsNotEmpty()
  mediaPath: string;
}
