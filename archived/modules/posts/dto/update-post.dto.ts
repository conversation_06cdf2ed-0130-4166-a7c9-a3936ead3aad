import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';

import { PostStatus } from '@/constants/posts';

import { CreatePostDto, IsFutureDateIfScheduled } from './create-post.dto';
export class UpdatePostDto extends PartialType(
  OmitType(CreatePostDto, ['postScheduleDate'] as const),
) {
  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false,
    example: '2024-12-31T23:59:59Z',
  })
  @Transform(({ obj }) =>
    obj.postStatus === PostStatus.PUBLISHED ? new Date() : new Date(obj.postScheduleDate),
  )
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED && obj.postScheduleDate)
  @IsFutureDateIfScheduled()
  postScheduleDate?: Date;
}
