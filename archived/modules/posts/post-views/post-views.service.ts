import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, sql, sum } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { postViews } from '@/db/schema';

import { PostViewsStatus } from '@/constants/post-views';

@Injectable()
export class PostViewsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findPostViewsCountByPostId(postId: string) {
    const counts = await this.drizzleDev
      .select({
        value: sum(postViews.viewCount),
      })
      .from(postViews)
      .where(and(eq(postViews.postId, postId), eq(postViews.status, PostViewsStatus.ACTIVE)));

    return counts[0].value ? +counts[0].value : 0;
  }

  async findPostViewsByPostId(postId: string) {
    return this.drizzleDev.query.postViews.findMany({
      where: and(eq(postViews.postId, postId), eq(postViews.status, PostViewsStatus.ACTIVE)),
      columns: { viewCount: true },
      with: {
        user: {
          columns: {
            id: true,
            firstName: true,
          },
        },
      },
    });
  }

  async createOrIncrementPostView(postId: string, userId: string) {
    const viewRowExist = await this.drizzleDev.query.postViews.findFirst({
      where: and(eq(postViews.postId, postId), eq(postViews.userId, userId)),
    });

    await (viewRowExist
      ? this.drizzleDev
          .update(postViews)
          .set({
            viewCount: sql`${postViews.viewCount} + 1`,
          })
          .where(
            and(
              eq(postViews.postId, postId),
              eq(postViews.userId, userId),
              eq(postViews.status, PostViewsStatus.ACTIVE),
            ),
          )
      : this.drizzleDev.insert(postViews).values({ postId, userId }));

    return true;
  }
}
