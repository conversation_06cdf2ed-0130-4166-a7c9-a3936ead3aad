import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import { CreateWaitlistDto } from './dto/create-waitlist.dto';

import * as schema from '@/db/schema';
import { waitlists } from '@/db/schema';

import { WaitlistStatus } from '@/constants/waitlists';
import { EntityName } from '@/constants/entities';

import { itemAlreadyExists } from '@/exceptions/common';

@Injectable()
export class WaitlistsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(createWaitlistDto: CreateWaitlistDto) {
    const waitlistRow = await this.drizzleDev.query.waitlists.findFirst({
      where: and(
        eq(waitlists.email, createWaitlistDto.email),
        eq(waitlists.status, WaitlistStatus.ACTIVE),
      ),
    });

    if (waitlistRow) throw itemAlreadyExists(EntityName.USER);

    const [newWaitingListEntry] = await this.drizzleDev
      .insert(waitlists)
      .values({
        ...createWaitlistDto,
      })
      .returning();

    return newWaitingListEntry;
  }

  async findAll() {
    return this.drizzleDev.query.waitlists.findMany({
      where: eq(waitlists.status, WaitlistStatus.ACTIVE),
    });
  }
}
