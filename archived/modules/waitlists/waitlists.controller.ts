import { <PERSON>, Get, Post, Body } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { WaitlistsService } from './waitlists.service';

import { CreateWaitlistDto } from './dto/create-waitlist.dto';

import { Public } from '@/decorators/public.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import { Roles } from '@/constants/user-types';

@Controller('waitlists')
@ApiTags('waitlists')
@ApiBearerAuth()
export class WaitlistsController {
  constructor(private readonly waitlistsService: WaitlistsService) {}

  @Post()
  @Public()
  create(@Body() createWaitlistDto: CreateWaitlistDto) {
    return this.waitlistsService.create(createWaitlistDto);
  }

  @Get()
  @UserRoles(Roles.OWNER, Roles.SUPER_ADMIN)
  findAll() {
    return this.waitlistsService.findAll();
  }
}
