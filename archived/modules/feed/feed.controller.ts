import { Controller, Get } from '@nestjs/common';
import { FeedService } from './feed.service';
import { User } from '@/decorators/user.decorator';
import { AuthConstants } from '@/constants/auth';

@Controller('feed')
export class FeedController {
  constructor(private readonly feedService: FeedService) {}

  @Get('/widgets/workspaces')
  async getRandomWorkspaces(@User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string) {
    return this.feedService.getRandomWorkspaces(userId);
  }

  @Get('/widgets/posts')
  async getRandomPosts(@User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string) {
    return this.feedService.getRandomPosts(userId);
  }

  @Get('/widgets/article')
  async getRandomArticle(@User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string) {
    return this.feedService.getRandomArticle(userId);
  }

  @Get('/widgets/tags')
  async getRandomTags() {
    return this.feedService.getRandomTags();
  }
}
