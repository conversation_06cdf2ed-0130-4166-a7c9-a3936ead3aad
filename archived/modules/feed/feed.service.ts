import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '@/db/schema';
import { and, eq, isNull, or, sql } from 'drizzle-orm';
import {
  posts,
  reportedPosts,
  postViews,
  postTags,
  workspaces,
  tags,
  workspaceFollowers,
} from '@/db/schema';
import { WorkspacesStatus } from '@/constants/workspaces';
import { PostActiveStatus, PostStatus, PostType } from '@/constants/posts';
import { TagsStatus } from '@/constants/tags';
import { PostLikesStatus } from '@/constants/post-likes';
import { PostTagsStatus } from '@/constants/post-tags';
import { FollowerStatus } from '@/constants/followers';

@Injectable()
export class FeedService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async getRandomWorkspaces(entityId: string) {
    return this.drizzleDev
      .select()
      .from(workspaces)
      .leftJoin(
        workspaceFollowers,
        and(
          eq(workspaces.id, workspaceFollowers.workspaceId),
          eq(workspaceFollowers.entityId, entityId),
        ),
      )
      .where(
        and(
          eq(workspaces.status, WorkspacesStatus.ACTIVE),
          or(
            isNull(workspaceFollowers.workspaceId),
            eq(workspaceFollowers.status, FollowerStatus.INACTIVE),
          ),
        ),
      )
      .orderBy(sql`RANDOM()`)
      .limit(5);
  }

  async getRandomPosts(userId: string) {
    return this.drizzleDev.query.posts.findMany({
      where: and(
        eq(posts.postType, PostType.POST),
        eq(posts.status, PostActiveStatus.ACTIVE),
        eq(posts.postStatus, PostStatus.PUBLISHED),
        isNull(
          this.drizzleDev
            .select({ postId: reportedPosts.postId })
            .from(reportedPosts)
            .where(and(eq(reportedPosts.reporterId, userId), eq(reportedPosts.postId, posts.id))),
        ),
      ),
      orderBy: sql`RANDOM()`,
      limit: 5,
      with: {
        publishedBy: {
          columns: {
            id: true,
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostLikesStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
    });
  }

  async getRandomArticle(userId: string) {
    return this.drizzleDev.query.posts.findMany({
      where: and(
        eq(posts.postType, PostType.ARTICLE),
        eq(posts.status, PostActiveStatus.ACTIVE),
        eq(posts.postStatus, PostStatus.PUBLISHED),
        isNull(
          this.drizzleDev
            .select({ postId: reportedPosts.postId })
            .from(reportedPosts)
            .where(and(eq(reportedPosts.reporterId, userId), eq(reportedPosts.postId, posts.id))),
        ),
      ),
      orderBy: sql`RANDOM()`,
      limit: 5,
      with: {
        publishedBy: {
          columns: {
            id: true,
            email: true,
            firstName: true,
            middleName: true,
            lastName: true,
          },
        },
        workspace: {
          columns: {
            type: true,
            label: true,
          },
        },
        postViews: {
          where: eq(postViews.status, PostLikesStatus.ACTIVE),
          columns: {
            userId: true,
          },
        },
        postTags: {
          columns: {},
          where: eq(postTags.status, PostTagsStatus.ACTIVE),
          with: {
            tag: {
              columns: {
                label: true,
              },
            },
          },
        },
      },
    });
  }

  async getRandomTags() {
    return this.drizzleDev.query.tags.findMany({
      where: eq(tags.status, TagsStatus.ACTIVE),
      orderBy: sql`RANDOM()`,
      limit: 5,
    });
  }
}
