import { SQL, and, eq, sql, ne, isNull } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { Inject, Injectable } from '@nestjs/common';

import { CreateSubtypeDto } from './dto/create-subtype.dto';
import { UpdateSubtypeDto } from './dto/update-subtype.dto';
import { GetSubtypesQueryDto } from './dto/get-subtypes.dto';

import * as schema from '@/db/schema';
import { subtypes } from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import {
  parentSubtypeBelongsToDifferentAccountType,
  parentSubtypeNotFound,
  subtypeIdOrNameRequired,
} from '@/exceptions/subtypes';

import { EntityName } from '@/constants/entities';
import { SubtypesStatus } from '@/constants/subtypes';

import { AccountTypeMap, Subtype } from '@/interfaces/subtypes';

@Injectable()
export class SubtypesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(createSubtypeDto: CreateSubtypeDto) {
    const where: SQL[] = [eq(sql`lower(${subtypes.name})`, createSubtypeDto.name?.toLowerCase())];

    if (createSubtypeDto.parentId !== undefined) {
      const parentSubtype = await this.drizzleDev.query.subtypes.findFirst({
        where: eq(subtypes.id, createSubtypeDto.parentId),
      });

      if (!parentSubtype) throw parentSubtypeNotFound;

      if (parentSubtype.accountType !== createSubtypeDto.accountType)
        throw parentSubtypeBelongsToDifferentAccountType;

      where.push(
        createSubtypeDto.parentId
          ? eq(subtypes.parentId, createSubtypeDto.parentId)
          : isNull(subtypes.parentId),
      );
    }

    const existingSubtype = await this.drizzleDev.query.subtypes.findFirst({
      where: and(...where),
    });

    if (existingSubtype && existingSubtype.status === SubtypesStatus.ACTIVE)
      throw itemAlreadyExists(EntityName.SUBTYPE);

    if (existingSubtype) {
      if (existingSubtype.status === SubtypesStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.SUBTYPE);
      } else {
        const [updatedRolePermission] = await this.drizzleDev
          .update(subtypes)
          .set({
            status: SubtypesStatus.ACTIVE,
          })
          .where(and(eq(subtypes.id, existingSubtype.id)))
          .returning();

        return updatedRolePermission;
      }
    }

    const [newSubtype] = await this.drizzleDev
      .insert(subtypes)
      .values(createSubtypeDto)
      .returning();

    return newSubtype;
  }

  async getSubtypes({
    accountType,
    subtypeId,
  }: GetSubtypesQueryDto): Promise<AccountTypeMap | Subtype[]> {
    const where: SQL[] = [eq(subtypes.status, SubtypesStatus.ACTIVE)];

    if (accountType) {
      where.push(eq(subtypes.accountType, accountType));
    }

    const subtypesDatas = await this.drizzleDev.query.subtypes.findMany({
      where: and(...where),
      columns: {
        id: true,
        name: true,
        parentId: true,
        accountType: true,
      },
    });

    const subtypesMap: Record<string, Subtype & { children: Subtype[] }> = {};
    subtypesDatas.forEach((subtype) => {
      subtypesMap[subtype.id] = { ...subtype, children: [] };
    });

    subtypesDatas.forEach((subtype) => {
      if (subtype.parentId && subtypesMap[subtype.parentId]) {
        subtypesMap[subtype.parentId].children.push(subtypesMap[subtype.id]);
      }
    });

    // If an ID is provided, return only the children of that subtype
    if (subtypeId) {
      return subtypesMap[subtypeId] ? subtypesMap[subtypeId].children : [];
    }

    const accountTypeMap: Record<string, Subtype[]> = {};

    subtypesDatas.forEach((subtype) => {
      if (!subtype.parentId) {
        if (!accountTypeMap[subtype.accountType]) {
          accountTypeMap[subtype.accountType] = [];
        }
        accountTypeMap[subtype.accountType].push(subtypesMap[subtype.id]);
      }
    });

    // If accountType is provided, return only that accountType group
    if (accountType) {
      return accountTypeMap[accountType] || [];
    }

    // Otherwise, return the entire accountTypeMap with all subtypes
    return accountTypeMap;
  }

  async findOne(subtypeId?: string, name?: string) {
    if (!subtypeId && !name) {
      throw subtypeIdOrNameRequired;
    }

    const subtype = await this.drizzleDev.query.subtypes.findFirst({
      where: and(
        subtypeId ? eq(subtypes.id, subtypeId) : undefined,
        name ? eq(subtypes.name, name) : undefined,
        eq(subtypes.status, SubtypesStatus.ACTIVE),
      ),
    });

    if (!subtype) itemNotFound(EntityName.SUBTYPE);

    return subtype;
  }

  async update(subtypeId: string, updateSubtypeDto: UpdateSubtypeDto) {
    const existingSubtype = await this.drizzleDev.query.subtypes.findFirst({
      where: and(eq(subtypes.id, subtypeId), eq(subtypes.status, SubtypesStatus.ACTIVE)),
    });

    if (!existingSubtype) {
      throw itemNotFound(EntityName.SUBTYPE);
    }

    const where: SQL[] = [
      eq(sql`lower(${subtypes.name})`, updateSubtypeDto.name.toLowerCase()),
      eq(subtypes.status, SubtypesStatus.ACTIVE),
    ];

    const existingParentId = existingSubtype.parentId;

    if (existingParentId) {
      where.push(eq(subtypes.parentId, existingParentId));
    } else {
      where.push(isNull(subtypes.parentId));
    }

    const duplicateSubtype = await this.drizzleDev.query.subtypes.findFirst({
      where: and(...where, ne(subtypes.id, subtypeId)),
    });

    if (duplicateSubtype) {
      throw itemAlreadyExists(EntityName.SUBTYPE);
    }

    // Update the subtype with new values
    const [updatedSubtype] = await this.drizzleDev
      .update(subtypes)
      .set(updateSubtypeDto)
      .where(eq(subtypes.id, subtypeId))
      .returning();

    return updatedSubtype;
  }

  async remove(subtypeId: string) {
    const [res] = await this.drizzleDev
      .update(subtypes)
      .set({
        status: SubtypesStatus.INACTIVE,
      })
      .where(and(eq(subtypes.id, subtypeId), eq(subtypes.status, SubtypesStatus.ACTIVE)))
      .returning();

    if (!res) throw itemNotFound(EntityName.WORKSPACE_FILE);

    return res;
  }
}
