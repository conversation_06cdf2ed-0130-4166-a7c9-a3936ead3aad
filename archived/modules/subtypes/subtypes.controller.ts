import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';

import { SubtypesService } from './subtypes.service';

import { CreateSubtypeDto } from './dto/create-subtype.dto';
import { UpdateSubtypeDto } from './dto/update-subtype.dto';
import { GetSubtypesQueryDto } from './dto/get-subtypes.dto';

@Controller('subtypes')
@ApiTags('subtypes')
@ApiBearerAuth()
export class SubtypesController {
  constructor(private readonly subtypesService: SubtypesService) {}

  @Post()
  create(@Body() createSubtypeDto: CreateSubtypeDto) {
    return this.subtypesService.create(createSubtypeDto);
  }

  @Get()
  findAll(@Query() query: GetSubtypesQueryDto) {
    return this.subtypesService.getSubtypes(query);
  }

  @Get(':subtypeId')
  findOne(@Param('subtypeId') id: string) {
    return this.subtypesService.findOne(id);
  }

  @Patch(':subtypeId')
  update(@Param('subtypeId') id: string, @Body() updateSubtypeDto: UpdateSubtypeDto) {
    return this.subtypesService.update(id, updateSubtypeDto);
  }

  @Delete(':subtypeId')
  remove(@Param('subtypeId') id: string) {
    return this.subtypesService.remove(id);
  }
}
