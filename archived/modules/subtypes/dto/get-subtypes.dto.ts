import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';

import { AccountType } from '@/constants/user-types';

export class GetSubtypesQueryDto {
  @ApiPropertyOptional({
    description: 'The subtype ID (UUID format)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  subtypeId?: string;

  @ApiPropertyOptional({
    description: 'The opportunity type',
    example: AccountType.ORGANISATION,
  })
  @IsOptional()
  @IsEnum(AccountType, {
    message: `type must be one of the following values: ${Object.values(AccountType).join(', ')}`,
  })
  accountType?: AccountType;
}
