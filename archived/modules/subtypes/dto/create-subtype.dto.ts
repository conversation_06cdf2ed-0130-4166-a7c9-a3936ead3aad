import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

import { AccountType } from '@/constants/user-types';

export class CreateSubtypeDto {
  @ApiProperty({
    name: 'name',
    type: 'string',
    required: true,
    example: 'Senior Surgeon',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiProperty({
    name: 'accountType',
    type: 'string',
    required: true,
    example: AccountType.PROFESSIONAL,
  })
  @IsNotEmpty()
  @IsEnum(AccountType)
  accountType: AccountType;

  @ApiProperty({
    name: 'parentId',
    type: 'uuid',
    required: false,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsString()
  @IsOptional()
  @IsUUID()
  parentId?: string;
}
