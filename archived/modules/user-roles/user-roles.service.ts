import { Injectable, Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, isNotNull, isNull } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';
import { userPermissions, userRoles, rolePermissions } from '@/db/schema';

import { CreateUserRolesDtoWith } from './dto/create-user-role.dto';
import { UpdateUserRolesDto } from './dto/update-user-role.dto';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { UserRolesActiveStatus, UserRolesStatus } from '@/constants/user-roles';
import { RolesPermissionStatus } from '@/constants/roles-permissions';
import { UserPermissionStatus } from '@/constants/user-permissions';
import { AuthConstants } from '@/constants/auth';

import { UserData } from '@/interfaces/auth';

import { GlobalRoleDto, WorkspaceRoleDto } from '@/modules/users/dto/create-user.dto';

@Injectable()
export class UserRolesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async createUserRole(
    createUserRolesDto: CreateUserRolesDtoWith,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      const existingUserRoles = await tx.query.userRoles.findFirst({
        where: and(
          eq(userRoles.userId, createUserRolesDto.userId),
          eq(userRoles.roleId, createUserRolesDto.roleId),
          createUserRolesDto.workspaceId
            ? eq(userRoles.workspaceId, createUserRolesDto.workspaceId)
            : undefined,
        ),
      });

      let userRoleDetails;

      // TODO: update here to set the workspace id if passed from FE
      if (existingUserRoles) {
        if (existingUserRoles.status === UserRolesStatus.INACTIVE) {
          const [existingUserRole] = await tx
            .update(userRoles)
            .set({
              status: UserRolesStatus.ACTIVE,
            })
            .where(eq(userRoles.id, existingUserRoles.id))
            .returning();

          userRoleDetails = existingUserRole;
        } else {
          throw itemAlreadyExists(EntityName.USER_ROLES);
        }
      } else {
        const [newUserRole] = await tx
          .insert(userRoles)
          .values({
            ...createUserRolesDto,
            status: UserRolesStatus.ACTIVE,
          })
          .returning();

        userRoleDetails = newUserRole;
      }

      if (createUserRolesDto.permissionIds) {
        for (const permission of createUserRolesDto.permissionIds) {
          try {
            await this.assignPermissionToUserRole((userRoleDetails as any).id, permission, tx);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (error) {
            // Handle the error if necessary
          }
        }

        return (userRoleDetails as any).id;
      }

      // assigning permissions from rolesPermissions table into userPermissions table.
      const permissionsFromRolesPermission = await tx.query.rolePermissions.findMany({
        where: and(
          eq(rolePermissions.roleId, createUserRolesDto.roleId),
          eq(rolePermissions.status, RolesPermissionStatus.ACTIVE),
        ),
      });

      for (const permission of permissionsFromRolesPermission) {
        try {
          await this.assignPermissionToUserRole(
            (userRoleDetails as any).id,
            permission.permissionId,
            tx,
          );
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          // Handle the error if necessary
        }
      }

      return userRoleDetails;
    });
  }

  async findOneUserRole(id: string) {
    const userRole = await this.drizzleDev.query.userRoles.findFirst({
      where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
    });

    if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

    return userRole;
  }

  async updateUserRole(id: string, updateUserRolesDto: UpdateUserRolesDto) {
    const userRole = await this.drizzleDev.query.userRoles.findFirst({
      where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
    });

    if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

    const [updatedUserRole] = await this.drizzleDev
      .update(userRoles)
      .set({
        ...updateUserRolesDto,
      })
      .where(eq(userRoles.id, id))
      .returning();

    return updatedUserRole;
  }

  async toggleUserRoleActive(id: string) {
    return this.drizzleDev.transaction(async (tx) => {
      const userRole = await tx.query.userRoles.findFirst({
        where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
      });

      if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

      const toggle = userRole.active;

      const [updatedUserRole] = await tx
        .update(userRoles)
        .set({
          active: !userRole.active,
        })
        .where(eq(userRoles.id, id))
        .returning();

      const permissionsFromUserPermissions = await tx.query.userPermissions.findMany({
        where: eq(userPermissions.userRoleId, id),
      });

      for (const permission of permissionsFromUserPermissions) {
        try {
          await (toggle
            ? this.removePermissionFromUserRole(id, permission.permissionId)
            : this.assignPermissionToUserRole(id, permission.permissionId));
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {}
      }

      return updatedUserRole;
    });
  }

  async softDeleteUserRole(id: string) {
    return this.drizzleDev.transaction(async (tx) => {
      const userRole = await tx.query.userRoles.findFirst({
        where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
      });

      if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

      const [softDeletedUserRole] = await tx
        .update(userRoles)
        .set({
          status: UserRolesStatus.ARCHIVED,
          active: UserRolesActiveStatus.INACTIVE,
        })
        .where(eq(userRoles.id, id))
        .returning();

      // removing permissions from the userPermissions table
      const permissionsFromUserPermissions = await tx.query.userPermissions.findMany({
        where: and(
          eq(userPermissions.userRoleId, id),
          eq(userPermissions.status, UserPermissionStatus.ACTIVE),
        ),
      });

      for (const permission of permissionsFromUserPermissions) {
        try {
          await this.removePermissionFromUserRole(id, permission.permissionId);
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          // Handle the error if necessary
        }
      }

      return softDeletedUserRole;
    });
  }

  async assignPermissionToUserRole(
    userRoleId: string,
    permissionId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      const userPermission = await tx.query.userPermissions.findFirst({
        where: and(
          eq(userPermissions.userRoleId, userRoleId),
          eq(userPermissions.permissionId, permissionId),
        ),
      });

      if (userPermission) {
        if (userPermission.status === UserPermissionStatus.ACTIVE) {
          throw itemAlreadyExists(EntityName.USER_PERMISSION);
        } else {
          const [updatedUserPermission] = await tx
            .update(userPermissions)
            .set({
              status: UserPermissionStatus.ACTIVE,
            })
            .where(
              and(
                eq(userPermissions.userRoleId, userRoleId),
                eq(userPermissions.permissionId, permissionId),
              ),
            )
            .returning();

          return updatedUserPermission;
        }
      }

      const [newUserPermission] = await tx
        .insert(userPermissions)
        .values({
          userRoleId,
          permissionId,
        })
        .returning();

      return newUserPermission;
    });
  }

  async updateRolePermissions(
    userRoleId: string,
    permissionIds: string[],
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      // Get current permissions for this role
      const currentPermissions = await tx.query.userPermissions.findMany({
        where: and(
          eq(userPermissions.userRoleId, userRoleId),
          eq(userPermissions.status, UserPermissionStatus.ACTIVE),
        ),
      });

      const currentPermissionMap = new Map(
        currentPermissions.map((perm) => [perm.permissionId, perm]),
      );
      const updatedPermissionIds = new Set(permissionIds);

      // Deactivate permissions no longer assigned
      for (const currentPerm of currentPermissions) {
        if (!updatedPermissionIds.has(currentPerm.permissionId)) {
          await this.removePermissionFromUserRole(userRoleId, currentPerm.permissionId, tx);
        }
      }

      // Add new permissions
      for (const permissionId of permissionIds) {
        if (!currentPermissionMap.has(permissionId)) {
          await this.assignPermissionToUserRole(userRoleId, permissionId, tx);
        }
      }
    });
  }

  async removePermissionFromUserRole(
    userRoleId: string,
    permissionId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      const userPermission = await tx.query.userPermissions.findFirst({
        where: and(
          eq(userPermissions.userRoleId, userRoleId),
          eq(userPermissions.permissionId, permissionId),
          eq(userPermissions.status, UserPermissionStatus.ACTIVE),
        ),
      });

      if (!userPermission) throw itemNotFound(EntityName.USER_PERMISSION);

      const [res] = await tx
        .update(userPermissions)
        .set({
          status: UserPermissionStatus.INACTIVE,
        })
        .where(
          and(
            eq(userPermissions.userRoleId, userRoleId),
            eq(userPermissions.permissionId, permissionId),
          ),
        )
        .returning();

      return res;
    });
  }

  async findPermissionsByUserRoleId(userRoleId: string) {
    const userPermission = await this.drizzleDev.query.userRoles.findFirst({
      columns: {},
      where: and(eq(userRoles.id, userRoleId), eq(userRoles.status, UserRolesStatus.ACTIVE)),
      with: {
        userPermissions: {
          columns: {
            permissionId: true,
          },
          where: and(
            eq(userPermissions.userRoleId, userRoleId),
            eq(userPermissions.status, UserPermissionStatus.ACTIVE),
          ),
          with: {
            permission: {
              columns: {
                name: true,
                key: true,
              },
              with: {
                module: {
                  columns: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return userPermission;
  }

  async switchUserRole(firebaseUid: string, userId: string, userRoleId: string) {
    return this.drizzleDev.transaction(async (tx) => {
      // 1. Verify user role exists and is active
      const userRole = await tx.query.userRoles.findFirst({
        where: and(
          eq(userRoles.id, userRoleId),
          eq(userRoles.userId, userId),
          eq(userRoles.status, UserRolesStatus.ACTIVE),
          eq(userRoles.active, UserRolesActiveStatus.ACTIVE),
        ),
        with: {
          role: {
            columns: {
              key: true,
            },
          },
          workspaces: {
            columns: {
              type: true,
            },
          },
        },
      });

      if (!userRole) {
        throw itemNotFound(EntityName.USER_ROLES);
      }

      // 2. Get user's permissions for this role
      const permissions = await this.findPermissionsByUserRoleId(userRoleId);
      const structuredPermissions = permissions?.userPermissions.map(
        (userPerm) => userPerm.permission.key,
      );

      // 4. Prepare new claims
      const workspaceDetails = {
        [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: userRole.workspaceId,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: userRole.role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: structuredPermissions,
      };

      // 5. Update Firebase claims
      const userRecord = await getAuth().getUser(firebaseUid);
      const currentClaims: UserData = (userRecord.customClaims as UserData) || {};

      const updatedClaims = {
        ...currentClaims,
        ...workspaceDetails,
      };

      await getAuth().setCustomUserClaims(firebaseUid, updatedClaims);

      return workspaceDetails;
    });
  }

  async updateUserRoles<T extends GlobalRoleDto | WorkspaceRoleDto>(
    userId: string,
    roles: T[],
    options: {
      isGlobal: boolean;
      transaction?: PostgresJsDatabase<typeof schema>;
    },
  ) {
    const { isGlobal, transaction } = options;
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      // Get current roles based on whether we're updating global or workspace roles
      const currentRoles = await tx.query.userRoles.findMany({
        where: and(
          eq(userRoles.userId, userId),
          isGlobal ? isNull(userRoles.workspaceId) : isNotNull(userRoles.workspaceId),
          eq(userRoles.status, UserRolesStatus.ACTIVE),
        ),
      });

      if (isGlobal) {
        // Process global roles
        const currentRoleMap = new Map(currentRoles.map((role) => [role.roleId, role]));
        const updatedRoleIds = new Set(roles.map((role) => role.roleId));

        // Deactivate roles that are no longer assigned
        for (const currentRole of currentRoles) {
          if (!updatedRoleIds.has(currentRole.roleId)) {
            await tx
              .update(userRoles)
              .set({ status: UserRolesStatus.INACTIVE })
              .where(eq(userRoles.id, currentRole.id));

            await this.updateRolePermissions(currentRole.id, [], tx);
          }
        }

        // Process each role in the update DTO
        for (const role of roles as GlobalRoleDto[]) {
          if (currentRoleMap.has(role.roleId)) {
            // Role exists - update permissions
            const existingRole = currentRoleMap.get(role.roleId);
            await this.updateRolePermissions(existingRole!.id, role.permissionIds, tx);
          } else {
            // Create new role
            await this.createUserRole(
              {
                userId,
                roleId: role.roleId,
                permissionIds: role.permissionIds,
              },
              tx,
            );
          }
        }
      } else {
        // Process workspace roles
        const getWorkspaceRoleKey = (roleId: string, workspaceId: string) =>
          `${roleId}:${workspaceId}`;

        const currentWorkspaceRoleMap = new Map(
          currentRoles.map((role) => [getWorkspaceRoleKey(role.roleId, role.workspaceId!), role]),
        );

        const updatedWorkspaceRoleKeys = new Set(
          (roles as WorkspaceRoleDto[]).map((role) =>
            getWorkspaceRoleKey(role.roleId, role.workspaceId),
          ),
        );

        // Deactivate roles that are no longer assigned
        for (const currentRole of currentRoles) {
          const key = getWorkspaceRoleKey(currentRole.roleId, currentRole.workspaceId!);
          if (!updatedWorkspaceRoleKeys.has(key)) {
            await tx
              .update(userRoles)
              .set({ status: UserRolesStatus.INACTIVE })
              .where(eq(userRoles.id, currentRole.id));

            await this.updateRolePermissions(currentRole.id, [], tx);
          }
        }

        // Process each workspace role in the update DTO
        for (const role of roles as WorkspaceRoleDto[]) {
          const key = getWorkspaceRoleKey(role.roleId, role.workspaceId);

          if (currentWorkspaceRoleMap.has(key)) {
            // Role exists - update permissions
            const existingRole = currentWorkspaceRoleMap.get(key);
            await this.updateRolePermissions(existingRole!.id, role.permissionIds, tx);
          } else {
            // Create new workspace role
            await this.createUserRole(
              {
                userId,
                roleId: role.roleId,
                workspaceId: role.workspaceId,
                permissionIds: role.permissionIds,
              },
              tx,
            );
          }
        }
      }
    });
  }
}
