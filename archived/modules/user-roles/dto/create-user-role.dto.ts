import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class CreateUserRolesDto {
  @ApiProperty({
    name: 'userId',
    type: 'string',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    name: 'roleId',
    type: 'string',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  roleId: string;

  @ApiProperty({
    name: 'workspaceId',
    type: 'string',
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  workspaceId?: string | null;

  // @ApiProperty({
  //   name: 'active',
  //   type: 'boolean',
  //   required: true,
  //   example: true,
  // })
  // @IsBoolean()
  // @IsNotEmpty()
  // active: boolean;
}

export class CreateUserRolesDtoWith extends CreateUserRolesDto {
  @ApiProperty({
    description: 'permission ids for the role',
    required: false,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  @IsOptional()
  permissionIds?: string[];
}
