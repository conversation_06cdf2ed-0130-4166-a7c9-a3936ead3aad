import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateUserRolesDto } from './create-user-role.dto';
import { IsBoolean, IsNotEmpty } from 'class-validator';

export class UpdateUserRolesDto extends PartialType(CreateUserRolesDto) {
  @ApiProperty({
    name: 'archived',
    type: 'boolean',
    required: true,
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  archived: boolean;
}
