import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { Inject, Injectable } from '@nestjs/common';

import { permissions, rolePermissions } from '@/db/schema';
import * as schema from '@/db/schema';

import { EntityName } from '@/constants/entities';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

@Injectable()
export class PermissionsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async findAllPermissions() {
    const permissionList = await this.drizzleDev.query.permissions.findMany({
      where: eq(permissions.status, 1),
      with: {
        module: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    return permissionList;
  }

  async findPermissionById(id: string) {
    const permission = await this.drizzleDev.query.permissions.findFirst({
      where: and(eq(permissions.id, id), eq(permissions.status, 1)),
      with: {
        module: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!permission) throw itemNotFound(EntityName.PERMISSION);

    return permission;
  }

  // Manage role-permission association
  async assignPermission(roleId: string, permissionId: string) {
    const rolePermission = await this.drizzleDev.query.rolePermissions.findFirst({
      where: and(
        eq(rolePermissions.roleId, roleId),
        eq(rolePermissions.permissionId, permissionId),
      ),
    });

    if (rolePermission) {
      if (rolePermission.status === 1) {
        throw itemAlreadyExists(EntityName.ROLE_PERMISSION);
      } else {
        const [updatedRolePermission] = await this.drizzleDev
          .update(rolePermissions)
          .set({
            status: 1,
          })
          .where(
            and(eq(rolePermissions.roleId, roleId), eq(rolePermissions.permissionId, permissionId)),
          )
          .returning();

        return updatedRolePermission;
      }
    }

    const [newRolePermission] = await this.drizzleDev
      .insert(rolePermissions)
      .values({
        roleId,
        permissionId,
      })
      .returning();

    return newRolePermission;
  }

  // Remove a permission from a role (soft delete)
  async removePermission(roleId: string, permissionId: string) {
    const rolePermission = await this.drizzleDev.query.rolePermissions.findFirst({
      where: and(
        eq(rolePermissions.roleId, roleId),
        eq(rolePermissions.permissionId, permissionId),
        eq(rolePermissions.status, 1),
      ),
    });

    if (!rolePermission) throw itemNotFound(EntityName.ROLE_PERMISSION);

    const [res] = await this.drizzleDev
      .update(rolePermissions)
      .set({
        status: 0,
      })
      .where(
        and(eq(rolePermissions.roleId, roleId), eq(rolePermissions.permissionId, permissionId)),
      )
      .returning();

    return res;
  }

  // async assignPermissionsToUserRole(
  //   tx: PostgresJsDatabase<typeof schema>,
  //   userId: string,
  //   roleId: string,
  //   organisationId: string | null,
  //   additionalPermissionIds: string[] | null,
  // ) {
  //   // Find if there's an existing user role (global or organisation)
  //   const userRole = await tx.query.userRoles.findFirst({
  //     where: and(
  //       eq(userRoles.userId, userId),
  //       eq(userRoles.roleId, roleId),
  //       organisationId
  //         ? eq(userRoles.organisationId, organisationId)
  //         : sql`${userRoles.organisationId} IS NULL`,
  //       eq(userRoles.status, 1),
  //     ),
  //   });

  //   if (!userRole) {
  //     throw new Error('User role not found');
  //   }

  //   // Delete existing permissions for the user role
  //   await tx.delete(userPermissions).where(eq(userPermissions.userRoleId, userRole.id));

  //   // Get all permissions for the new role
  //   const rolePermissions = await tx.query.rolesPermissions.findMany({
  //     where: and(eq(rolesPermissions.roleId, roleId), eq(rolesPermissions.status, 1)),
  //   });

  //   // Combine role permissions and additional permissions
  //   const allPermissionIds = [
  //     ...rolePermissions.map((rp) => rp.permissionsId),
  //     ...(additionalPermissionIds || []),
  //   ];

  //   // Format user permissions data
  //   const userPermissionsData = allPermissionIds.map((permissionId) => ({
  //     userRoleId: userRole.id,
  //     permissionsId: permissionId,
  //     status: 1,
  //   }));

  //   // Insert user permissions
  //   await tx.insert(userPermissions).values(userPermissionsData);
  // }
}
