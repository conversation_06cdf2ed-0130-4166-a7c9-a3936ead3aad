import { Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { PermissionsService } from './permissions.service';

@Controller('permissions')
@ApiBearerAuth()
@ApiTags('permissions')
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Get()
  findAllPermissions() {
    return this.permissionsService.findAllPermissions();
  }

  @Get(':id')
  findPermissionById(@Param('id') id: string) {
    return this.permissionsService.findPermissionById(id);
  }

  @ApiOperation({ summary: 'Assign a permission to a role' })
  @Post('assign-permission/:roleId/:permissionsId')
  assignPermission(@Param('roleId') roleId: string, @Param('permissionsId') permissionsId: string) {
    return this.permissionsService.assignPermission(roleId, permissionsId);
  }

  @ApiOperation({ summary: 'Remove a permission from a role' })
  @Delete('remove-permission/:roleId/:permissionsId')
  removePermission(@Param('roleId') roleId: string, @Param('permissionsId') permissionsId: string) {
    return this.permissionsService.removePermission(roleId, permissionsId);
  }
}
