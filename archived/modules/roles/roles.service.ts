import { Inject, Injectable } from '@nestjs/common';
import { and, eq, SQL } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

import * as schema from '@/db/schema';
import { roles, rolePermissions } from '@/db/schema';

import { EntityName } from '@/constants/entities';
import { RoleStatus, RoleType } from '@/constants/roles';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
// import { PermissionsService } from '../permissions/permissions.service';

@Injectable()
export class RolesService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createRole(createRoleDto: CreateRoleDto) {
    const role = await this.drizzleDev.query.roles.findFirst({
      where: eq(roles.key, createRoleDto.key),
    });

    if (role) throw itemAlreadyExists(EntityName.ROLE);

    const [newRole] = await this.drizzleDev
      .insert(roles)
      .values({
        ...createRoleDto,
      })
      .returning();

    return newRole;
  }

  findAllRoles(roleType?: RoleType) {
    const where: SQL[] = [eq(roles.status, RoleStatus.ACTIVE)];

    if (roleType) {
      where.push(eq(roles.type, roleType));
    }

    return this.drizzleDev.query.roles.findMany({
      where: and(...where),
      with: {
        rolesPermissions: {
          with: {
            permission: {
              columns: {
                id: true,
                name: true,
                key: true,
              },
              with: {
                module: {
                  columns: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  async findRoleById(id: string) {
    return this.findRoleByCondition(eq(roles.id, id));
  }

  async findRoleByKey(key: string) {
    return this.findRoleByCondition(eq(roles.key, key));
  }

  private async findRoleByCondition(condition: SQL) {
    const role = await this.drizzleDev.query.roles.findFirst({
      where: and(condition, eq(roles.status, 1)),
      // with: {
      //   rolesPermissions: {
      //     columns: {
      //       permissionsId: true,
      //     },
      //     where: eq(rolesPermissions.status, 1),
      //     with: {
      //       permission: {
      //         columns: {
      //           id: true,
      //           name: true,
      //           key: true,
      //         },
      //         with: {
      //           module: {
      //             columns: {
      //               id: true,
      //               name: true,
      //             },
      //           },
      //         },
      //       },
      //     },
      //   },
      // },
    });

    if (!role) throw itemNotFound(EntityName.ROLE);

    return role;
  }

  // async findOne(idOrKey: string) {
  //   const role = await this.drizzleDev.query.roles.findFirst({
  //     where: and(or(eq(roles.id, idOrKey), eq(roles.key, idOrKey)), eq(roles.status, 1)),
  //     with: {
  //       rolesPermissions: {
  //         columns: {
  //           permissionsId: true,
  //         },
  //         where: eq(rolesPermissions.status, 1),
  //         with: {
  //           permission: {
  //             columns: {
  //               name: true,
  //               key: true,
  //             },
  //           },
  //         },
  //       },
  //     },
  //   });

  //   if (!role) throw itemNotFound(EntityName.ROLE);

  //   return role;
  // }

  async updateRole(id: string, updateRoleDto: UpdateRoleDto) {
    const role = await this.drizzleDev.query.roles.findFirst({
      where: eq(roles.id, id),
    });

    if (!role) throw itemNotFound(EntityName.ROLE);

    if (updateRoleDto.key) {
      const alreadyExistingRole = await this.drizzleDev.query.roles.findFirst({
        where: eq(schema.roles.key, updateRoleDto.key),
      });

      if (alreadyExistingRole) throw itemAlreadyExists(EntityName.ROLE);
    }

    const [updatedRole] = await this.drizzleDev
      .update(roles)
      .set({
        ...updateRoleDto,
      })
      .where(eq(roles.id, id))
      .returning();

    return updatedRole;
  }

  async softDeleteRole(id: string) {
    const role = await this.drizzleDev.query.roles.findFirst({
      where: and(eq(roles.id, id), eq(roles.status, 1)),
    });

    if (!role) throw itemNotFound(EntityName.ROLE);

    const [deletedRole] = await this.drizzleDev
      .update(roles)
      .set({
        status: 0,
      })
      .where(eq(roles.id, id))
      .returning();

    return deletedRole;
  }

  // Fetch all permissions associated with a role
  async findPermissionsByRoleId(roleId: string) {
    const rolePermission = await this.drizzleDev.query.roles.findFirst({
      columns: {},
      where: and(eq(roles.id, roleId), eq(roles.status, 1)),
      with: {
        rolesPermissions: {
          columns: {
            permissionId: true,
          },
          where: and(eq(rolePermissions.status, 1)),
          with: {
            permission: {
              columns: {
                name: true,
                key: true,
              },
              with: {
                module: {
                  columns: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return rolePermission;
  }

  // async assignRoleToUser(
  //   tx: PostgresJsDatabase<typeof schema>,
  //   userId: string,
  //   roleId: string,
  //   organisationId: string | null,
  //   additionalPermissionIds: string[] | null,
  // ) {
  //   // Check if user already has a role (global or in this organization)
  //   const existingUserRole = await this.findUserRole(tx, userId, organisationId || null);

  //   let newUpdatedUserRole: UserRole;
  //   if (existingUserRole) {
  //     // Update existing role
  //     const [updatedUserRole] = await tx
  //       .update(userRoles)
  //       .set({ roleId, organisationId, status: 1, updatedAt: new Date() })
  //       .where(eq(userRoles.id, existingUserRole.id))
  //       .returning();
  //     newUpdatedUserRole = updatedUserRole;
  //   } else {
  //     // Create new role assignment
  //     const [newUserRole] = await tx
  //       .insert(userRoles)
  //       .values({
  //         userId,
  //         roleId,
  //         organisationId,
  //         status: 1,
  //       })
  //       .returning();
  //     newUpdatedUserRole = newUserRole;
  //   }

  //   await this.permissionsService.assignPermissionsToUserRole(
  //     tx,
  //     userId,
  //     newUpdatedUserRole.roleId,
  //     organisationId || null,
  //     additionalPermissionIds || null,
  //   );
  // }

  // async findUserRole(
  //   tx: PostgresJsDatabase<typeof schema>,
  //   userId: string,
  //   organisationId?: string | null,
  // ) {
  //   return tx.query.userRoles.findFirst({
  //     where: and(
  //       eq(userRoles.userId, userId),
  //       organisationId
  //         ? eq(userRoles.organisationId, organisationId)
  //         : sql`${userRoles.organisationId} IS NULL`,
  //       eq(userRoles.status, 1),
  //     ),
  //   });
  // }
}
