import { Controller, Get, Post, Body, Patch, Param, Delete, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { RoleType } from '@/constants/roles';

import { RolesService } from './roles.service';

import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

@Controller('roles')
@ApiBearerAuth()
@ApiTags('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Create a new role
   * @param createRoleDto - The data for creating a new role
   * @returns The created role
   */
  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  createRole(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.createRole(createRoleDto);
  }

  /**
   * Retrieve all roles
   * @returns An array of all roles
   */
  @Get()
  @ApiOperation({ summary: 'Get all roles' })
  @ApiQuery({
    name: 'roleType',
    required: false,
    type: String,
    enum: RoleType,
  })
  findAllRoles(@Query('roleType') roleType: RoleType) {
    return this.rolesService.findAllRoles(roleType);
  }

  /**
   * Find a role by its ID
   * @param id - The ID of the role to find
   * @returns The role with the specified ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a role by ID' })
  findRoleById(@Param('id') id: string) {
    return this.rolesService.findRoleById(id);
  }

  /**
   * Update a role's information
   * @param id - The ID of the role to update
   * @param updateRoleDto - The data to update the role with
   * @returns The updated role
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a role' })
  updateRole(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.rolesService.updateRole(id, updateRoleDto);
  }

  /**
   * Soft delete a role
   * @param id - The ID of the role to delete
   * @returns The deleted role
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete a role' })
  removeRole(@Param('id') id: string) {
    return this.rolesService.softDeleteRole(id);
  }

  /**
   * Get all permissions associated with a role
   * @param roleId - The ID of the role
   * @returns An array of permissions associated with the role
   */
  @ApiOperation({ summary: 'Get all permissions associated with a role' })
  @Get(':id/permissions')
  findPermissionsByRoleId(@Param('id') roleId: string) {
    return this.rolesService.findPermissionsByRoleId(roleId);
  }
}
