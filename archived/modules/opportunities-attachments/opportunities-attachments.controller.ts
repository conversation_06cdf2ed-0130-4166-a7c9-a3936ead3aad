import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';

import { OpportunitiesAttachmentsService } from './opportunities_attachments.service';

import { CreateOpportunitiesAttachmentDto } from './dto/create-opportunities-attachment.dto';
import { UpdateOpportunitiesAttachmentDto } from './dto/update-opportunities-attachment.dto';

import { ActiveWorkspace } from '@/decorators/active-workspace.decorator';

@Controller('opportunities-attachments')
@ApiTags('opportunities-attachments')
export class OpportunitiesAttachmentsController {
  constructor(private readonly opportunitiesAttachmentsService: OpportunitiesAttachmentsService) {}

  @Post(':opportunityId')
  @ActiveWorkspace()
  create(
    @Param('opportunityId') opportunityId: string,
    @Body() createOpportunitiesAttachmentDto: CreateOpportunitiesAttachmentDto,
  ) {
    return this.opportunitiesAttachmentsService.create(
      opportunityId,
      createOpportunitiesAttachmentDto,
    );
  }

  @Get()
  findAll() {
    return this.opportunitiesAttachmentsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.opportunitiesAttachmentsService.findOne(id);
  }

  @Patch(':id')
  @ActiveWorkspace()
  update(
    @Param('id') id: string,
    @Body() updateOpportunitiesAttachmentDto: UpdateOpportunitiesAttachmentDto,
  ) {
    return this.opportunitiesAttachmentsService.update(id, updateOpportunitiesAttachmentDto);
  }

  @Delete(':id')
  @ActiveWorkspace()
  remove(@Param('id') id: string) {
    return this.opportunitiesAttachmentsService.softDelete(id);
  }
}
