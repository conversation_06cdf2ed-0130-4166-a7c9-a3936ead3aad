import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { OpportunitiesAttachments } from '@/constants/opportunities-attachments';

export class CreateOpportunitiesAttachmentDto {
  @ApiProperty({
    name: 'filePath',
    type: 'string',
    required: true,
    example: 'uploads/files/document.pdf',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  filePath: string;

  @ApiProperty({
    name: 'fileType',
    type: 'string',
    required: true,
    example: 'pdf',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  fileType: string;

  @ApiProperty({
    name: 'fileSize',
    type: 'number',
    required: true,
    example: 1024.5,
  })
  @IsNumber()
  @IsNotEmpty()
  @Max(OpportunitiesAttachments.MAX_FILE_SIZE, { message: 'File size must not exceed 5 MB.' })
  fileSize: number;
}

// export class CreateOpportunitiesAttachmentDto extends OpportunitiesFileDetail {
//   @ApiProperty({
//     name: 'opportunityId',
//     type: 'string',
//     required: true,
//     example: '550e8400-e29b-41d4-a716-************',
//   })
//   @IsUUID()
//   @IsNotEmpty()
//   opportunityId: string;
// }
