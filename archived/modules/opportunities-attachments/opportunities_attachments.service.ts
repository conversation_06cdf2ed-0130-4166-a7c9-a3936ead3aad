import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { CreateOpportunitiesAttachmentDto } from './dto/create-opportunities-attachment.dto';
import { UpdateOpportunitiesAttachmentDto } from './dto/update-opportunities-attachment.dto';

import * as schema from '@/db/schema';
import { opportunitiesAttachments } from '@/db/schema';

import { EntityName } from '@/constants/entities';
import {
  OpportunitiesAttachments,
  OpportunitiesAttachmentsStatus,
} from '@/constants/opportunities-attachments';

import {
  itemAlreadyExists,
  itemLimitReached,
  itemNotFound,
  itemSizeTooLarge,
} from '@/exceptions/common';

@Injectable()
export class OpportunitiesAttachmentsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  findAll() {
    return this.drizzleDev.query.opportunitiesAttachments.findMany({
      where: eq(opportunitiesAttachments.status, OpportunitiesAttachmentsStatus.ACTIVE),
    });
  }

  findOne(id: string) {
    const attachment = this.drizzleDev.query.opportunitiesAttachments.findFirst({
      where: eq(opportunitiesAttachments.id, id),
    });

    if (!attachment) throw itemNotFound(EntityName.OPPORTUNITY_ATTACHMENT);

    return attachment;
  }

  async create(
    opportunityId: string,
    createOpportunitiesAttachmentDto: CreateOpportunitiesAttachmentDto,
  ) {
    if (createOpportunitiesAttachmentDto?.fileSize > OpportunitiesAttachments.MAX_FILE_SIZE) {
      throw itemSizeTooLarge(EntityName.OPPORTUNITY_ATTACHMENT);
    }

    const OpportunityIdAttachments = await this.drizzleDev.query.opportunitiesAttachments.findMany({
      where: and(
        eq(opportunitiesAttachments.opportunityId, opportunityId),
        eq(opportunitiesAttachments.status, OpportunitiesAttachmentsStatus.ACTIVE),
      ),
    });

    if (OpportunityIdAttachments.length === OpportunitiesAttachments.MAX_NO_OF_FILES) {
      throw itemLimitReached(EntityName.OPPORTUNITY_ATTACHMENT);
    }

    const attachment = await this.drizzleDev.query.opportunitiesAttachments.findFirst({
      where: and(
        eq(opportunitiesAttachments.filePath, createOpportunitiesAttachmentDto.filePath),
        eq(opportunitiesAttachments.opportunityId, opportunityId),
      ),
    });

    if (attachment) {
      if (attachment.status === OpportunitiesAttachmentsStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.OPPORTUNITY_ATTACHMENT);
      } else {
        const [updatedAttachment] = await this.drizzleDev
          .update(opportunitiesAttachments)
          .set({
            ...createOpportunitiesAttachmentDto,
            status: OpportunitiesAttachmentsStatus.ACTIVE,
          })
          .where(eq(opportunitiesAttachments.id, attachment.id))
          .returning();

        return updatedAttachment;
      }
    }

    const [newAttachment] = await this.drizzleDev
      .insert(opportunitiesAttachments)
      .values({
        opportunityId,
        ...createOpportunitiesAttachmentDto,
      })
      .returning();

    return newAttachment;
  }

  async createOrUpdateMultipleAttachment(
    opportunityId: string,
    createOpportunitiesAttachmentDtos: CreateOpportunitiesAttachmentDto[],
    transaction?: PostgresJsDatabase<typeof schema>,
    isNewOpportunity: boolean = false,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    let allAttachments = createOpportunitiesAttachmentDtos;

    if (!isNewOpportunity) {
      // Fetch existing attachments for the opportunity
      const existingAttachments = await dbOrTransaction.query.opportunitiesAttachments.findMany({
        where: and(
          eq(opportunitiesAttachments.opportunityId, opportunityId),
          eq(opportunitiesAttachments.status, OpportunitiesAttachmentsStatus.ACTIVE),
        ),
      });

      // Check if adding new attachments would exceed the maximum allowed files
      const totalAttachmentsCount =
        existingAttachments.length + createOpportunitiesAttachmentDtos.length;
      if (totalAttachmentsCount > OpportunitiesAttachments.MAX_NO_OF_FILES) {
        throw itemLimitReached(EntityName.OPPORTUNITY_ATTACHMENT);
      }

      // Filter out duplicate attachments based on filePath
      allAttachments = createOpportunitiesAttachmentDtos.filter((dto) => {
        return !existingAttachments.some((attachment) => attachment.filePath === dto.filePath);
      });
    }

    // Insert new attachments in a single DB operation
    const newAttachmentsData = allAttachments.map((dto) => ({
      opportunityId,
      filePath: dto.filePath,
      fileType: dto.fileType,
      fileSize: dto.fileSize,
    }));

    // Only insert if there are new attachments to add
    if (newAttachmentsData.length > 0)
      await dbOrTransaction.insert(opportunitiesAttachments).values(newAttachmentsData);
  }

  async update(id: string, updateOpportunitiesAttachmentDto: UpdateOpportunitiesAttachmentDto) {
    const attachment = await this.drizzleDev.query.opportunitiesAttachments.findFirst({
      where: and(
        eq(opportunitiesAttachments.id, id),
        eq(opportunitiesAttachments.status, OpportunitiesAttachmentsStatus.ACTIVE),
      ),
    });

    if (!attachment) throw itemNotFound(EntityName.OPPORTUNITY_ATTACHMENT);

    const [res] = await this.drizzleDev
      .update(opportunitiesAttachments)
      .set({
        ...updateOpportunitiesAttachmentDto,
      })
      .where(eq(opportunitiesAttachments.id, id))
      .returning();

    return res;
  }

  async softDelete(id: string) {
    const attachment = await this.drizzleDev.query.opportunitiesAttachments.findFirst({
      where: and(
        eq(opportunitiesAttachments.id, id),
        eq(opportunitiesAttachments.status, OpportunitiesAttachmentsStatus.ACTIVE),
      ),
    });

    if (!attachment) throw itemNotFound(EntityName.OPPORTUNITY_ATTACHMENT);

    const [res] = await this.drizzleDev
      .update(opportunitiesAttachments)
      .set({
        status: OpportunitiesAttachmentsStatus.INACTIVE,
      })
      .where(eq(opportunitiesAttachments.id, id))
      .returning();

    return res;
  }
}
