import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { SQL, and, asc, eq, gt, gte, inArray, ne, sql } from 'drizzle-orm';

import { UpdateAccountTypeByAdminDto, UpdateAccountTypeDto } from './dto/update-workspace.dto';
import { FindAllWorkspacesDto } from './dto/find-all-workspace.dto';

import * as schema from '@/db/schema';
import {
  organisations,
  workspaces,
  surgeons,
  professionals,
  Organisations,
  Surgeons,
  Professionals,
  Workspaces,
  organisationCertifications,
  professionalAssociations,
  professionalAwards,
  professionalCertificates,
} from '@/db/schema';

import { itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import {
  AccountTypeInfo,
  WORKSPACE_TYPE_RELATIONS,
  WorkspaceType,
  WorkspacesStatus,
} from '@/constants/workspaces';

import { AccountTypeFactory } from './account-types/account-type.factory';

import { CreateSubscriptionDto } from '@/modules/subscriptions/dto/create-subscription.dto';
import { UsersService } from '@/modules/users/users.service';

import { getFullName } from '@/utils/users';

@Injectable()
export class WorkspacesService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly accountTypeFactory: AccountTypeFactory,
    private readonly usersService: UsersService,
  ) {}

  async getWorkspacesByAccountType(accountType: WorkspaceType) {
    return this.drizzleDev.query.workspaces.findMany({
      where: and(
        gte(workspaces.status, WorkspacesStatus.PENDING),
        eq(workspaces.type, accountType),
      ),
      with: WORKSPACE_TYPE_RELATIONS[accountType],
    });
  }

  getWorkspaces(query: FindAllWorkspacesDto) {
    const { name, workspaceType } = query;

    // Get all active workspace types or only the requested ones
    const conditions: SQL[] = [
      name && sql`LOWER(${workspaces.label}) LIKE ${`%${name.toLowerCase().trim()}%`}`,
      workspaceType?.length ? inArray(workspaces.type, workspaceType) : undefined,
    ].filter(Boolean) as SQL[];

    return this.drizzleDev.query.workspaces.findMany({
      where: and(gte(workspaces.status, WorkspacesStatus.PENDING), ...conditions),
      columns: {
        id: false,
      },
      extras: {
        workspaceId: sql`${workspaces.id}`.as('workspace_id'),
      },
      orderBy: asc(workspaces.createdAt),
    });
  }

  async getWorkspacesForPublic(query: FindAllWorkspacesDto) {
    const { name, workspaceType } = query;

    // Get all active workspace types or only the requested ones
    const typesToProcess = workspaceType?.length ? workspaceType : Object.values(WorkspaceType);

    const conditions: SQL[] = [
      eq(workspaces.status, WorkspacesStatus.ACTIVE),
      name && sql`LOWER(${workspaces.label}) LIKE ${`%${name.toLowerCase().trim()}%`}`,
      typesToProcess.length ? inArray(workspaces.type, typesToProcess) : undefined,
    ].filter(Boolean) as SQL[];

    // Get all matching workspaces
    const allWorkspaces = await this.drizzleDev.query.workspaces.findMany({
      where: and(...conditions),
      columns: {
        id: true,
        type: true,
        label: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      with: {
        createdByUser: {
          columns: {
            firstName: true,
            middleName: true,
            lastName: true,
            username: true,
            email: true,
          },
        },
        subtype: {
          columns: {
            name: true,
          },
        },
      },
      orderBy: (workspace, { desc }) => [desc(workspace.createdAt)],
    });

    // Group workspaces by type
    const groupedWorkspaces = typesToProcess
      .map((type) => ({
        type,
        datas: allWorkspaces.filter((workspace) => workspace.type === type),
      }))
      .filter((group) => group.datas.length > 0); // Only include groups that have matching workspaces

    return groupedWorkspaces;
  }

  findOneWorkspaceByWorkspaceId(workspaceId: string, getBasicDetailsOnly: boolean = false) {
    return this.findUserByCondition(eq(workspaces.id, workspaceId), getBasicDetailsOnly);
  }

  findOneWorkspaceByWorkspacename(workspacename: string, getBasicDetailsOnly: boolean = false) {
    return this.findUserByCondition(
      eq(workspaces.workspacename, workspacename),
      getBasicDetailsOnly,
    );
  }

  private async findUserByCondition(condition: SQL, getBasicDetailsOnly: boolean = false) {
    const workspace = await this.drizzleDev.query.workspaces.findFirst({
      where: and(condition, gt(workspaces.status, WorkspacesStatus.ARCHIVED)),
      with: {
        subtype: {
          columns: {
            name: true,
          },
        },
        createdByUser: {
          columns: {
            email: true,
          },
          with: {
            country: {
              columns: {
                name: true,
              },
            },
          },
        },
      },
    });

    if (!workspace) throw itemNotFound(EntityName.WORKSPACE);

    if (getBasicDetailsOnly) return workspace;

    let accountTypeInfo: AccountTypeInfo;

    switch (workspace.type) {
      case WorkspaceType.ORGANISATION:
        accountTypeInfo = (await this.drizzleDev.query.organisations.findFirst({
          where: and(eq(organisations.workspaceId, workspace.id), eq(organisations.status, 1)),
          with: {
            organisationCertifications: true,
            country: {
              columns: {
                name: true,
              },
            },
          },
        })) as Organisations;
        break;
      case WorkspaceType.SURGEON:
        accountTypeInfo = (await this.drizzleDev.query.surgeons.findFirst({
          where: and(eq(surgeons.workspaceId, workspace.id), eq(surgeons.status, 1)),
          with: {
            professionalAssociations: true,
            professionalCertificates: true,
            professionalAwards: true,
          },
        })) as Surgeons;
        break;
      case WorkspaceType.PROFESSIONAL:
        accountTypeInfo = (await this.drizzleDev.query.professionals.findFirst({
          where: and(eq(professionals.workspaceId, workspace.id), eq(professionals.status, 1)),
        })) as Professionals;
        break;
      default:
        throw itemNotFound(EntityName.WORKSPACE);
    }

    if (!accountTypeInfo) throw itemNotFound(EntityName.WORKSPACE);

    return {
      ...workspace,
      ...accountTypeInfo,
    };
  }

  async isWorkspacenameExists(workspacename: string, workspaceId: string): Promise<boolean> {
    const exists = await this.drizzleDev.query.workspaces.findFirst({
      where: and(eq(workspaces.workspacename, workspacename), ne(workspaces.id, workspaceId)),
      columns: { workspacename: true },
    });

    return Boolean(exists);
  }

  async create(
    createAccountDto: CreateSubscriptionDto,
    userId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const { type, orgName, subtypeId, ...accountData } = createAccountDto;

    const dbOrTransaction = transaction || this.drizzleDev;

    const user = await this.usersService.findUserById(userId);
    if (!user) throw itemNotFound(EntityName.USER);

    return dbOrTransaction.transaction(async (txn) => {
      // Generate UUID first
      const workspaceId = crypto.randomUUID();

      const label = type !== WorkspaceType.ORGANISATION ? getFullName(user) : orgName!;

      // Generate workspacename using the UUID, removing all special characters from label
      const cleanedLabel = label
        .trim()
        .replace(/[^a-zA-Z0-9_-]/g, '')
        .toLowerCase();
      const workspacename = `${cleanedLabel}-${workspaceId}`;

      // Insert the new workspace
      const [newWorkspace] = await txn
        .insert(workspaces)
        .values({
          id: workspaceId,
          workspacename,
          type,
          label,
          subtypeId,
          createdBy: userId,
          status: WorkspacesStatus.PENDING,
        })
        .returning();

      // Use the AccountTypeFactory to get the right strategy
      const strategy = this.accountTypeFactory.getStrategy(type);

      // Call the strategy to create the account
      await strategy.createAccount(workspaceId, accountData, txn);

      return newWorkspace;
    });
  }

  async softDelete(id: string) {
    return this.drizzleDev.transaction(async (txn) => {
      const [res] = await txn
        .update(workspaces)
        .set({
          status: WorkspacesStatus.ARCHIVED,
        })
        .where(and(eq(workspaces.id, id), gte(workspaces.status, WorkspacesStatus.REJECTED)))
        .returning();

      if (!res) throw itemNotFound(EntityName.WORKSPACE);

      let accRes;
      switch (res.type) {
        case WorkspaceType.ORGANISATION:
          [accRes] = await txn
            .update(organisations)
            .set({
              status: 0,
            })
            .where(and(eq(organisations.workspaceId, id), eq(organisations.status, 1)))
            .returning();
          break;

        case WorkspaceType.SURGEON:
          [accRes] = await txn
            .update(surgeons)
            .set({
              status: 0,
            })
            .where(and(eq(surgeons.workspaceId, id), eq(surgeons.status, 1)))
            .returning();
          break;

        case WorkspaceType.PROFESSIONAL:
          [accRes] = await txn
            .update(professionals)
            .set({
              status: 0,
            })
            .where(and(eq(professionals.workspaceId, id), eq(professionals.status, 1)))
            .returning();
          break;

        // Add other cases as needed
        default:
          break;
      }

      if (!accRes) throw itemNotFound(EntityName.WORKSPACE);

      return res;
    });
  }

  async update(
    workspaceId: string,
    updateAccountTypeDto: UpdateAccountTypeDto | UpdateAccountTypeByAdminDto,
  ) {
    const {
      label,
      linkedinLink,
      subtypeId,
      tiktokLink,
      youtubeLink,
      twitterLink,
      instagramLink,
      facebookLink,
      profileImageUrl,
      profileImageUrlThumbnail,
      workspacename,
      status,
      ...accountData
    } = updateAccountTypeDto as UpdateAccountTypeByAdminDto;

    return this.drizzleDev.transaction(async (txn) => {
      let accountType: WorkspaceType;
      const workspaceFieldsToUpdate: Partial<Workspaces> = {};

      if ('label' in updateAccountTypeDto) workspaceFieldsToUpdate.label = label;
      if ('linkedinLink' in updateAccountTypeDto)
        workspaceFieldsToUpdate.linkedinLink = linkedinLink;
      if ('subtypeId' in updateAccountTypeDto) workspaceFieldsToUpdate.subtypeId = subtypeId;
      if ('tiktokLink' in updateAccountTypeDto) workspaceFieldsToUpdate.tiktokLink = tiktokLink;
      if ('youtubeLink' in updateAccountTypeDto) workspaceFieldsToUpdate.youtubeLink = youtubeLink;
      if ('twitterLink' in updateAccountTypeDto) workspaceFieldsToUpdate.twitterLink = twitterLink;
      if ('instagramLink' in updateAccountTypeDto)
        workspaceFieldsToUpdate.instagramLink = instagramLink;
      if ('facebookLink' in updateAccountTypeDto)
        workspaceFieldsToUpdate.facebookLink = facebookLink;
      if ('profileImageUrl' in updateAccountTypeDto)
        workspaceFieldsToUpdate.profileImageUrl = profileImageUrl;
      if ('profileImageUrlThumbnail' in updateAccountTypeDto)
        workspaceFieldsToUpdate.profileImageUrlThumbnail = profileImageUrlThumbnail;
      if ('workspacename' in updateAccountTypeDto)
        workspaceFieldsToUpdate.workspacename = workspacename;
      if ('status' in updateAccountTypeDto) workspaceFieldsToUpdate.status = status;

      // If any workspace fields are present in DTO
      if (Object.keys(workspaceFieldsToUpdate).length > 0) {
        const [response] = await txn
          .update(workspaces)
          .set(workspaceFieldsToUpdate)
          .where(
            and(eq(workspaces.id, workspaceId), gt(workspaces.status, WorkspacesStatus.ARCHIVED)),
          )
          .returning();

        if (!response) throw itemNotFound(EntityName.WORKSPACE);

        accountType = response.type;
      } else {
        const { type: accountTypeResponse } = await this.findOneWorkspaceByWorkspaceId(workspaceId);
        accountType = accountTypeResponse;
      }

      if (accountData && Object.keys(accountData ?? {}).length > 0) {
        let accRes;
        switch (accountType) {
          case WorkspaceType.ORGANISATION:
            [accRes] = await txn
              .update(organisations)
              .set(accountData)
              .where(and(eq(organisations.workspaceId, workspaceId), eq(organisations.status, 1)))
              .returning();

            const orgId = accRes.id;

            if (accountData.organisationCertifications) {
              const existingCerts = await txn.query.organisationCertifications.findMany({
                where: eq(organisationCertifications.organisationId, orgId),
              });

              const existingMap = new Map(existingCerts.map((cert) => [cert.id, cert]));

              // Process each certificate
              for (const cert of accountData.organisationCertifications) {
                if (cert.id && existingMap.has(cert.id)) {
                  // Update existing certificate
                  await txn
                    .update(organisationCertifications)
                    .set({
                      title: cert.title,
                      issuingBody: cert.issuingBody,
                      issuedDate: new Date(cert.issuedDate).toLocaleString(),
                      expiryDate: cert.expiryDate
                        ? new Date(cert.expiryDate).toLocaleString()
                        : null,
                      certificationType: cert.certificationType,
                    })
                    .where(eq(organisationCertifications.id, cert.id));

                  existingMap.delete(cert.id);
                } else {
                  // Create new certificate
                  await txn.insert(organisationCertifications).values({
                    organisationId: orgId,
                    title: cert.title,
                    issuingBody: cert.issuingBody,
                    issuedDate: new Date(cert.issuedDate).toLocaleString(),
                    expiryDate: cert.expiryDate ? new Date(cert.expiryDate).toLocaleString() : null,
                    certificationType: cert.certificationType,
                  });
                }
              }

              // Delete certificates that weren't in the update
              for (const [id] of existingMap) {
                await txn
                  .delete(organisationCertifications)
                  .where(eq(organisationCertifications.id, id));
              }

              // find any new or removed anything, and do approprate operation here
            }
            break;

          case WorkspaceType.SURGEON:
            [accRes] = await txn
              .update(surgeons)
              .set(accountData as any)
              .where(and(eq(surgeons.workspaceId, workspaceId), eq(surgeons.status, 1)))
              .returning();

            const surgeonId = accRes.id;

            if (accountData.professionalAssociations) {
              const existingAssociations = await txn.query.professionalAssociations.findMany({
                where: eq(professionalAssociations.surgeonId, surgeonId),
              });

              const existingMap = new Map(
                existingAssociations.map((associa) => [associa.id, associa]),
              );

              // Process each association
              for (const associa of accountData.professionalAssociations) {
                if (associa.id && existingMap.has(associa.id)) {
                  // Update existing association
                  await txn
                    .update(professionalAssociations)
                    .set({
                      ...associa,
                      joinedDate: new Date(associa.joinedDate).toLocaleString(),
                      expiryDate: associa.expiryDate
                        ? new Date(associa.expiryDate).toLocaleString()
                        : null,
                    })
                    .where(eq(professionalAssociations.id, associa.id));

                  existingMap.delete(associa.id);
                } else {
                  // Create new association
                  await txn.insert(professionalAssociations).values({
                    surgeonId,
                    ...associa,
                    joinedDate: new Date(associa.joinedDate).toLocaleString(),
                    expiryDate: associa.expiryDate
                      ? new Date(associa.expiryDate).toLocaleString()
                      : null,
                  });
                }
              }

              // Delete association that weren't in the update
              for (const [id] of existingMap) {
                await txn
                  .delete(professionalAssociations)
                  .where(eq(professionalAssociations.id, id));
              }
            }

            if (accountData.professionalCertificates) {
              const existingCertificates = await txn.query.professionalCertificates.findMany({
                where: eq(professionalCertificates.surgeonId, surgeonId),
              });

              const existingMap = new Map(existingCertificates.map((cert) => [cert.id, cert]));

              // Process each certificates
              for (const cert of accountData.professionalCertificates) {
                if (cert.id && existingMap.has(cert.id)) {
                  // Update existing certificate
                  await txn
                    .update(professionalCertificates)
                    .set({
                      ...cert,
                      issuedDate: new Date(cert.issuedDate).toLocaleString(),
                      expiryDate: cert.expiryDate
                        ? new Date(cert.expiryDate).toLocaleString()
                        : null,
                    })
                    .where(eq(professionalCertificates.id, cert.id));

                  existingMap.delete(cert.id);
                } else {
                  // Create new certificate
                  await txn.insert(professionalCertificates).values({
                    surgeonId,
                    ...cert,
                    issuedDate: new Date(cert.issuedDate).toLocaleString(),
                    expiryDate: cert.expiryDate ? new Date(cert.expiryDate).toLocaleString() : null,
                  });
                }
              }

              // Delete certificates that weren't in the update
              for (const [id] of existingMap) {
                await txn
                  .delete(professionalCertificates)
                  .where(eq(professionalCertificates.id, id));
              }
            }

            if (accountData.professionalAwards) {
              const existingAwards = await txn.query.professionalAwards.findMany({
                where: eq(professionalAwards.surgeonId, surgeonId),
              });

              const existingMap = new Map(existingAwards.map((awa) => [awa.id, awa]));

              // Process each award
              for (const awa of accountData.professionalAwards) {
                if (awa.id && existingMap.has(awa.id)) {
                  // Update existing award
                  await txn
                    .update(professionalAwards)
                    .set({
                      ...awa,
                      awardedDate: new Date(awa.awardedDate).toLocaleString(),
                    })
                    .where(eq(professionalAwards.id, awa.id));

                  existingMap.delete(awa.id);
                } else {
                  // Create new award
                  await txn.insert(professionalAwards).values({
                    surgeonId,
                    ...awa,
                    awardedDate: new Date(awa.awardedDate).toLocaleString(),
                  });
                }
              }

              // Delete awards that weren't in the update
              for (const [id] of existingMap) {
                await txn.delete(professionalAwards).where(eq(professionalAwards.id, id));
              }
            }
            break;

          case WorkspaceType.PROFESSIONAL:
            [accRes] = await txn
              .update(professionals)
              .set(accountData as any)
              .where(and(eq(professionals.workspaceId, workspaceId), eq(professionals.status, 1)))
              .returning();
            break;

          // Add other cases as needed
          default:
            break;
        }

        if (!accRes) throw itemNotFound(EntityName.WORKSPACE);
      }

      return this.findOneWorkspaceByWorkspaceId(workspaceId, true);
    });
  }
}
