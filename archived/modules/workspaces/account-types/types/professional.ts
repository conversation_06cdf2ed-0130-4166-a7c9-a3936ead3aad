import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { AccountTypeStrategy } from '../account-type.strategy';

import * as schema from '@/db/schema';
import { professionals } from '@/db/schema';

@Injectable()
export class ProfessionalStrategy implements AccountTypeStrategy {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async createAccount(
    workspaceId: string,
    accountData: any,
    txn?: PostgresJsDatabase<typeof schema>,
  ): Promise<any> {
    const dbOrTransaction = txn ? txn : this.drizzleDev;
    // Insert new professional into the database
    const [newProfessional] = await dbOrTransaction
      .insert(professionals)
      .values({
        ...accountData,
        workspaceId,
      })
      .returning(); // Use returning to get the inserted professional data

    return newProfessional;
  }
}
