import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { AccountTypeStrategy } from '../account-type.strategy';

import * as schema from '@/db/schema';
import { surgeons } from '@/db/schema';

@Injectable()
export class SurgeonStrategy implements AccountTypeStrategy {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async createAccount(
    workspaceId: string,
    accountData: any,
    txn?: PostgresJsDatabase<typeof schema>,
  ): Promise<any> {
    const dbOrTransaction = txn ? txn : this.drizzleDev;
    // Insert new surgeon into the database
    const [newSurgeon] = await dbOrTransaction
      .insert(surgeons)
      .values({
        ...accountData,
        workspaceId,
      })
      .returning(); // Use returning to get the inserted surgeon data

    return newSurgeon;
  }
}
