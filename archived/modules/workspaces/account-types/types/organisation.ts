import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import { AccountTypeStrategy } from '../account-type.strategy';

import * as schema from '@/db/schema';
import { organisations } from '@/db/schema';

import { itemAlreadyExists } from '@/exceptions/common';
import { forbidden } from '@/exceptions/system';

import { EntityName } from '@/constants/entities';

import { SubtypesService } from '@/modules/subtypes/subtypes.service';
import { Subtype } from '@/modules/subtypes/entities/subtype.entity';

@Injectable()
export class OrganisationStrategy implements AccountTypeStrategy {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly subtypesService: SubtypesService,
  ) {}

  async createAccount(
    workspaceId: string,
    accountData: any,
    txn?: PostgresJsDatabase<typeof schema>,
  ): Promise<any> {
    const dbOrTransaction = txn ? txn : this.drizzleDev;

    if (accountData.subtypeId) {
      // Fetch the children of the provided subtypeId
      const subtypeChildren = (await this.subtypesService.getSubtypes({
        subtypeId: accountData.subtypeId,
      })) as Subtype[];

      // Determine if the subtype has any children
      const hasSubtypeChildren = subtypeChildren.length > 0;
      if (hasSubtypeChildren) {
        throw forbidden();
      }
    }

    if (accountData.email) {
      const existingOrganisation = await dbOrTransaction.query.organisations.findFirst({
        where: eq(organisations.email, accountData.email),
      });

      if (existingOrganisation) {
        if (existingOrganisation.status === 1) {
          throw itemAlreadyExists(EntityName.ORGANISATION);
        } else {
          const [updatedOrganisation] = await dbOrTransaction
            .update(organisations)
            .set({
              ...existingOrganisation,
              ...accountData,
              workspaceId,
              status: 1,
            })
            .where(eq(organisations.id, existingOrganisation.id))
            .returning();
          return updatedOrganisation;
        }
      }
    }

    const [newOrganisation] = await dbOrTransaction
      .insert(organisations)
      .values({
        ...accountData,
        workspaceId,
      })
      .returning();

    return newOrganisation;
  }
}
