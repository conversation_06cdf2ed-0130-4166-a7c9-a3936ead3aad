import { Injectable } from '@nestjs/common';

import { WorkspaceType } from '@/constants/workspaces';

import { AccountTypeStrategy } from './account-type.strategy';

import { OrganisationStrategy } from './types/organisation';
import { SurgeonStrategy } from './types/surgeons';
import { ProfessionalStrategy } from './types/professional';

@Injectable()
export class AccountTypeFactory {
  private strategies: Map<WorkspaceType, AccountTypeStrategy>;

  constructor(
    private readonly organisationStrategy: OrganisationStrategy,
    private readonly surgeonStrategy: SurgeonStrategy,
    private readonly professionalStrategy: ProfessionalStrategy,
    // Add new strategies here
  ) {
    this.strategies = new Map();
    this.strategies.set(WorkspaceType.ORGANISATION, this.organisationStrategy);
    this.strategies.set(WorkspaceType.SURGEON, this.surgeonStrategy);
    this.strategies.set(WorkspaceType.PROFESSIONAL, this.professionalStrategy);
    // Add new account types here
  }

  getStrategy(accountType: WorkspaceType): AccountTypeStrategy {
    const strategy = this.strategies.get(accountType);
    if (!strategy) {
      throw new Error(`No strategy found for account type: ${accountType}`);
    }
    return strategy;
  }
}
