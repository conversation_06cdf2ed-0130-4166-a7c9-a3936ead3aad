import { Module } from '@nestjs/common';

import { WorkspacesService } from './workspaces.service';
import { WorkspacesController } from './workspaces.controller';
import { AccountTypeFactory } from './account-types/account-type.factory';
import { OrganisationStrategy } from './account-types/types/organisation';
import { SurgeonStrategy } from './account-types/types/surgeons';
import { ProfessionalStrategy } from './account-types/types/professional';
import { SubtypesModule } from '../subtypes/subtypes.module';

import { UsersModule } from '@/modules/users/users.module';

@Module({
  imports: [SubtypesModule, UsersModule],
  controllers: [WorkspacesController],
  providers: [
    WorkspacesService,
    AccountTypeFactory,
    OrganisationStrategy,
    SurgeonStrategy,
    ProfessionalStrategy,
  ],
  exports: [WorkspacesService],
})
export class WorkspacesModule {}
