import { OmitType, PartialType } from '@nestjs/swagger';

import { CreateAccountTypeByAdminDto, CreateAccountTypeDto } from './create-workspace.dto';

import { WorkspaceType } from '@/constants/workspaces';

export class UpdateAccountTypeDto extends PartialType(OmitType(CreateAccountTypeDto, ['type'])) {}

export class UpdateAccountTypeByAdminDto extends PartialType(CreateAccountTypeByAdminDto) {}

export function cleanDtoBasedOnAccountType(dto: UpdateAccountTypeDto, accountType: WorkspaceType) {
  const dtoCopy = { ...dto }; // Create a shallow copy to avoid mutating the original object

  switch (accountType) {
    case WorkspaceType.ORGANISATION:
      // Allow only fields that belong to Organisation
      return {
        hqLocation: dtoCopy.hqLocation,
        registrationNumber: dtoCopy.registrationNumber,
        countryId: dtoCopy.countryId,
        city: dtoCopy.city,
        email: dtoCopy.email,
        website: dtoCopy.website,
        phoneNumber: dtoCopy.phoneNumber,
        zipCode: dtoCopy.zipCode,
        aboutUs: dtoCopy.aboutUs,
        instagramLink: dtoCopy.instagramLink,
        facebookLink: dtoCopy.facebookLink,
        twitterLink: dtoCopy.twitterLink,
        linkedinLink: dtoCopy.linkedinLink,
        youtubeLink: dtoCopy.youtubeLink,
        tiktokLink: dtoCopy.tiktokLink,
        profileImageUrl: dtoCopy.profileImageUrl,
        profileImageUrlThumbnail: dtoCopy.profileImageUrlThumbnail,
        organisationCertifications: dtoCopy.organisationCertifications,
        workspacename: dtoCopy.workspacename,
      };

    case WorkspaceType.SURGEON:
      return {
        profileSummary: dtoCopy.profileSummary,
        yearsOfExperience: dtoCopy.yearsOfExperience,
        currentHospital: dtoCopy.currentHospital,
        email: dtoCopy.email,
        professionalCertificates: dtoCopy.professionalCertificates,
        professionalAwards: dtoCopy.professionalAwards,
        professionalAssociations: dtoCopy.professionalAssociations,
        workspacename: dtoCopy.workspacename,
      };

    case WorkspaceType.PROFESSIONAL:
      return {};

    default:
      return {};
  }
}
