import { WorkspaceType, WorkspacesStatus } from '@/constants/workspaces';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';

export class FindAllWorkspacesDto {
  @ApiPropertyOptional({
    description: 'status such',
    enum: WorkspacesStatus,
    example: '1',
  })
  @Transform(({ value }: TransformFnParams) => (value !== undefined ? Number(value) : undefined))
  @IsOptional()
  @IsEnum(WorkspacesStatus, {
    message: `type must be one of the following values: ${Object.values(WorkspacesStatus).join(', ')}`,
  })
  status?: WorkspacesStatus;

  @ApiPropertyOptional({
    description: 'workspace type',
    example: WorkspaceType.ORGANISATION,
  })
  @IsOptional()
  @IsEnum(WorkspaceType, {
    message: `type must be one of the following values: ${Object.values(WorkspaceType).join(', ')}`,
  })
  workspaceType?: WorkspaceType;
}
