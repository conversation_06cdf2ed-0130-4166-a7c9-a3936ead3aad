import {
  IsDate,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  ValidateIf,
} from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { WorkspaceType, WorkspacesStatus } from '@/constants/workspaces';
import { CertificationType } from '@/constants/organisation-certifications';

// General DTO for workspace creation
export class CreateWorkspaceDto {
  @ApiProperty({
    name: 'type',
    type: 'string',
    required: true,
    example: WorkspaceType.SURGEON,
  })
  @IsNotEmpty()
  @IsEnum(WorkspaceType)
  type: WorkspaceType;

  @ApiProperty({
    name: 'label',
    type: 'string',
    required: false,
    example: 'some label for workspaces',
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  label?: string;

  @ApiProperty({
    name: 'workspacename',
    type: 'string',
    required: false,
    example: 'unique-workspace_name123',
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @Matches(/^[a-zA-Z0-9_-]+$/, {
    message: 'Only alphanumeric characters, underscores, and hyphens are allowed',
  })
  workspacename?: string;

  @ApiProperty({
    name: 'subtypeId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  subtypeId: string;
}

export class CreateAccountTypeDto extends CreateWorkspaceDto {
  @ApiProperty({
    name: 'hqLocation',
    type: 'string',
    required: true,
    example: '123 Street Downtown, California, USA',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  hqLocation?: string;

  @ApiProperty({
    name: 'registrationNumber',
    type: 'string',
    required: true,
    example: '12455ABC',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  registrationNumber?: string;

  @ApiProperty({
    name: 'countryId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  countryId?: string;

  @ApiProperty({
    name: 'city',
    type: 'string',
    required: false,
    example: 'Delhi',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  city?: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION || o.type === WorkspaceType.SURGEON)
  @IsEmail()
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  email?: string;

  @ApiProperty({
    name: 'website',
    type: 'string',
    required: false,
    example: 'https://www.acme.com',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  website?: string;

  @ApiProperty({
    name: 'phoneNumber',
    type: 'string',
    required: true,
    example: '******-456-7890',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  phoneNumber?: string;

  @ApiProperty({
    name: 'zipCode',
    type: 'string',
    required: true,
    example: '90001',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  zipCode?: string;

  @ApiProperty({
    name: 'aboutUs',
    type: 'string',
    required: false,
    example: 'We are a leading provider of cardiac solutions.',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  aboutUs?: string;

  @ApiProperty({
    name: 'instagramLink',
    type: 'string',
    required: false,
    example: 'https://www.instagram.com/company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  instagramLink?: string;

  @ApiProperty({
    name: 'facebookLink',
    type: 'string',
    required: false,
    example: 'https://www.facebook.com/company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  facebookLink?: string;

  @ApiProperty({
    name: 'twitterLink',
    type: 'string',
    required: false,
    example: 'https://www.twitter.com/company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  twitterLink?: string;

  @ApiProperty({
    name: 'linkedinLink',
    type: 'string',
    required: false,
    example: 'https://www.linkedin.com/company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  linkedinLink?: string;

  @ApiProperty({
    name: 'youtubeLink',
    type: 'string',
    required: false,
    example: 'https://www.youtube.com/company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  youtubeLink?: string;

  @ApiProperty({
    name: 'tiktokLink',
    type: 'string',
    required: false,
    example: 'https://www.tiktok.com/@company',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  tiktokLink?: string;

  @ApiProperty({
    name: 'yearsOfExperience',
    type: 'number',
    required: false,
    example: 2,
  })
  @IsNumber()
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  yearsOfExperience?: number;

  @ApiProperty({
    name: 'currentHospital',
    type: 'string',
    required: false,
    example: 'ABCD Hospital',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  currentHospital?: string;

  @ApiProperty({
    name: 'profileSummary',
    type: 'string',
    required: false,
    example: 'ABCD Hospital',
  })
  @IsString()
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  profileSummary?: string;

  @ApiProperty({
    name: 'profileImageUrl',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrl?: string;

  @ApiProperty({
    name: 'profileImageUrlThumbnail',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrlThumbnail?: string;

  @ApiProperty({
    name: 'subscriptionPlanId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  subscriptionPlanId: string;

  @ApiProperty({ name: 'organisationCertifications', type: 'array', required: false, example: [] })
  @ValidateIf((o) => o.type === WorkspaceType.ORGANISATION)
  @IsOptional()
  organisationCertifications?: OrganisationCertificateDto[];

  @ApiProperty({ name: 'professionalAssociations', type: 'array', required: false, example: [] })
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  professionalAssociations?: ProfessionalAssociationDto[];

  @ApiProperty({ name: 'professionalAwards', type: 'array', required: false, example: [] })
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  professionalAwards?: ProfessionalAwardDto[];

  @ApiProperty({ name: 'professionalCertificates', type: 'array', required: false, example: [] })
  @ValidateIf((o) => o.type === WorkspaceType.SURGEON)
  @IsOptional()
  professionalCertificates?: ProfessionalCertificateDto[];
}

export class CreateAccountTypeByAdminDto extends CreateAccountTypeDto {
  @ApiProperty({
    name: 'userId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    enum: WorkspacesStatus,
    type: 'enum',
    required: true,
    example: WorkspacesStatus.ACTIVE,
  })
  @IsEnum(WorkspacesStatus)
  @IsOptional()
  status?: WorkspacesStatus;
}

export class OrganisationCertificateDto {
  @ApiProperty({ name: 'id', type: 'string', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ name: 'title', type: 'string', required: true })
  @IsString()
  title: string;

  @ApiProperty({ name: 'issuingBody', type: 'string', required: true })
  @IsString()
  issuingBody: string;

  @ApiProperty({ name: 'issuedDate', type: 'date', required: true })
  @IsDate()
  issuedDate: Date;

  @ApiProperty({ name: 'expiryDate', type: 'date', required: false })
  @IsDate()
  @IsOptional()
  expiryDate?: Date;

  @ApiProperty({ name: 'certificationType', type: 'string', required: true })
  @IsEnum(CertificationType)
  certificationType: CertificationType;
}

export class ProfessionalAssociationDto {
  @ApiProperty({ name: 'id', type: 'string', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ name: 'associationName', type: 'string', required: true })
  @IsString()
  associationName: string;

  @ApiProperty({ name: 'membershipId', type: 'string', required: false })
  @IsOptional()
  membershipId?: string;

  @ApiProperty({ name: 'joinedDate', type: 'date', required: true })
  @IsDate()
  joinedDate: string;

  @ApiProperty({ name: 'expiryDate', type: 'date', required: false })
  @IsDate()
  @IsOptional()
  expiryDate?: string;
}

export class ProfessionalAwardDto {
  @ApiProperty({ name: 'id', type: 'string', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ name: 'title', type: 'string', required: true })
  @IsString()
  title: string;

  @ApiProperty({ name: 'awardingBody', type: 'string', required: true })
  @IsString()
  awardingBody: string;

  @ApiProperty({ name: 'awardedDate', type: 'date', required: true })
  @IsDate()
  awardedDate: string;

  @ApiProperty({ name: 'description', type: 'string', required: false })
  @IsOptional()
  description?: string;
}

export class ProfessionalCertificateDto {
  @ApiProperty({ name: 'id', type: 'string', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ name: 'title', type: 'string', required: true })
  @IsString()
  title: string;

  @ApiProperty({ name: 'issuingBody', type: 'string', required: true })
  @IsString()
  issuingBody: string;

  @ApiProperty({ name: 'issuedDate', type: 'date', required: true })
  @IsDate()
  issuedDate: Date;

  @ApiProperty({ name: 'expiryDate', type: 'date', required: false })
  @IsDate()
  @IsOptional()
  expiryDate?: Date;
}
