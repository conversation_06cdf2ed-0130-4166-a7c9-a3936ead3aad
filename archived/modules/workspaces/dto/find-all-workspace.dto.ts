import { WorkspaceType } from '@/constants/workspaces';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class FindAllWorkspacesDto {
  @ApiPropertyOptional({
    description: 'Filter workspaces by workspace name or label',
    example: 'John',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Filter workspaces by types (comma-separated)',
    example: 'ORGANISATION,SURGEON',
  })
  @IsOptional()
  @Transform(({ value }) => {
    // Handle empty string, undefined, or null
    if (!value) return undefined;
    // Handle single value or comma-separated values
    const valuee = value.split(',').filter(Boolean);
    return valuee;
  })
  @IsEnum(WorkspaceType, {
    each: true,
    message: `type must be one of the following values: ${Object.values(WorkspaceType).join(', ')}`,
  })
  workspaceType?: WorkspaceType[];
}
