import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseEnumPipe,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { WorkspacesService } from './workspaces.service';

import { Roles } from '@/constants/user-types';

import { CreateAccountTypeByAdminDto } from './dto/create-workspace.dto';
import { UpdateAccountTypeByAdminDto, UpdateAccountTypeDto } from './dto/update-workspace.dto';
import { FindAllWorkspacesDto } from './dto/find-all-workspace.dto';

import { User } from '@/decorators/user.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';
import { Public } from '@/decorators/public.decorator';

import { AuthConstants } from '@/constants/auth';
import { WorkspaceType } from '@/constants/workspaces';

@Controller('workspaces')
@ApiBearerAuth()
@ApiTags('workspaces')
export class WorkspacesController {
  constructor(private readonly workspacesService: WorkspacesService) {}

  @Post()
  create(@Body() createAccountTypeDto: CreateAccountTypeByAdminDto) {
    return this.workspacesService.create(createAccountTypeDto, createAccountTypeDto.userId);
  }

  @Get()
  findAllWorkspaces(@Query() query: FindAllWorkspacesDto) {
    return this.workspacesService.getWorkspaces(query);
  }

  @Get('public')
  @Public()
  findAllWorkspacesForPublic(@Query() query: FindAllWorkspacesDto) {
    return this.workspacesService.getWorkspacesForPublic(query);
  }

  @Get('account-type/:accountType')
  findAllWorkspaceOfAnAccountType(
    @Param('accountType', new ParseEnumPipe(WorkspaceType)) accountType: WorkspaceType,
  ) {
    return this.workspacesService.getWorkspacesByAccountType(accountType);
  }

  @Get('own')
  findOwnWorkspace(@User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string) {
    return this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId);
  }

  @Patch('own')
  @UserRoles(
    Roles.ORGANISATION_ADMIN,
    Roles.ORGANISATION_USER,
    Roles.SURGEON_ADMIN,
    Roles.SURGEON_USER,
  )
  updateOwnWorkspace(
    @Body() updateAccountTypeDto: UpdateAccountTypeDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.workspacesService.update(workspaceId, updateAccountTypeDto);
  }

  @Get(':workspacename')
  findOneWorkspace(@Param('workspacename') workspaceId: string) {
    return this.workspacesService.findOneWorkspaceByWorkspacename(workspaceId);
  }

  @Get('workspacename/exists/:workspacename')
  findWorkspacenameAvailable(
    @Param('workspacename') workspacename: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.workspacesService.isWorkspacenameExists(workspacename, workspaceId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateAccountTypeDto: UpdateAccountTypeByAdminDto) {
    return this.workspacesService.update(id, updateAccountTypeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.workspacesService.softDelete(id);
  }
}
