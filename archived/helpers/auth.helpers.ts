import { getAuth, DecodedIdToken } from 'firebase-admin/auth';

import * as authException from '@/exceptions/system';

const processFirebaseAuthError = (errorCode: string): string => {
  const AUTH_ERROR_ID_TOKEN_REVOKED = 'auth/id-token-revoked';
  const AUTH_ERROR_ID_TOKEN_EXPIRED = 'auth/id-token-expired';
  const AUTH_ERROR_EMAIL_ID_ALREADY_EXISTS = 'auth/email-already-exists';
  const AUTH_ERROR_INTERNAL_ERROR = 'auth/internal-error';

  switch (errorCode) {
    case AUTH_ERROR_ID_TOKEN_REVOKED:
      return 'Token has been revoked, please re-authenticate';

    case AUTH_ERROR_ID_TOKEN_EXPIRED:
      return 'Token has expired, please re-authenticate';

    case AUTH_ERROR_EMAIL_ID_ALREADY_EXISTS:
      return 'Email ID already exists';

    case AUTH_ERROR_INTERNAL_ERROR:
    default:
      return 'Unable to authenticate user';
  }
};

export const verifyToken = async <T extends Partial<DecodedIdToken>>(
  token: string,
  checkRevoked: boolean = false,
) => {
  try {
    const userData: DecodedIdToken = await getAuth().verifyIdToken(token, checkRevoked);

    return userData as T;
  } catch (firebaseError: any) {
    console.error(firebaseError);
    if (firebaseError?.response?.message === 'Incorrect Usertype.')
      throw authException.unauthorized();
    throw authException.forbiddenFirebase(processFirebaseAuthError(firebaseError.code));
  }
};

export const verifySessionCookie = async <T extends Partial<DecodedIdToken>>(
  cookie: string,
  checkRevoked: boolean = false,
) => {
  try {
    const userData: DecodedIdToken = await getAuth().verifySessionCookie(cookie, checkRevoked);

    return userData as T;
  } catch (firebaseError: any) {
    console.error(firebaseError);
    if (firebaseError?.response?.message === 'Incorrect Usertype.')
      throw authException.unauthorized();
    throw authException.forbiddenFirebase(processFirebaseAuthError(firebaseError.code));
  }
};
