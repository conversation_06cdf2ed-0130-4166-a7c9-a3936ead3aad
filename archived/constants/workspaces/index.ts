import type { Professionals, Surgeons, Organisations } from '@/db/schema';
import { AccountType, WorkspaceRoles } from '../user-types';

// Workspace types (subset of AccountType, excluding GENERAL_PUBLIC)
export enum WorkspaceType {
  ORGANISATION = AccountType.ORGANISATION,
  SURGEON = AccountType.SURGEON,
  PROFESSIONAL = AccountType.PROFESSIONAL,
}
// First, let's define the relation names more strongly
export const WORKSPACE_RELATIONS = {
  ORGANISATION: 'organisations',
  SURGEON: 'surgeons',
  PROFESSIONAL: 'professionals',
} as const;

// Type for the relation names
// type WorkspaceRelation = (typeof WORKSPACE_RELATIONS)[keyof typeof WORKSPACE_RELATIONS];

// Map WorkspaceType to their relations
export const WORKSPACE_TYPE_RELATIONS: Record<WorkspaceType, { [key: string]: true }> = {
  [WorkspaceType.ORGANISATION]: {
    [WORKSPACE_RELATIONS.ORGANISATION]: true,
  },
  [WorkspaceType.SURGEON]: {
    [WORKSPACE_RELATIONS.SURGEON]: true,
  },
  [WorkspaceType.PROFESSIONAL]: {
    [WORKSPACE_RELATIONS.PROFESSIONAL]: true,
  },
} as const;

export type AccountTypeInfo = Professionals | Surgeons | Organisations;

export enum WorkspaceUsersStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  ARCHIVED = -1,
}

export enum WorkspacesStatus {
  ACTIVE = 2,
  INACTIVE = 1,
  PENDING = 0,
  REJECTED = -1,
  ARCHIVED = -2,
}

// Role levels for workspace roles
export enum WorkspaceRoleLevel {
  ADMIN = 'ADMIN',
  USER = 'USER',
}

// Mapping between workspace types and their roles
export const WORKSPACE_ROLES_MAP = {
  [WorkspaceType.ORGANISATION]: {
    [WorkspaceRoleLevel.ADMIN]: WorkspaceRoles.ORGANISATION_ADMIN,
    [WorkspaceRoleLevel.USER]: WorkspaceRoles.ORGANISATION_USER,
  },
  [WorkspaceType.SURGEON]: {
    [WorkspaceRoleLevel.ADMIN]: WorkspaceRoles.SURGEON_ADMIN,
    [WorkspaceRoleLevel.USER]: WorkspaceRoles.SURGEON_USER,
  },
  [WorkspaceType.PROFESSIONAL]: {
    [WorkspaceRoleLevel.ADMIN]: WorkspaceRoles.PROFESSIONAL_ADMIN,
    [WorkspaceRoleLevel.USER]: WorkspaceRoles.PROFESSIONAL_USER,
  },
} as const;
