export enum PostActiveStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

export enum PostStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
  PENDING_APPROVAL = 'pending_approval',
}

export enum PostType {
  POST = 'post',
  ARTICLE = 'article',
  OPPORTUNITY = 'opportunity',
  POLL = 'poll',
}

export const MEDIA_CATEGORY = {
  IMAGE: 'image',
  VIDEO: 'video',
} as const;

export type MediaCategoryType = (typeof MEDIA_CATEGORY)[keyof typeof MEDIA_CATEGORY]; // 'image' | 'video'

// Define the structure for individual media type info
type MediaInfo = {
  readonly accept: readonly string[];
  readonly extensions: Record<string, string>;
  readonly maxSize: number;
  readonly maxFiles: number;
};

// Define the structure for the entire MEDIA_TYPES_INFO object
type MediaInfoMap = Record<MediaCategoryType, MediaInfo>;

export const ENTITY_TYPES = {
  POST: 'post',
  ARTICLE: 'article',
  PROFILE: 'profile',
} as const;

export type EntityTypesType = (typeof ENTITY_TYPES)[keyof typeof ENTITY_TYPES];

export const MEDIA_TYPES_INFO: MediaInfoMap = {
  [MEDIA_CATEGORY.IMAGE]: {
    accept: ['image/jpeg', 'image/gif', 'image/jpg', 'image/png', 'image/webp'],
    extensions: {
      'image/jpeg': 'jpeg',
      'image/gif': 'gif',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
    },
    maxSize: 2097152, // 2 MB in bytes
    maxFiles: 20,
  } as const,

  [MEDIA_CATEGORY.VIDEO]: {
    accept: [
      'video/mp4',
      'video/avi',
      'video/webm',
      'video/x-ms-wmv',
      'video/x-flv',
      'video/mpeg',
      'video/quicktime',
      'video/x-m4v',
    ],
    extensions: {
      'video/mp4': 'mp4',
      'video/avi': 'avi',
      'video/webm': 'webm',
      'video/x-ms-wmv': 'wmv',
      'video/x-flv': 'flv',
      'video/mpeg': 'mpeg',
      'video/quicktime': 'mov',
      'video/x-m4v': 'm4v',
    },
    maxSize: 104857600, // 100 MB in bytes
    maxFiles: 1,
  } as const,
};

export enum ReportedReason {
  SPAM = 'spam',
  HARASSMENT = 'harassment',
  INAPPROPRIATE_CONTENT = 'inappropriate content',
}

export enum ReportedPostStatus {
  ARCHIVED = -1,
  INACTIVE = 0,
  ACTIVE = 1,
}
