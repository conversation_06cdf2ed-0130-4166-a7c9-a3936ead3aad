export const SUBTYPES = {
  CardiacSurgeon: {
    label: 'Cardiac Surgeon',
    SeniorSurgeons: {
      label: 'Senior Surgeons',
      Professor: { label: 'Professor' },
      AssociateProfessor: { label: 'Associate Professor' },
      AssistantProfessor: { label: 'Assistant Professor' },
      Consultant: { label: 'Consultant' },
      Specialist: { label: 'Specialist' },
      AssociateSpecialist: { label: 'Associate Specialist' },
    },
    JuniorSurgeons: {
      label: 'Junior Surgeons',
      SpecialistRegistrar: { label: 'Specialist Registrar' },
      SpecialtyTrainee: { label: 'Specialty Trainee' },
      SpecialtyDoctor: { label: 'Specialty Doctor' },
      JuniorDoctor: { label: 'Junior Doctor' },
    },
  },
  Organisation: {
    label: 'Organisation',
    Industry: {
      label: 'Industry',
      MedicalManufacturers: {
        label: 'Medical Manufacturers',
        DevicesInstrumentsAndEquipment: { label: 'Devices, Instruments, and Equipment' },
        Consumables: { label: 'Consumables' },
      },
      MedicalSuppliers: {
        label: 'Medical Suppliers',
        Consumables: { label: 'Consumables' },
      },
      MedicalTechnologies: {
        label: 'Medical Technologies',
        Products: { label: 'Products' },
        Services: { label: 'Services' },
        Solutions: { label: 'Solutions' },
      },
    },
    Hospitals: {
      label: 'Hospitals',
      Hospitals: { label: 'Hospitals' },
      Trusts: { label: 'Trusts' },
      CardiacCentres: { label: 'Cardiac Centres' },
    },
    Societies: {
      label: 'Societies',
      ISMICS: { label: 'ISMICS' },
      BISMICS: { label: 'BISMICS' },
      SMICTSI: { label: 'SMICTSI' },
      SCTS: { label: 'SCTS' },
      EACTS: { label: 'EACTS' },
    },
    Charities: {
      label: 'Charities',
      NonProfits: { label: 'Non-profits' },
      HealthcareRelatedCharities: { label: 'Healthcare-related Charities' },
    },
    Insurers: {
      label: 'Insurers',
      HealthcareInsuranceCompanies: { label: 'Healthcare Insurance Companies' },
    },
  },
  GeneralPublic: {
    label: 'General Public',
    Doctor: {
      label: 'Doctor',
      SeniorDoctors: {
        label: 'Senior Doctors',
        Professor: { label: 'Professor' },
        AssociateProfessor: { label: 'Associate Professor' },
        AssistantProfessor: { label: 'Assistant Professor' },
        Consultant: { label: 'Consultant' },
        Specialist: { label: 'Specialist' },
        AssociateSpecialist: { label: 'Associate Specialist' },
      },
      JuniorDoctors: {
        label: 'Junior Doctors',
        SpecialistRegistrar: { label: 'Specialist Registrar' },
        SpecialtyTrainee: { label: 'Specialty Trainee' },
        SpecialtyDoctor: { label: 'Specialty Doctor' },
      },
    },
    Perfusionist: { label: 'Perfusionist' },
    SurgicalPractitioner: { label: 'Surgical Practitioner' },
    Nurse: { label: 'Nurse' },
    Scientist: { label: 'Scientist' },
    Patient: { label: 'Patient' },
    Student: { label: 'Student' },
    Customer: { label: 'Customer' },
    Public: { label: 'Public' },
    Industry: {
      label: 'Industry',
      Staff: { label: 'Staff (associated with organization)' },
      Administrator: { label: 'Administrator (under organization)' },
    },
  },
  Professionals: {
    label: 'Professionals',
    Perfusionist: { label: 'Perfusionist' },
    Researcher: {
      label: 'Researcher',
    },
  },
} as const;

export enum SubtypesStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}
