// Account types shown in dropdown during signup/subscription
export enum AccountType {
  ORGANISATION = 'ORGANISATION',
  SURGEON = 'SURGEON',
  PROFESSIONAL = 'PROFESSIONAL',
  GENERAL_PUBLIC = 'GENERAL_PUBLIC',
}

// Global roles (non-workspace roles)
export enum GlobalRoles {
  GENERAL_PUBLIC = 'GENERAL_PUBLIC',
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
  OWNER = 'OWNER',
}

// Workspace-specific roles
export enum WorkspaceRoles {
  ORGANISATION_ADMIN = 'ORG_ADMIN',
  ORGANISATION_USER = 'ORG_USER',
  SURGEON_ADMIN = 'SURGEON_ADMIN',
  SURGEON_USER = 'SURGEON_USER',
  PROFESSIONAL_ADMIN = 'PROFESSIONAL_ADMIN',
  PROFESSIONAL_USER = 'PROFESSIONAL_USER',
}

// All possible roles in the system
export enum Roles {
  GENERAL_PUBLIC = GlobalRoles.GENERAL_PUBLIC,
  SYSTEM_ADMIN = GlobalRoles.SYSTEM_ADMIN,
  SUPER_ADMIN = GlobalRoles.SUPER_ADMIN,
  OWNER = GlobalRoles.OWNER,
  ORGANISATION_ADMIN = WorkspaceRoles.ORGANISATION_ADMIN,
  ORGANISATION_USER = WorkspaceRoles.ORGANISATION_USER,
  SURGEON_ADMIN = WorkspaceRoles.SURGEON_ADMIN,
  SURGEON_USER = WorkspaceRoles.SURGEON_USER,
  PROFESSIONAL_ADMIN = WorkspaceRoles.PROFESSIONAL_ADMIN,
  PROFESSIONAL_USER = WorkspaceRoles.PROFESSIONAL_USER,
}

export enum EntityType {
  USER = 'user',
  WORKSPACE = 'workspace',
}
