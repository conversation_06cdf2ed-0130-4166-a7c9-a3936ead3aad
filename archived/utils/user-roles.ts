import { Roles, GlobalRoles, WorkspaceRoles } from '@/constants/user-types';
import { WorkspaceRoleLevel, WORKSPACE_ROLES_MAP, WorkspaceType } from '@/constants/workspaces';

export const isWorkspaceRole = (role: Roles | WorkspaceRoles): boolean => {
  return Object.values(WorkspaceRoles).includes(role as WorkspaceRoles);
};

export const isGlobalRole = (role: Roles | GlobalRoles): boolean => {
  return Object.values(GlobalRoles).includes(role as GlobalRoles);
};

export const getWorkspaceRole = (
  workspaceType: WorkspaceType,
  level: WorkspaceRoleLevel,
): WorkspaceRoles => {
  return WORKSPACE_ROLES_MAP[workspaceType][level];
};

export const getWorkspaceTypeFromRole = (role: WorkspaceRoles): WorkspaceType | null => {
  for (const [type, roles] of Object.entries(WORKSPACE_ROLES_MAP)) {
    if (Object.values(roles).includes(role)) {
      return type as WorkspaceType;
    }
  }
  return null;
};

export const getWorkspaceRoleFromRoleAndLevel = (
  role: WorkspaceRoles,
  level: WorkspaceRoleLevel,
): WorkspaceRoles | null => {
  const workspaceType = getWorkspaceTypeFromRole(role);
  if (!workspaceType) return null;
  return getWorkspaceRole(workspaceType, level);
};
