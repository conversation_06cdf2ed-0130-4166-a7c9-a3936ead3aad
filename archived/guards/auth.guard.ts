import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';

// import { Roles } from '@/constants/user-types';
// import { Permissions } from '@/constants/permissions';
import { <PERSON><PERSON><PERSON> } from '@/constants/auth';

import { sessionExpired, unauthorized } from '@/exceptions/system';

import { authUserData } from '@/interfaces/auth';

import { CustomConfigService } from '@/config/configuration.service';

import { verifySessionCookie, verifyToken } from '@/helpers/auth.helpers';

import { IS_PUBLIC_METADATA_KEY } from '@/decorators/public.decorator';
import { IS_SESSION_ONLY_METADATA_KEY } from '@/decorators/session-only.decorator';
import { IS_TOKEN_ONLY_METADATA_KEY } from '@/decorators/token-only.decorator';

type UserContext = Request & {
  user: authUserData;
};

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    protected readonly customConfigService: CustomConfigService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: UserContext = context.switchToHttp().getRequest();
    const isHealthEndpoint: boolean = request.path.replaceAll('/', '').endsWith('health');
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    const isSessionOnly = this.reflector.getAllAndOverride<boolean>(IS_SESSION_ONLY_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    const isTokenOnly = this.reflector.getAllAndOverride<boolean>(IS_TOKEN_ONLY_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const token = this.extractTokenFromHeader(request);

    // Extract user details even for public routes
    if (token) {
      try {
        request.user = await verifyToken<authUserData>(token);
      } catch (e) {
        console.error('Invalid token for public route:', e.message);
      }
    }

    // Allow health and public routes without additional checks
    if (isPublic || isHealthEndpoint) {
      return true;
    }

    // Enforce token or session requirements for protected routes
    if (!isSessionOnly && !token) {
      throw unauthorized();
    }

    const sessionCookie = request.cookies?.[CookieKey];

    if (!isTokenOnly && !sessionCookie) {
      throw sessionExpired();
    }

    request.user = isSessionOnly
      ? await verifySessionCookie<authUserData>(sessionCookie)
      : await verifyToken<authUserData>(token!);

    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
