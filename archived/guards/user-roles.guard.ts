import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { USER_ROLES_METADATA_KEY } from '@/decorators/user-roles.decorator';

import { Roles } from '@/constants/user-types';

import { UserData } from '@/interfaces/auth';

@Injectable()
export class UserRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(cxt: ExecutionContext): Promise<boolean> {
    const requiredUserRoles = this.reflector.getAllAndOverride<Roles[] | undefined>(
      USER_ROLES_METADATA_KEY,
      [cxt.getHandler(), cxt.getClass()],
    );

    if (!requiredUserRoles || requiredUserRoles.length === 0) {
      return true;
    }

    const req = cxt.switchToHttp().getRequest();
    const user: UserData = req.user;

    return (
      requiredUserRoles.includes(user.role) ||
      (!!user.role && requiredUserRoles.includes(user.role))
    );
  }
}
