import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus, HttpException } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';

import { IException } from '@/interfaces/system';

@Catch()
class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch<T extends Error>(exception: T, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string | null = '';
    let metadata = null;
    let stack = null;

    if (exception instanceof HttpException) {
      const httpException: IException = exception as unknown as IException;
      statusCode = exception.getStatus();
      message = httpException?.response?.message || null;
      metadata = httpException?.response?.metadata || null;
      stack = httpException?.response?.stack || exception?.stack || null;
    } else {
      message = exception?.message || '';
      stack = exception?.stack || null;
    }

    const responseBody = {
      statusCode,
      message,
      data: null,
      metadata,
      stack,
      timestamp: new Date().toISOString(),
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, statusCode);
  }
}

export default AllExceptionsFilter;
