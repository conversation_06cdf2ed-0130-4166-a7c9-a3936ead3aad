/* DO NOT EDIT, file generated by nestjs-i18n */
  
/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "auth": {
        "email_registered_not_verified": string;
        "user_cannot_be_created_without_any_role": string;
        "workspace_type_role_required_a_workspace_id": string;
        "global_type_role_should_not_need_a_workspace_id": string;
        "recent_sign_in_required": string;
        "cookie_not_found": string;
        "invalid_cookie": string;
        "session_expired": string;
        "invalid_credentials": string;
        "account_locked": string;
        "token_invalid": string;
        "token_expired": string;
        "user_no_workspace_access": string;
    };
    "exception": {
        "not_found": string;
        "already_exists": string;
        "validation_error": string;
        "route_not_found": string;
        "unauthorized": string;
        "forbidden": string;
        "item_size_too_large": string;
        "limit_reached": string;
        "session_expired": string;
    };
    "followers": {
        "self_follow_not_allowed": string;
    };
    "opportunities": {
        "already_applied": string;
        "opportunity_expired": string;
    };
    "posts": {
        "post_schedule": {
            "date_future_required": string;
        };
        "country_id": {
            "post_in_unsubscribed_country": string;
        };
        "post_status": {
            "invalid_transition": string;
        };
        "reported_post": {
            "post_creator_cannot_report_a_post": string;
            "reported_post_is_not_modifiable": string;
        };
    };
    "roles": {
        "id_or_name_required": string;
    };
    "subtypes": {
        "error_messages": {
            "parent_subtype_not_found": string;
            "parent_subtype_belongs_to_different_account_type": string;
            "subtype_id_or_name_required": string;
        };
    };
    "workspaces": {
        "workspace_not_verified": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
