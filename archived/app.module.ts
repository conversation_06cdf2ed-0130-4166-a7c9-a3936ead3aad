import * as path from 'path';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DrizzlePostgresModule } from '@knaadh/nestjs-drizzle-postgres';
import { I18nModule, AcceptLanguageResolver } from 'nestjs-i18n';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';

import rdsCertificate from './certificates/aws-rds-certificate/aws.rds';

import * as schema from '@/db/schema';

import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { WorkspaceFilesModule } from '@/modules/workspace-files/workspace-files.module';
import { WorkspaceUsersModule as AdminWorkspaceUsersModule } from '@/modules/workspace-users/admin/workspace-users.module';
import { WorkspaceUsersModule as WorkspaceAdminWorkspaceUsersModule } from '@/modules/workspace-users/workspace-admin/workspace-users.module';
import { UsersModule } from '@/modules/users/users.module';
import { RolesModule } from '@/modules/roles/roles.module';
import { ModulesModule } from '@/modules/modules/modules.module';
import { PermissionsModule } from '@/modules/permissions/permissions.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { DropDownModule } from '@/modules/drop-downs/dropdowns.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { OpportunitiesModule } from '@/modules/opportunities/opportunities.module';
import { OpportunitiesAttachmentsModule } from '@/modules/opportunities-attachments/opportunities-attachments.module';
import { SubtypesModule } from '@/modules/subtypes/subtypes.module';
import { WaitlistsModule } from '@/modules/waitlists/waitlists.module';
import { PostsModule } from '@/modules/posts/posts.module';
import { CategoryModule } from '@/modules/category/category.module';
import { SubscriptionsModule } from '@/modules/subscriptions/subscriptions.module';
import { UtilsModule } from '@/modules/utils/utils.module';
import { FollowersModule } from '@/modules/followers/followers.module';

import envConfiguration from '@/config/configuration';
import { CustomConfigService } from '@/config/configuration.service';

import AllExceptionsFilter from '@/filters/AllExceptions.filter';
import NotFoundFilter from '@/filters/NotFound.filter';

import ResponseTransformInterceptor from '@/interceptors/Response.interceptor';

import ValidationPipe from '@/pipes/Validation.pipe';

import validate from '@/validation/env.validation';

import { DEFAULT_LOCALE } from '@/constants/i18n';

import { ISystemConfig } from '@/interfaces/system';

import { HealthController } from '@/health/health.controller';

import { PermissionsGuard } from '@/guards/permissions.guard';
import { AuthGuard } from '@/guards/auth.guard';
import { UserRolesGuard } from '@/guards/user-roles.guard';
import { ActiveWorkspaceGuard } from '@/guards/active-workspace.guard';

import { MailModule } from '@/common/mail/mail.module';
import { FileStorageModule } from '@/common/file-storage/file-storage.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { FeedModule } from './modules/feed/feed.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      ignoreEnvFile: true,
      isGlobal: true,
      cache: true,
      load: [envConfiguration],
      validate,
    }),

    DrizzlePostgresModule.registerAsync({
      tag: 'DB_DEV',
      inject: [ConfigService],
      useFactory: async (config: ConfigService<ISystemConfig>) => {
        const configService = new CustomConfigService(config);
        const databaseConfig = configService.getDatabaseConfig();

        return {
          tag: 'DB_DEV',
          postgres: {
            url: '',
            config: {
              host: databaseConfig.host,
              database: databaseConfig.name,
              user: databaseConfig.user,
              password: databaseConfig.password,
              port: databaseConfig.port,
              ssl: databaseConfig.ssl
                ? {
                    ca: rdsCertificate,
                  }
                : undefined,
            },
          },
          config: { schema: { ...schema }, logger: true },
        };
      },
    }),

    I18nModule.forRoot({
      fallbackLanguage: DEFAULT_LOCALE,
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      typesOutputPath: path.join(__dirname, '/generated/i18n.generated.js'),
      resolvers: [AcceptLanguageResolver],
    }),

    //TODO: WAITLIST: HAVE TO BE MODIFIED LATER
    ...(new CustomConfigService(new ConfigService()).getEnvironment()?.isProduction
      ? []
      : [
          UsersModule,
          WorkspacesModule,
          WorkspaceFilesModule,
          AdminWorkspaceUsersModule,
          RolesModule,
          ModulesModule,
          PermissionsModule,
          WorkspaceAdminWorkspaceUsersModule,
          UserRolesModule,
          OpportunitiesModule,
          OpportunitiesAttachmentsModule,
          MailModule,
          FileStorageModule,
          SubtypesModule,
          DropDownModule,
          AuthModule,
          PostsModule,
          SubscriptionsModule,
          CategoryModule,
          UtilsModule,
          FollowersModule,
          NotificationsModule,
        ]),
    WaitlistsModule,
    FeedModule,
  ],
  controllers: [HealthController],
  providers: [
    CustomConfigService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: NotFoundFilter,
    },
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
    {
      provide: APP_GUARD,
      useClass: UserRolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ActiveWorkspaceGuard,
    },
  ],
  exports: [CustomConfigService],
})
export class AppModule {}
