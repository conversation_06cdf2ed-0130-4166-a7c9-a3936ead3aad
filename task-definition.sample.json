{
    "family": "minicardiac_ecs-task-definition_$ENVIRONMENT",
    "executionRoleArn": "$TASK_EXECUTION_ROLE",
    "containerDefinitions": [
      {
        "name": "api",
        "image": "$CONTAINER_REGISTRY_NAME/minicardiac-$ENVIRONMENT:$SEM_VERSION",
        "memoryReservation": 200,
        "portMappings": [
          {
            "hostPort": 0,
            "protocol": "tcp",
            "containerPort": $PORT
          }
        ],
        "essential": true,
        "healthCheck": {
          "retries": 5,
          "command": ["CMD-SHELL", "curl -f http://localhost:$PORT/api/v1/health || exit 1"],
          "timeout": 5,
          "interval": 10,
          "startPeriod": 30
        },
        "secrets": [
          {
            "name": "DOPPLER_TOKEN",
            "valueFrom": "$DOPPLER_TOKEN_SECRETS_ARN"
          }
        ],
        "logConfiguration": {
          "logDriver": "awslogs",
          "options": {
            "awslogs-group": "/ecs/minicardiac-$ENVIRONMENT",
            "awslogs-region": "$AWS_REGION",
            "awslogs-stream-prefix": "app"
          }
        },
        "mountPoints": [],
        "volumesFrom": []
      }
    ],
    "volumes": [],
    "placementConstraints": [],
    "requiresCompatibilities": ["EC2"],
    "ipcMode": "none"
  }
  