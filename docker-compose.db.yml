services:
  db:
    container_name: minicardiac_db
    image: postgres:17-alpine
    restart: always
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=gmh7kmain
    ports:
      - "5432:5432"
    volumes:
      - minicardiac_db:/var/lib/postgresql/data
    networks:
      - minicardiac_server_network

volumes:
  minicardiac_db:

networks:
  minicardiac_server_network:
    name: minicardiac_server_network