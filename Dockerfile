ARG NODE_VARIANT=22-alpine

# ---- Base ----
FROM node:${NODE_VARIANT} as base
WORKDIR /usr/src/app
ENV NODE_ENV development
COPY package.json pnpm-lock.yaml tsconfig.json ./
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --ignore-scripts

# ---- Prod Dependency Builder ----
FROM node:${NODE_VARIANT} as prodDependencyBase
WORKDIR /usr/src/app
ENV NODE_ENV production
COPY package.json pnpm-lock.yaml tsconfig.json ./
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile --prod --ignore-scripts

# ---- Builder ----
FROM base as builder
WORKDIR /usr/src/app
COPY src src/
COPY package.json pnpm-lock.yaml tsconfig.json tsconfig.build.json nest-cli.json ./
RUN npm run build

FROM node:${NODE_VARIANT}
WORKDIR /usr/src/app
ENV NODE_ENV production
RUN apk add dumb-init curl
COPY scripts/secret_manager_setup.sh ./secret_manager_setup.sh
COPY --from=prodDependencyBase /usr/src/app/node_modules /usr/src/app/node_modules
COPY --from=builder /usr/src/app/dist /usr/src/app
COPY src/i18n /usr/src/app/i18n
COPY scripts/entrypoint.sh ./entrypoint.sh
COPY VERSION.txt package.json pnpm-lock.yaml ./
RUN ./secret_manager_setup.sh
# This is a hack because knex tries processing these files and fails so remove the types
RUN rm migrations/*.d.ts
CMD ["/usr/src/app/entrypoint.sh"]