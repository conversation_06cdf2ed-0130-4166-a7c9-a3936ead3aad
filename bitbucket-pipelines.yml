image: node:20

definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-store

options:
  max-time: 15

pipelines:
  pull-requests:
    "**":
      - step:
          name: Build & Check
          caches:
            - pnpm
            - node
          script:
            - npm install -g pnpm
            - pnpm install --frozen-lockfile
      - parallel:
          - step:
              name: Prettier validate files
              caches:
                - pnpm
                - node
              script:
                - npm run prettier:check
          - step:
              name: Lint Files
              caches:
                - pnpm
                - node
              script:
                - npm run lint
          - step:
              name: Type Check
              caches:
                - pnpm
                - node
              script:
                - npx tsc

  branches:
    develop:
      - step:
          name: Build & Deploy to Stage
          deployment: staging
          size: 2x
          services:
            - docker
          caches:
            - pnpm
            - node
            - docker
          script:
            - export DOCKER_BUILDKIT=1
            - apt-get update && apt-get install -y gettext
            - export SEM_VERSION=$(echo $BITBUCKET_COMMIT | head -c 7)
            - docker build -t minicardiac-$ENVIRONMENT:$SEM_VERSION .
            - envsubst <task-definition.sample.json >task-definition.json

            - pipe: atlassian/aws-ecr-push-image:2.4.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: 'minicardiac-$ENVIRONMENT'
                TAGS: '$SEM_VERSION'

            - pipe: atlassian/aws-ecs-deploy:1.12.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                CLUSTER_NAME: $AWS_ECS_CLUSTER_NAME
                SERVICE_NAME: $AWS_ECS_SERVICE_NAME
                TASK_DEFINITION: 'task-definition.json'
