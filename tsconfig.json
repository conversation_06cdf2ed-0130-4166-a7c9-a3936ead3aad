{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "lib": [
      "ESNext"
    ],
    "moduleResolution": "node",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "removeComments": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "incremental": true,
    "target": "ES2022",
    "outDir": "dist/",
    "baseUrl": ".",
    "strictNullChecks": true,
    "noImplicitAny": true,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/interfaces/*": [
        "src/interfaces/*"
      ],
      "@/constants/*": [
        "src/constants/*"
      ],
      "@/config/*": [
        "src/config/*"
      ],
      "@/common/*": [
        "src/common/*"
      ],
      "@/authentication/*": [
        "src/authentication/*"
      ],
      "@/modules/*": [
        "src/modules/*"
      ],
      "@/db/*": [
        "src/db/*"
      ],
      "@/interceptors/*": [
        "src/interceptors/*"
      ],
      "@/filters/*": [
        "src/filters/*"
      ],
      "@/pipes/*": [
        "src/pipes/*"
      ],
      "@/validation/*": [
        "src/validation/*"
      ],
      "@/utils/*": [
        "src/utils/*"
      ],
      "@/exceptions/*": [
        "src/exceptions/*"
      ],
      "@/generated/*": [
        "src/generated/*"
      ],
      "@/decorators/*": [
        "src/decorators/*"
      ],
      "@/guards/*": [
        "src/guards/*"
      ],
      "@/helpers/*": [
        "src/helpers/*"
      ],
      "@/health/*": [
        "src/health/*"
      ]
    }
  },
  "include": [
    "src/**/*",
  ],
  "exclude": [
    "node_modules/**/*",
    ".vscode/**/*",
    "dist"
  ],
  "ts-node": {
    "require": [
      "tsconfig-paths/register"
    ]
  }
}