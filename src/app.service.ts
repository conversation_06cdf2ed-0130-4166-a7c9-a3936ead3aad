/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-empty-function */
import {
  Injectable,
  OnApplicationBootstrap,
  OnApplicationShutdown,
  BeforeApplicationShutdown,
} from '@nestjs/common';
import { ListTopicsCommand, ListTopicsCommandOutput, PublishCommand } from '@aws-sdk/client-sns';

import { CustomConfigService } from '@/config/configuration.service';

import { snsClient } from '@/config/aws';

const DEPLOYMENT_SNS_TOPIC_NAME = 'minicardiac-bot-deployment-notifications';

@Injectable()
export default class AppService
  implements OnApplicationBootstrap, OnApplicationShutdown, BeforeApplicationShutdown
{
  constructor(private configService: CustomConfigService) {}

  async onApplicationBootstrap() {
    const environment = this.configService.getEnvironment();

    if (environment.isDevelop || environment.isStaging || environment.isProduction) {
      try {
        // notify SNS only when it's a server environment

        // find the topic to notify
        const listTopicsCommand = new ListTopicsCommand();
        const listTopicsResult: ListTopicsCommandOutput = await snsClient.send(listTopicsCommand);
        // Iterate over the topics to find the one that matches the desired name
        const topicArn: string = listTopicsResult.Topics?.find((topic) =>
          topic.TopicArn?.endsWith(`:${DEPLOYMENT_SNS_TOPIC_NAME}`),
        )?.TopicArn as string;
        if (!topicArn) return;

        // https://docs.aws.amazon.com/chatbot/latest/adminguide/custom-notifs.html
        const deploymentPayload = {
          version: '1.0',
          source: 'custom',
          content: {
            textType: 'client-markdown',
            title: `:white_check_mark: Minicardiac Server - Deployment Stable for ${this.configService.getEnvironmentName()}`,
            description: `Version ${this.configService.getAppVersion()} rolled over and stable.`,
          },
        };

        const params = {
          Message: JSON.stringify(deploymentPayload),
          TopicArn: topicArn,
        };

        const publishMessageToTopicCommand = new PublishCommand(params);
        await snsClient.send(publishMessageToTopicCommand);
      } catch (error) {
        console.error(`Error app service: ${JSON.stringify(error, undefined, 2)}`);
      }
    } else {
      console.log('Skipped deployment notification');
    }
  }

  async beforeApplicationShutdown() {}

  async onApplicationShutdown() {}
}
