import { Transform, plainToInstance } from 'class-transformer';
import { IsN<PERSON>ber, IsString, Max, <PERSON>, validateSync } from 'class-validator';

class EnvironmentVariables {
  @IsString()
  DB_HOST: string;

  @IsString()
  DB_USER: string;

  @IsString()
  DB_PASSWORD: string;

  @IsString()
  DB_NAME: string;

  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(0)
  @Max(65535)
  PORT: number;

  @IsString()
  ASSETS_BUCKET_NAME: string;

  @IsString()
  ASSETS_CDN_BASE_URL: string;

  @IsString()
  AWS_ACCESS_KEY_ID: string;

  @IsString()
  AWS_DEFAULT_REGION: string;

  @IsString()
  AWS_SECRET_ACCESS_KEY: string;

  @IsString()
  SMTP_HOST: string;

  @IsString()
  SMTP_PASSWORD: string;

  @IsString()
  SMTP_USER: string;
}

const validate = (config: Record<string, unknown>) => {
  const validatedConfig = plainToInstance(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, { skipMissingProperties: false });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
};

export default validate;
