import * as path from 'path';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DrizzlePostgresModule } from '@knaadh/nestjs-drizzle-postgres';
import { I18nModule, AcceptLanguageResolver } from 'nestjs-i18n';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

import rdsCertificate from './certificates/aws-rds-certificate/aws.rds';

import * as schema from '@/db/schema';

import envConfiguration from '@/config/configuration';
import { CustomConfigService } from '@/config/configuration.service';

import AllExceptionsFilter from '@/filters/AllExceptions.filter';
import NotFoundFilter from '@/filters/NotFound.filter';

import ResponseTransformInterceptor from '@/interceptors/Response.interceptor';

import ValidationPipe from '@/pipes/Validation.pipe';

import validate from '@/validation/env.validation';

import { DEFAULT_LOCALE } from '@/constants/i18n';

import { ISystemConfig } from '@/interfaces/system';

import { HealthController } from '@/health/health.controller';

import { PermissionsGuard } from '@/guards/permissions.guard';
import { AuthGuard } from '@/guards/auth.guard';
import { UserRolesGuard } from '@/guards/user-roles.guard';
import { AccountTypesGuard } from '@/guards/account-types.guard';
import { ActiveWorkspaceGuard } from '@/guards/active-workspace.guard';
import { AccountSetupStagesGuard } from '@/guards/account-setup-stages.guard';

import { MailModule } from '@/common/mail/mail.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { WorkspaceUsersModule } from '@/modules/workspace-users/workspace-users.module';
import { UsersModule } from '@/modules/users/users.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { DropDownModule } from '@/modules/drop-downs/dropdowns.module';
import { SegmentCategoriesModule } from '@/modules/segment-categories/segment-categories.module';
import { SubscriptionsModule } from '@/modules/subscriptions/subscriptions.module';
import { UtilsModule } from '@/modules/utils/utils.module';
import { DocumentRequirementsModule } from '@/modules/document-requirements/document-requirements.module';
import { EmployersModule } from '@/modules/employer/employer.module';
import { OnboardingModule } from '@/modules/onboarding/onboarding.module';
import { NetworkingModule } from '@/modules/networking/networking.module';
import { TagsModule } from '@/modules/tags/tags.module';
import { PostsModule } from '@/modules/posts/posts.module';
import { TagFollowModule } from '@/modules/tag-follow/tag-follow.module';

import AppService from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      ignoreEnvFile: true,
      isGlobal: true,
      cache: true,
      load: [envConfiguration],
      validate,
    }),

    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),

    DrizzlePostgresModule.registerAsync({
      tag: 'DB_DEV',
      inject: [ConfigService],
      useFactory: async (config: ConfigService<ISystemConfig>) => {
        const configService = new CustomConfigService(config);
        const databaseConfig = configService.getDatabaseConfig();

        return {
          tag: 'DB_DEV',
          postgres: {
            url: '',
            config: {
              host: databaseConfig.host,
              database: databaseConfig.name,
              user: databaseConfig.user,
              password: databaseConfig.password,
              port: databaseConfig.port,
              ssl: databaseConfig.ssl
                ? {
                    ca: rdsCertificate,
                  }
                : undefined,
            },
          },
          config: { schema: { ...schema }, logger: true },
        };
      },
    }),

    I18nModule.forRoot({
      fallbackLanguage: DEFAULT_LOCALE,
      loaderOptions: {
        path: path.join(__dirname, '/i18n/'),
        watch: true,
      },
      typesOutputPath: path.join(__dirname, '/generated/i18n.generated.js'),
      resolvers: [AcceptLanguageResolver],
    }),

    AuthModule,
    OnboardingModule,
    UsersModule,
    WorkspacesModule,
    UserRolesModule,
    UtilsModule,
    MailModule,
    SubscriptionsModule,
    SegmentCategoriesModule,
    DropDownModule,
    WorkspaceUsersModule,
    UserRolesModule,
    DocumentRequirementsModule,
    EmployersModule,
    NetworkingModule,
    TagsModule,
    PostsModule,
    TagFollowModule,
  ],
  controllers: [HealthController],
  providers: [
    CustomConfigService,
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_FILTER,
      useClass: NotFoundFilter,
    },
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
    {
      provide: APP_GUARD,
      useClass: UserRolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AccountTypesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AccountSetupStagesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ActiveWorkspaceGuard,
    },
  ],
  exports: [CustomConfigService],
})
export class AppModule {}
