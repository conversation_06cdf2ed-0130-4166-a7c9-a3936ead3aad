import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('professional_certificates', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('specialist_id')
        .notNullable()
        .references('id')
        .inTable('specialists')
        .onDelete('CASCADE')
        .index('index_professional_certificates_on_specialist_id');

      t.text('title').notNullable();
      t.text('issuing_body').notNullable();
      t.date('issued_date').notNullable();
      t.date('expiry_date');

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER professional_certificates_updated_at BEFORE UPDATE
      ON professional_certificates FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `,
    )

    .createTable('professional_awards', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('specialist_id')
        .notNullable()
        .references('id')
        .inTable('specialists')
        .onDelete('CASCADE')
        .index('index_professional_awards_on_specialist_id');

      t.text('title').notNullable();
      t.text('awarding_body').notNullable();
      t.date('awarded_date').notNullable();
      t.text('description');

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER professional_awards_updated_at BEFORE UPDATE
      ON professional_awards FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `,
    )

    .createTable('professional_associations', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('specialist_id')
        .notNullable()
        .references('id')
        .inTable('specialists')
        .onDelete('CASCADE')
        .index('index_professional_associations_on_specialist_id');

      t.text('association_name').notNullable();
      t.text('membership_id');
      t.date('joined_date').notNullable();
      t.date('expiry_date');

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
      CREATE TRIGGER professional_associations_updated_at BEFORE UPDATE
      ON professional_associations FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `,
    )

    .createTable('organisation_certifications', (t) => {
      t.uuid('id').primary().defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('organisation_id')
        .notNullable()
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_organisation_certifications_on_organisation_id');

      t.text('title').notNullable();
      t.text('issuing_body').notNullable();
      t.date('issued_date').notNullable();
      t.date('expiry_date');
      t.text('certification_type').notNullable();

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    }).raw(`
      CREATE TRIGGER organisation_certifications_updated_at BEFORE UPDATE
      ON organisation_certifications FOR EACH ROW EXECUTE PROCEDURE 
      update_updated_at_column();
    `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema
    .dropTable('organisation_certifications')
    .dropTable('professional_associations')
    .dropTable('professional_awards')
    .dropTable('professional_certificates');
}
