import type { Knex } from 'knex';

const countriesData = [
  { name: 'Afghanistan', key: 'AF', continent: 'Asia', status: 1 },
  { name: 'Albania', key: 'AL', continent: 'Europe', status: 1 },
  { name: 'Algeria', key: 'DZ', continent: 'Africa', status: 1 },
  { name: 'American Samoa', key: 'AS', continent: 'Oceania', status: 1 },
  { name: 'Andorra', key: 'AD', continent: 'Europe', status: 1 },
  { name: 'Angola', key: 'AO', continent: 'Africa', status: 1 },
  { name: '<PERSON><PERSON><PERSON>', key: 'AI', continent: 'North America', status: 1 },
  { name: 'Antarctica', key: 'AQ', continent: 'Antarctica', status: 1 },
  { name: 'Antigua and Barbuda', key: 'AG', continent: 'North America', status: 1 },
  { name: 'Argentina', key: 'AR', continent: 'South America', status: 1 },
  { name: 'Armenia', key: 'AM', continent: 'Asia', status: 1 },
  { name: 'Aruba', key: 'AW', continent: 'North America', status: 1 },
  { name: 'Australia', key: 'AU', continent: 'Oceania', status: 1 },
  { name: 'Austria', key: 'AT', continent: 'Europe', status: 1 },
  { name: 'Azerbaijan', key: 'AZ', continent: 'Asia', status: 1 },
  { name: 'Bahamas', key: 'BS', continent: 'North America', status: 1 },
  { name: 'Bahrain', key: 'BH', continent: 'Asia', status: 1 },
  { name: 'Bangladesh', key: 'BD', continent: 'Asia', status: 1 },
  { name: 'Barbados', key: 'BB', continent: 'North America', status: 1 },
  { name: 'Belarus', key: 'BY', continent: 'Europe', status: 1 },
  { name: 'Belgium', key: 'BE', continent: 'Europe', status: 1 },
  { name: 'Belize', key: 'BZ', continent: 'North America', status: 1 },
  { name: 'Benin', key: 'BJ', continent: 'Africa', status: 1 },
  { name: 'Bermuda', key: 'BM', continent: 'North America', status: 1 },
  { name: 'Bhutan', key: 'BT', continent: 'Asia', status: 1 },
  { name: 'Bolivia, Plurinational State of', key: 'BO', continent: 'South America', status: 1 },
  { name: 'Bonaire, Sint Eustatius and Saba', key: 'BQ', continent: 'North America', status: 1 },
  { name: 'Bosnia and Herzegovina', key: 'BA', continent: 'Europe', status: 1 },
  { name: 'Botswana', key: 'BW', continent: 'Africa', status: 1 },
  { name: 'Bouvet Island', key: 'BV', continent: 'Antarctica', status: 1 },
  { name: 'Brazil', key: 'BR', continent: 'South America', status: 1 },
  { name: 'British Indian Ocean Territory', key: 'IO', continent: 'Asia', status: 1 },
  { name: 'Brunei Darussalam', key: 'BN', continent: 'Asia', status: 1 },
  { name: 'Bulgaria', key: 'BG', continent: 'Europe', status: 1 },
  { name: 'Burkina Faso', key: 'BF', continent: 'Africa', status: 1 },
  { name: 'Burundi', key: 'BI', continent: 'Africa', status: 1 },
  { name: 'Cambodia', key: 'KH', continent: 'Asia', status: 1 },
  { name: 'Cameroon', key: 'CM', continent: 'Africa', status: 1 },
  { name: 'Canada', key: 'CA', continent: 'North America', status: 1 },
  { name: 'Cape Verde', key: 'CV', continent: 'Africa', status: 1 },
  { name: 'Cayman Islands', key: 'KY', continent: 'North America', status: 1 },
  { name: 'Central African Republic', key: 'CF', continent: 'Africa', status: 1 },
  { name: 'Chad', key: 'TD', continent: 'Africa', status: 1 },
  { name: 'Chile', key: 'CL', continent: 'South America', status: 1 },
  { name: 'China', key: 'CN', continent: 'Asia', status: 1 },
  { name: 'Christmas Island', key: 'CX', continent: 'Asia', status: 1 },
  { name: 'Cocos (Keeling) Islands', key: 'CC', continent: 'Asia', status: 1 },
  { name: 'Colombia', key: 'CO', continent: 'South America', status: 1 },
  { name: 'Comoros', key: 'KM', continent: 'Africa', status: 1 },
  { name: 'Congo', key: 'CG', continent: 'Africa', status: 1 },
  { name: 'Congo, the Democratic Republic of the', key: 'CD', continent: 'Africa', status: 1 },
  { name: 'Cook Islands', key: 'CK', continent: 'Oceania', status: 1 },
  { name: 'Costa Rica', key: 'CR', continent: 'North America', status: 1 },
  { name: 'Croatia', key: 'HR', continent: 'Europe', status: 1 },
  { name: 'Cuba', key: 'CU', continent: 'North America', status: 1 },
  { name: 'Curaçao', key: 'CW', continent: 'North America', status: 1 },
  { name: 'Cyprus', key: 'CY', continent: 'Asia', status: 1 },
  { name: 'Czech Republic', key: 'CZ', continent: 'Europe', status: 1 },
  { name: "Côte d'Ivoire", key: 'CI', continent: 'Africa', status: 1 },
  { name: 'Denmark', key: 'DK', continent: 'Europe', status: 1 },
  { name: 'Djibouti', key: 'DJ', continent: 'Africa', status: 1 },
  { name: 'Dominica', key: 'DM', continent: 'North America', status: 1 },
  { name: 'Dominican Republic', key: 'DO', continent: 'North America', status: 1 },
  { name: 'Ecuador', key: 'EC', continent: 'South America', status: 1 },
  { name: 'Egypt', key: 'EG', continent: 'Africa', status: 1 },
  { name: 'El Salvador', key: 'SV', continent: 'North America', status: 1 },
  { name: 'Equatorial Guinea', key: 'GQ', continent: 'Africa', status: 1 },
  { name: 'Eritrea', key: 'ER', continent: 'Africa', status: 1 },
  { name: 'Estonia', key: 'EE', continent: 'Europe', status: 1 },
  { name: 'Ethiopia', key: 'ET', continent: 'Africa', status: 1 },
  { name: 'Falkland Islands (Malvinas)', key: 'FK', continent: 'South America', status: 1 },
  { name: 'Faroe Islands', key: 'FO', continent: 'Europe', status: 1 },
  { name: 'Fiji', key: 'FJ', continent: 'Oceania', status: 1 },
  { name: 'Finland', key: 'FI', continent: 'Europe', status: 1 },
  { name: 'France', key: 'FR', continent: 'Europe', status: 1 },
  { name: 'French Guiana', key: 'GF', continent: 'South America', status: 1 },
  { name: 'French Polynesia', key: 'PF', continent: 'Oceania', status: 1 },
  { name: 'French Southern Territories', key: 'TF', continent: 'Antarctica', status: 1 },
  { name: 'Gabon', key: 'GA', continent: 'Africa', status: 1 },
  { name: 'Gambia', key: 'GM', continent: 'Africa', status: 1 },
  { name: 'Georgia', key: 'GE', continent: 'Asia', status: 1 },
  { name: 'Germany', key: 'DE', continent: 'Europe', status: 1 },
  { name: 'Ghana', key: 'GH', continent: 'Africa', status: 1 },
  { name: 'Gibraltar', key: 'GI', continent: 'Europe', status: 1 },
  { name: 'Greece', key: 'GR', continent: 'Europe', status: 1 },
  { name: 'Greenland', key: 'GL', continent: 'North America', status: 1 },
  { name: 'Grenada', key: 'GD', continent: 'North America', status: 1 },
  { name: 'Guadeloupe', key: 'GP', continent: 'North America', status: 1 },
  { name: 'Guam', key: 'GU', continent: 'Oceania', status: 1 },
  { name: 'Guatemala', key: 'GT', continent: 'North America', status: 1 },
  { name: 'Guernsey', key: 'GG', continent: 'Europe', status: 1 },
  { name: 'Guinea', key: 'GN', continent: 'Africa', status: 1 },
  { name: 'Guinea-Bissau', key: 'GW', continent: 'Africa', status: 1 },
  { name: 'Guyana', key: 'GY', continent: 'South America', status: 1 },
  { name: 'Haiti', key: 'HT', continent: 'North America', status: 1 },
  { name: 'Heard Island and McDonald Islands', key: 'HM', continent: 'Antarctica', status: 1 },
  { name: 'Holy See (Vatican City State)', key: 'VA', continent: 'Europe', status: 1 },
  { name: 'Honduras', key: 'HN', continent: 'North America', status: 1 },
  { name: 'Hong Kong', key: 'HK', continent: 'Asia', status: 1 },
  { name: 'Hungary', key: 'HU', continent: 'Europe', status: 1 },
  { name: 'Iceland', key: 'IS', continent: 'Europe', status: 1 },
  { name: 'India', key: 'IN', continent: 'Asia', status: 1 },
  { name: 'Indonesia', key: 'ID', continent: 'Asia', status: 1 },
  { name: 'Iran, Islamic Republic of', key: 'IR', continent: 'Asia', status: 1 },
  { name: 'Iraq', key: 'IQ', continent: 'Asia', status: 1 },
  { name: 'Ireland', key: 'IE', continent: 'Europe', status: 1 },
  { name: 'Isle of Man', key: 'IM', continent: 'Europe', status: 1 },
  { name: 'Israel', key: 'IL', continent: 'Asia', status: 1 },
  { name: 'Italy', key: 'IT', continent: 'Europe', status: 1 },
  { name: 'Jamaica', key: 'JM', continent: 'North America', status: 1 },
  { name: 'Japan', key: 'JP', continent: 'Asia', status: 1 },
  { name: 'Jersey', key: 'JE', continent: 'Europe', status: 1 },
  { name: 'Jordan', key: 'JO', continent: 'Asia', status: 1 },
  { name: 'Kazakhstan', key: 'KZ', continent: 'Asia', status: 1 },
  { name: 'Kenya', key: 'KE', continent: 'Africa', status: 1 },
  { name: 'Kiribati', key: 'KI', continent: 'Oceania', status: 1 },
  { name: "Korea, Democratic People's Republic of", key: 'KP', continent: 'Asia', status: 1 },
  { name: 'Korea, Republic of', key: 'KR', continent: 'Asia', status: 1 },
  { name: 'Kuwait', key: 'KW', continent: 'Asia', status: 1 },
  { name: 'Kyrgyzstan', key: 'KG', continent: 'Asia', status: 1 },
  { name: "Lao People's Democratic Republic", key: 'LA', continent: 'Asia', status: 1 },
  { name: 'Latvia', key: 'LV', continent: 'Europe', status: 1 },
  { name: 'Lebanon', key: 'LB', continent: 'Asia', status: 1 },
  { name: 'Lesotho', key: 'LS', continent: 'Africa', status: 1 },
  { name: 'Liberia', key: 'LR', continent: 'Africa', status: 1 },
  { name: 'Libya', key: 'LY', continent: 'Africa', status: 1 },
  { name: 'Liechtenstein', key: 'LI', continent: 'Europe', status: 1 },
  { name: 'Lithuania', key: 'LT', continent: 'Europe', status: 1 },
  { name: 'Luxembourg', key: 'LU', continent: 'Europe', status: 1 },
  { name: 'Macao', key: 'MO', continent: 'Asia', status: 1 },
  { name: 'Macedonia, the Former Yugoslav Republic of', key: 'MK', continent: 'Europe', status: 1 },
  { name: 'Madagascar', key: 'MG', continent: 'Africa', status: 1 },
  { name: 'Malawi', key: 'MW', continent: 'Africa', status: 1 },
  { name: 'Malaysia', key: 'MY', continent: 'Asia', status: 1 },
  { name: 'Maldives', key: 'MV', continent: 'Asia', status: 1 },
  { name: 'Mali', key: 'ML', continent: 'Africa', status: 1 },
  { name: 'Malta', key: 'MT', continent: 'Europe', status: 1 },
  { name: 'Marshall Islands', key: 'MH', continent: 'Oceania', status: 1 },
  { name: 'Martinique', key: 'MQ', continent: 'North America', status: 1 },
  { name: 'Mauritania', key: 'MR', continent: 'Africa', status: 1 },
  { name: 'Mauritius', key: 'MU', continent: 'Africa', status: 1 },
  { name: 'Mayotte', key: 'YT', continent: 'Africa', status: 1 },
  { name: 'Mexico', key: 'MX', continent: 'North America', status: 1 },
  { name: 'Micronesia, Federated States of', key: 'FM', continent: 'Oceania', status: 1 },
  { name: 'Moldova, Republic of', key: 'MD', continent: 'Europe', status: 1 },
  { name: 'Monaco', key: 'MC', continent: 'Europe', status: 1 },
  { name: 'Mongolia', key: 'MN', continent: 'Asia', status: 1 },
  { name: 'Montenegro', key: 'ME', continent: 'Europe', status: 1 },
  { name: 'Montserrat', key: 'MS', continent: 'North America', status: 1 },
  { name: 'Morocco', key: 'MA', continent: 'Africa', status: 1 },
  { name: 'Mozambique', key: 'MZ', continent: 'Africa', status: 1 },
  { name: 'Myanmar', key: 'MM', continent: 'Asia', status: 1 },
  { name: 'Namibia', key: 'NA', continent: 'Africa', status: 1 },
  { name: 'Nauru', key: 'NR', continent: 'Oceania', status: 1 },
  { name: 'Nepal', key: 'NP', continent: 'Asia', status: 1 },
  { name: 'Netherlands', key: 'NL', continent: 'Europe', status: 1 },
  { name: 'New Caledonia', key: 'NC', continent: 'Oceania', status: 1 },
  { name: 'New Zealand', key: 'NZ', continent: 'Oceania', status: 1 },
  { name: 'Nicaragua', key: 'NI', continent: 'North America', status: 1 },
  { name: 'Niger', key: 'NE', continent: 'Africa', status: 1 },
  { name: 'Nigeria', key: 'NG', continent: 'Africa', status: 1 },
  { name: 'Niue', key: 'NU', continent: 'Oceania', status: 1 },
  { name: 'Norfolk Island', key: 'NF', continent: 'Oceania', status: 1 },
  { name: 'Northern Mariana Islands', key: 'MP', continent: 'Oceania', status: 1 },
  { name: 'Norway', key: 'NO', continent: 'Europe', status: 1 },
  { name: 'Oman', key: 'OM', continent: 'Asia', status: 1 },
  { name: 'Pakistan', key: 'PK', continent: 'Asia', status: 1 },
  { name: 'Palau', key: 'PW', continent: 'Oceania', status: 1 },
  { name: 'Palestine, State of', key: 'PS', continent: 'Asia', status: 1 },
  { name: 'Panama', key: 'PA', continent: 'North America', status: 1 },
  { name: 'Papua New Guinea', key: 'PG', continent: 'Oceania', status: 1 },
  { name: 'Paraguay', key: 'PY', continent: 'South America', status: 1 },
  { name: 'Peru', key: 'PE', continent: 'South America', status: 1 },
  { name: 'Philippines', key: 'PH', continent: 'Asia', status: 1 },
  { name: 'Pitcairn', key: 'PN', continent: 'Oceania', status: 1 },
  { name: 'Poland', key: 'PL', continent: 'Europe', status: 1 },
  { name: 'Portugal', key: 'PT', continent: 'Europe', status: 1 },
  { name: 'Puerto Rico', key: 'PR', continent: 'North America', status: 1 },
  { name: 'Qatar', key: 'QA', continent: 'Asia', status: 1 },
  { name: 'Romania', key: 'RO', continent: 'Europe', status: 1 },
  { name: 'Russian Federation', key: 'RU', continent: 'Europe', status: 1 },
  { name: 'Rwanda', key: 'RW', continent: 'Africa', status: 1 },
  { name: 'Réunion', key: 'RE', continent: 'Africa', status: 1 },
  { name: 'Saint Barthélemy', key: 'BL', continent: 'North America', status: 1 },
  {
    name: 'Saint Helena, Ascension and Tristan da Cunha',
    key: 'SH',
    continent: 'Africa',
    status: 1,
  },
  { name: 'Saint Kitts and Nevis', key: 'KN', continent: 'North America', status: 1 },
  { name: 'Saint Lucia', key: 'LC', continent: 'North America', status: 1 },
  { name: 'Saint Martin (French part)', key: 'MF', continent: 'North America', status: 1 },
  { name: 'Saint Pierre and Miquelon', key: 'PM', continent: 'North America', status: 1 },
  { name: 'Saint Vincent and the Grenadines', key: 'VC', continent: 'North America', status: 1 },
  { name: 'Samoa', key: 'WS', continent: 'Oceania', status: 1 },
  { name: 'San Marino', key: 'SM', continent: 'Europe', status: 1 },
  { name: 'Sao Tome and Principe', key: 'ST', continent: 'Africa', status: 1 },
  { name: 'Saudi Arabia', key: 'SA', continent: 'Asia', status: 1 },
  { name: 'Senegal', key: 'SN', continent: 'Africa', status: 1 },
  { name: 'Serbia', key: 'RS', continent: 'Europe', status: 1 },
  { name: 'Seychelles', key: 'SC', continent: 'Africa', status: 1 },
  { name: 'Sierra Leone', key: 'SL', continent: 'Africa', status: 1 },
  { name: 'Singapore', key: 'SG', continent: 'Asia', status: 1 },
  { name: 'Sint Maarten (Dutch part)', key: 'SX', continent: 'North America', status: 1 },
  { name: 'Slovakia', key: 'SK', continent: 'Europe', status: 1 },
  { name: 'Slovenia', key: 'SI', continent: 'Europe', status: 1 },
  { name: 'Solomon Islands', key: 'SB', continent: 'Oceania', status: 1 },
  { name: 'Somalia', key: 'SO', continent: 'Africa', status: 1 },
  { name: 'South Africa', key: 'ZA', continent: 'Africa', status: 1 },
  {
    name: 'South Georgia and the South Sandwich Islands',
    key: 'GS',
    continent: 'Antarctica',
    status: 1,
  },
  { name: 'South Sudan', key: 'SS', continent: 'Africa', status: 1 },
  { name: 'Spain', key: 'ES', continent: 'Europe', status: 1 },
  { name: 'Sri Lanka', key: 'LK', continent: 'Asia', status: 1 },
  { name: 'Sudan', key: 'SD', continent: 'Africa', status: 1 },
  { name: 'Suriname', key: 'SR', continent: 'South America', status: 1 },
  { name: 'Svalbard and Jan Mayen', key: 'SJ', continent: 'Europe', status: 1 },
  { name: 'Swaziland', key: 'SZ', continent: 'Africa', status: 1 },
  { name: 'Sweden', key: 'SE', continent: 'Europe', status: 1 },
  { name: 'Switzerland', key: 'CH', continent: 'Europe', status: 1 },
  { name: 'Syrian Arab Republic', key: 'SY', continent: 'Asia', status: 1 },
  { name: 'Taiwan, Province of China', key: 'TW', continent: 'Asia', status: 1 },
  { name: 'Tajikistan', key: 'TJ', continent: 'Asia', status: 1 },
  { name: 'Tanzania, United Republic of', key: 'TZ', continent: 'Africa', status: 1 },
  { name: 'Thailand', key: 'TH', continent: 'Asia', status: 1 },
  { name: 'Timor-Leste', key: 'TL', continent: 'Asia', status: 1 },
  { name: 'Togo', key: 'TG', continent: 'Africa', status: 1 },
  { name: 'Tokelau', key: 'TK', continent: 'Oceania', status: 1 },
  { name: 'Tonga', key: 'TO', continent: 'Oceania', status: 1 },
  { name: 'Trinidad and Tobago', key: 'TT', continent: 'North America', status: 1 },
  { name: 'Tunisia', key: 'TN', continent: 'Africa', status: 1 },
  { name: 'Turkey', key: 'TR', continent: 'Asia', status: 1 },
  { name: 'Turkmenistan', key: 'TM', continent: 'Asia', status: 1 },
  { name: 'Turks and Caicos Islands', key: 'TC', continent: 'North America', status: 1 },
  { name: 'Tuvalu', key: 'TV', continent: 'Oceania', status: 1 },
  { name: 'Uganda', key: 'UG', continent: 'Africa', status: 1 },
  { name: 'Ukraine', key: 'UA', continent: 'Europe', status: 1 },
  { name: 'United Arab Emirates', key: 'AE', continent: 'Asia', status: 1 },
  { name: 'United Kingdom', key: 'GB', continent: 'Europe', status: 1 },
  { name: 'United States', key: 'US', continent: 'North America', status: 1 },
  { name: 'United States Minor Outlying Islands', key: 'UM', continent: 'Oceania', status: 1 },
  { name: 'Uruguay', key: 'UY', continent: 'South America', status: 1 },
  { name: 'Uzbekistan', key: 'UZ', continent: 'Asia', status: 1 },
  { name: 'Vanuatu', key: 'VU', continent: 'Oceania', status: 1 },
  { name: 'Venezuela, Bolivarian Republic of', key: 'VE', continent: 'South America', status: 1 },
  { name: 'Viet Nam', key: 'VN', continent: 'Asia', status: 1 },
  { name: 'Virgin Islands, British', key: 'VG', continent: 'North America', status: 1 },
  { name: 'Virgin Islands, U.S.', key: 'VI', continent: 'North America', status: 1 },
  { name: 'Wallis and Futuna', key: 'WF', continent: 'Oceania', status: 1 },
  { name: 'Western Sahara', key: 'EH', continent: 'Africa', status: 1 },
  { name: 'Yemen', key: 'YE', continent: 'Asia', status: 1 },
  { name: 'Zambia', key: 'ZM', continent: 'Africa', status: 1 },
  { name: 'Zimbabwe', key: 'ZW', continent: 'Africa', status: 1 },
  { name: 'Åland Islands', key: 'AX', continent: 'Europe', status: 1 },
];

export async function up(knex: Knex): Promise<void> {
  const countriesToInsert = [];

  for (const country of countriesData) {
    const { name, key, continent, status } = country;

    const continentRecord = await knex('continents')
      .select('id')
      .where({ name: continent })
      .first();

    if (continentRecord) {
      countriesToInsert.push({
        name,
        key,
        continent_id: continentRecord.id,
        status,
      });
    } else {
      console.error(`continent not found for, status: 1: ${continent}`);
    }
  }

  if (countriesToInsert.length > 0) {
    await knex('countries').insert(countriesToInsert);
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('countries').del();
}
