import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('post_countries', (t) => {
      t.primary(['post_id', 'country_id']);
      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_countries_on_post_id');
      t.uuid('country_id')
        .notNullable()
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_post_countries_on_country_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER post_countries_updated_at BEFORE UPDATE
ON post_countries FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('post_countries');
}
