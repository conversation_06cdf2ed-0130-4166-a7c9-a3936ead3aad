import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prestige_membership_applications', (t) => {
    t.renameColumn('wants_subsidiary_accounts', 'allow_subsidiaries');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prestige_membership_applications', (t) => {
    t.renameColumn('allow_subsidiaries', 'wants_subsidiary_accounts');
  });
}
