import type { Knex } from 'knex';

interface SeedItem {
  name: string;
  user_segment: 'CARDIAC_SPECIALIST' | 'ALLIED_CARDIAC' | 'STUDENT' | 'ORGANISATION';
  children: SeedItem[];
}

const seedDatas: SeedItem[] = [
  {
    name: 'Industry',
    user_segment: 'ORGANISATION',
    children: [
      {
        name: 'Manufacturers/suppliers of medical devices',
        user_segment: 'ORGANISATION',
        children: [],
      },
      {
        name: 'Manufacturers/suppliers of consumables',
        user_segment: 'ORGANISATION',
        children: [],
      },
      {
        name: 'Medical Technologies',
        user_segment: 'ORGANISATION',
        children: [],
      },
      {
        name: 'Pharmaceutical',
        user_segment: 'ORGANISATION',
        children: [],
      },
      {
        name: 'Other',
        user_segment: 'ORGANISATION',
        children: [],
      },
    ],
  },
  {
    name: 'Hospitals, Heart Centres, & Clinics',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Cardiac Surgery & Cardiology Societies',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Health Insurance',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Research Organisations',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Charities & Non-profits',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Others',
    user_segment: 'ORGANISATION',
    children: [],
  },
  {
    name: 'Physician/Surgeon',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Perfusionist',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Surgical Practitioner',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Nurse',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Cardiovascular scientist',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Researcher',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
  {
    name: 'Other',
    user_segment: 'ALLIED_CARDIAC',
    children: [],
  },
];

export async function up(knex: Knex): Promise<void> {
  // Recursive function to insert the categories and their children
  async function insertCategories(categories: SeedItem[], parent_id = null) {
    for (const category of categories) {
      // Insert the current category
      const { children, ...rest } = category;
      const [{ id: subtypeId }] = await knex('segment_categories')
        .insert({
          parent_id, // parent_id will be null for top-level categories
          ...rest,
          status: 1,
        })
        .returning('id'); // Capture the inserted ID for recursion

      // If the category has children, recursively insert them
      if (children && children.length > 0) {
        await insertCategories(children, subtypeId);
      }
    }
  }

  await insertCategories(seedDatas);
}

export async function down(knex: Knex): Promise<void> {
  // Get the top-level category names from our seed data
  const topLevelCategoryNames = seedDatas.map((item) => item.name);

  // First find the IDs of the top-level categories we added
  const topLevelCategories = await knex('segment_categories')
    .whereIn('name', topLevelCategoryNames)
    .where('parent_id', null)
    .select('id');

  const topLevelIds = topLevelCategories.map((cat) => cat.id);

  // Delete all subcategories (children) of our top-level categories
  if (topLevelIds.length > 0) {
    await knex('segment_categories').whereIn('parent_id', topLevelIds).del();

    // Then delete the top-level categories themselves
    await knex('segment_categories').whereIn('id', topLevelIds).del();
  }
}
