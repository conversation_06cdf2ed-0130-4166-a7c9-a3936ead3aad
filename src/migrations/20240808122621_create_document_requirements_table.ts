import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('document_requirements', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('user_segment').notNullable();
      t.uuid('document_type_id')
        .references('id')
        .inTable('document_types')
        .onDelete('CASCADE')
        .notNullable()
        .index('index_document_requirements_on_document_type_id');
      t.boolean('is_required').notNullable();
      t.integer('sort_order').notNullable();
      t.integer('max_count').notNullable();
      t.text('instructions');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER document_requirements_updated_at BEFORE UPDATE
ON document_requirements FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('document_requirements');
}
