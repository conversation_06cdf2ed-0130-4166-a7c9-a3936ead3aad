import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspace_documents', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_documents_on_workspace_id');
      t.uuid('document_type_id')
        .notNullable()
        .references('id')
        .inTable('document_types')
        .onDelete('CASCADE')
        .index('index_workspace_documents_on_document_type_id');
      t.text('file_path').notNullable();
      t.text('file_name').notNullable();
      t.integer('file_size').notNullable();
      t.integer('sort_order').notNullable();
      t.text('mime_type').notNullable();
      t.smallint('verification_status').notNullable();
      t.uuid('verified_by_id')
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_workspace_documents_on_verified_by_id');
      t.date('verified_date');
      t.text('verification_notes');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

      t.unique(['document_type_id', 'sort_order']);
    })
    .raw(
      `
CREATE TRIGGER workspace_documents_updated_at BEFORE UPDATE
ON workspace_documents FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspace_documents');
}
