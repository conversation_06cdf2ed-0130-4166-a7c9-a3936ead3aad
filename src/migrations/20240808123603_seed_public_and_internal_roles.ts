import { K<PERSON> } from 'knex';

import { Role, RoleLevel } from '@/constants/roles';

const roles = [
  { name: 'Owner', key: Role.OWNER, level: RoleLevel.INTERNAL, status: 1 },
  { name: 'System Administrator', key: Role.SYSTEM_ADMIN, level: RoleLevel.INTERNAL, status: 1 },
  { name: 'Super Administrator', key: Role.SUPER_ADMIN, level: RoleLevel.INTERNAL, status: 1 },

  { name: 'Patient', key: 'PATIENT', level: RoleLevel.PUBLIC, status: 1 },
  { name: 'Organisation', key: Role.ORGANISATION, level: RoleLevel.PUBLIC, status: 1 },
  {
    name: 'Cardiac Specialist',
    key: Role.CARDIAC_SPECIALIST,
    level: RoleLevel.PUBLIC,
    status: 1,
  },
  { name: 'Allied Cardiac', key: Role.ALLIED_CARDIAC, level: RoleLevel.PUBLIC, status: 1 },
  { name: 'Student', key: Role.STUDENT, level: RoleLevel.PUBLIC, status: 1 },
];

export async function up(knex: Knex): Promise<void> {
  await knex('roles').insert(roles);
}

export async function down(knex: Knex): Promise<void> {
  await knex('roles')
    .whereIn(
      'key',
      roles.map((role) => role.key),
    )
    .del();
}
