import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.raw('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"').raw(
    `
  CREATE OR REPLACE FUNCTION update_updated_at_column()
  RETURNS TRIGGER AS $$
  BEGIN
   NEW."updated_at" = now(); 
   RETURN NEW;
  END;
  $$ language 'plpgsql';
`,
  );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.raw('DROP FUNCTION update_updated_at_column;');
}
