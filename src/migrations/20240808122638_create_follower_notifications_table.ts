import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('follower_notifications', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));

    t.uuid('entity_id').notNullable().index('index_follower_notifications_on_entity_id');
    t.text('entity_type').notNullable();

    t.uuid('followed_id').notNullable().index('index_follower_notifications_on_followed_id');

    t.boolean('is_read').notNullable().defaultTo(false);

    t.integer('status').notNullable();
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('follower_notifications');
}
