import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('article_posts', (t) => {
      t.uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_article_posts_on_post_id');
      t.text('title').notNullable();
      t.text('summary').notNullable();
      t.text('content').notNullable(); // Rich text content
      t.text('cover_image_path'); // Optional cover image
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER article_posts_updated_at BEFORE UPDATE
ON article_posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('article_posts');
}
