import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('media_posts', (t) => {
      t.uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_media_posts_on_post_id');
      t.text('caption');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER media_posts_updated_at BEFORE UPDATE
ON media_posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('media_posts');
}
