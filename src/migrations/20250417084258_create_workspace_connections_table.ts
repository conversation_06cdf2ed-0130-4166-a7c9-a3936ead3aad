import type { K<PERSON> } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('workspace_connections', (t) => {
      t.primary(['requestor_id', 'recipient_id']);
      t.uuid('requestor_id')
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_workspace_connections_on_requestor_id')
        .notNullable();
      t.uuid('recipient_id')
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_workspace_connections_on_recipient_id')
        .notNullable();
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspace_connections_updated_at BEFORE UPDATE 
ON workspace_connections FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('workspace_connections');
}
