import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subscription_plans', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('title').notNullable();
      t.text('subtitle').notNullable();
      t.text('description');
      t.text('user_segment').notNullable();
      t.text('asset_uri');
      t.integer('price_monthly').notNullable();
      t.integer('price_yearly').notNullable();
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subscription_plans_updated_at BEFORE UPDATE
ON subscription_plans FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subscription_plans');
}
