import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('professionals', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('workspace_id')
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_professionals_on_workspace_id')
        .notNullable();
      t.text('title').notNullable();
      t.text('qualifications').notNullable();
      t.uuid('segment_category_id')
        .notNullable()
        .references('id')
        .inTable('segment_categories')
        .onDelete('CASCADE')
        .index('index_professionals_on_segment_category_id');
      t.text('designation').notNullable();
      t.uuid('employer_id')
        .references('id')
        .inTable('employers')
        .onDelete('CASCADE')
        .index('index_professionals_on_employer_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER professionals_updated_at BEFORE UPDATE
ON professionals FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('professionals');
}
