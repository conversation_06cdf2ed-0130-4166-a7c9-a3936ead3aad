import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.dropForeign(['parent_organisation_id']);

    t.foreign('parent_organisation_id').references('id').inTable('employers').onDelete('CASCADE');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.dropForeign(['parent_organisation_id']);

    t.foreign('parent_organisation_id')
      .references('id')
      .inTable('organisations')
      .onDelete('CASCADE');
  });
}
