import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspaces', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('user_segment').notNullable();
      t.uuid('created_by_id')
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_workspaces_on_created_by_id')
        .notNullable();
      t.text('primary_speciality').notNullable(); // enum CARDIAC_SURGEON, CARDIOLOGISTS, BOTH
      t.text('instagram_link');
      t.text('facebook_link');
      t.text('twitter_link');
      t.text('linkedin_link');
      t.text('youtube_link');
      t.text('tiktok_link');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspaces_updated_at BEFORE UPDATE
ON workspaces FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspaces');
}
