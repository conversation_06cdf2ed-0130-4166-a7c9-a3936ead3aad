import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  const moduleIds = await knex('modules')
    .insert([
      { name: 'Users', status: 1 },
      { name: 'Organisations', status: 1 },
      { name: 'Roles', status: 1 },
    ])
    .returning('id');

  const [usersModule, organisationsModule, rolesModule] = moduleIds.map((row) => row.id);

  const permissionsData = [
    // Users module permissions
    { name: 'View Users', key: 'users:view', status: 1, module_id: usersModule },
    { name: 'Create User', key: 'users:create', status: 1, module_id: usersModule },
    { name: 'Update User', key: 'users:update', status: 1, module_id: usersModule },
    { name: 'Delete User', key: 'users:delete', status: 1, module_id: usersModule },

    // Organisations module permissions
    {
      name: 'View Organisations',
      key: 'organisations:view',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Create Organisation',
      key: 'organisations:create',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Update Organisation',
      key: 'organisations:update',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Delete Organisation',
      key: 'organisations:delete',
      status: 1,
      module_id: organisationsModule,
    },

    // Organisation Users permissions
    {
      name: 'View Organisation Users',
      key: 'organisations:users:view',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Create Organisation User',
      key: 'organisations:users:create',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Update Organisation User',
      key: 'organisations:users:update',
      status: 1,
      module_id: organisationsModule,
    },
    {
      name: 'Delete Organisation User',
      key: 'organisations:users:delete',
      status: 1,
      module_id: organisationsModule,
    },

    // Roles module permissions
    { name: 'View Roles', key: 'roles:view', status: 1, module_id: rolesModule },
    { name: 'Create Role', key: 'roles:create', status: 1, module_id: rolesModule },
    { name: 'Update Role', key: 'roles:update', status: 1, module_id: rolesModule },
    { name: 'Delete Role', key: 'roles:delete', status: 1, module_id: rolesModule },
    { name: 'Assign Role', key: 'roles:assign', status: 1, module_id: rolesModule },
  ];

  await knex('permissions').insert(permissionsData);
}

export async function down(knex: Knex): Promise<void> {
  await knex('permissions').del();
  await knex('modules').del();
}
