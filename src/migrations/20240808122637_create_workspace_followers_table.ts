import { EntityType } from '@/constants/user-types';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspace_followers', (t) => {
      t.primary(['entity_id', 'workspace_id']);
      t.text('entity_type').notNullable().defaultTo(EntityType.USER);
      t.uuid('entity_id').notNullable().index('index_workspace_followers_on_entity_id');
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_followers_on_workspace_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspace_followers_updated_at BEFORE UPDATE
ON workspace_followers FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspace_followers');
}
