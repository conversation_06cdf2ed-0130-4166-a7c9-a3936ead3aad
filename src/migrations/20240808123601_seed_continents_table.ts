import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex('continents').insert([
    { name: 'Africa', status: 1 },
    { name: 'Antarctica', status: 1 },
    { name: 'Asia', status: 1 },
    { name: 'Europe', status: 1 },
    { name: 'North America', status: 1 },
    { name: 'Oceania', status: 1 },
    { name: 'South America', status: 1 },
  ]);
}

export async function down(knex: Knex): Promise<void> {
  await knex('continents').del();
}
