import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('users', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('display_name').notNullable();
      t.text('username').nullable().unique();
      t.text('introductory_statement');
      t.text('email').unique().notNullable();
      t.text('provider_id').unique().notNullable();
      t.text('current_stage').notNullable(); // onboarding:profile-setup
      t.text('account_type').notNullable(); // enum PROFESSONAL, ORGANISATION, PUBLIC, SYSTEM_ADMIN, SUPER_ADMIN, OWNER
      t.text('profile_image_url').unique();
      t.text('profile_image_url_thumbnail').unique();
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER users_updated_at BEFORE UPDATE
ON users FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('users');
}
