import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_followers', (t) => {
    t.dropForeign(['workspace_id']);
    t.foreign('workspace_id').references('id').inTable('users').onDelete('CASCADE');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('workspace_followers', (t) => {
    t.dropForeign(['workspace_id']);
    t.foreign('workspace_id').references('id').inTable('workspaces').onDelete('CASCADE');
  });
}
