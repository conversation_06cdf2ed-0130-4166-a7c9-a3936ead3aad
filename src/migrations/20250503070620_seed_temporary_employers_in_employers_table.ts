import type { Knex } from 'knex';
import envConfiguration from '@/config/configuration';

function generateRandomPrefix(length = 4): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
}

export async function up(knex: Knex): Promise<void> {
  const envConfig = envConfiguration();

  // preventing running this
  if (envConfig.environment.isProduction) {
    return;
  }

  const temporaryEmployers = Array.from({ length: 25 }, (_, i) => ({
    name: `${generateRandomPrefix()} TemporaryCardiac Employer ${i + 1}`,
    url: `https://sample-employer-${i + 1}.com`,
    status: 1,
  }));

  await knex('employers').insert(temporaryEmployers);
}

export async function down(knex: Knex): Promise<void> {
  await knex('employers').where('name', 'like', '%TemporaryCardiac%').del();
}
