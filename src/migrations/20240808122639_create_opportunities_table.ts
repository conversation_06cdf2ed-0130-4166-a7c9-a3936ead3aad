import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('opportunities', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
    t.uuid('workspace_id')
      .notNullable()
      .references('id')
      .inTable('workspaces')
      .onDelete('CASCADE')
      .index('index_opportunities_on_workspace_id');
    t.uuid('published_by_id')
      .notNullable()
      .references('id')
      .inTable('users')
      .onDelete('CASCADE')
      .index('index_opportunities_on_published_by_id');
    t.text('type').notNullable();
    t.text('title').notNullable();
    t.boolean('share_to_feed').notNullable();
    t.text('description').notNullable();
    t.text('short_description').notNullable();
    t.text('contact_person_name').notNullable();
    t.text('email').notNullable();
    t.text('phone').notNullable();
    t.text('website_link').notNullable();
    t.timestamp('expiry_date').notNullable();
    t.integer('status').notNullable();
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
  }).raw(`
    CREATE TRIGGER opportunities_updated_at BEFORE UPDATE
    ON opportunities FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
  `);
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('opportunities');
}
