import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspace_subscriptions', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_subscriptions_on_workspace_id');
      t.uuid('subscription_plan_id')
        .notNullable()
        .references('id')
        .inTable('subscription_plans')
        .onDelete('CASCADE')
        .index('index_workspace_subscriptions_on_subscription_plan_id');
      t.boolean('is_yearly').notNullable();
      t.date('start_date').notNullable();
      t.date('end_date').notNullable();
      t.date('payment_date').notNullable();
      t.text('payment_method').notNullable();
      t.text('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspace_subscriptions_updated_at BEFORE UPDATE
ON workspace_subscriptions FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspace_subscriptions');
}
