import type { Knex } from 'knex';
import { UserSegment } from '@/constants/user-segments';

export async function up(knex: Knex): Promise<void> {
  // Define document types
  const documentTypes = [
    // Common document types
    { name: 'Photo ID', description: 'Government-issued photo identification', status: 1 },
    { name: 'Primary Degree', description: 'Primary medical or professional degree', status: 1 },
    { name: 'Academic Degree', description: 'Additional academic qualifications', status: 1 },
    {
      name: 'Professional Certification',
      description: 'Professional certifications and licenses',
      status: 1,
    },

    // Student-specific document types
    { name: 'Student ID', description: 'Valid student identification card', status: 1 },
    {
      name: 'Bonafide Certificate',
      description: 'Certificate from educational institution',
      status: 1,
    },
  ];

  // Insert document types and get their IDs
  const insertedDocumentTypes = await knex('document_types')
    .insert(documentTypes)
    .returning(['id', 'name']);

  // Create a map of document type names to their IDs for easier reference
  const documentTypeMap: Record<string, string> = {};
  insertedDocumentTypes.forEach((type) => {
    documentTypeMap[type.name] = type.id;
  });

  // Define document requirements for each user segment
  const documentRequirements = [
    // CARDIAC_SPECIALIST requirements
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your primary medical degree certificate',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 3,
      instructions: 'Upload any additional academic qualifications (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.CARDIAC_SPECIALIST,
      document_type_id: documentTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload your professional certifications or licenses',
      status: 1,
    },

    // ALLIED_CARDIAC requirements
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your primary professional degree certificate',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 3,
      instructions: 'Upload any additional academic qualifications (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.ALLIED_CARDIAC,
      document_type_id: documentTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload your professional certifications or licenses',
      status: 1,
    },

    // ORGANISATION requirements
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: 'Upload a valid government-issued photo ID of the authorized representative',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Primary Degree'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload organization registration certificate or equivalent document',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Academic Degree'],
      is_required: false,
      sort_order: 3,
      max_count: 2,
      instructions: 'Upload any additional organizational credentials (optional)',
      status: 1,
    },
    {
      user_segment: UserSegment.ORGANISATION,
      document_type_id: documentTypeMap['Professional Certification'],
      is_required: true,
      sort_order: 4,
      max_count: 3,
      instructions: 'Upload organizational certifications or accreditations',
      status: 1,
    },

    // STUDENT requirements
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Photo ID'],
      is_required: true,
      sort_order: 1,
      max_count: 1,
      instructions: "Upload a valid government-issued photo ID (passport, driver's license, etc.)",
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Student ID'],
      is_required: true,
      sort_order: 2,
      max_count: 1,
      instructions: 'Upload your current student ID card',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Bonafide Certificate'],
      is_required: true,
      sort_order: 3,
      max_count: 1,
      instructions: 'Upload a bonafide certificate from your educational institution',
      status: 1,
    },
    {
      user_segment: UserSegment.STUDENT,
      document_type_id: documentTypeMap['Primary Degree'],
      is_required: false,
      sort_order: 4,
      max_count: 1,
      instructions: 'Upload any previous degree certificates (optional)',
      status: 1,
    },
  ];

  // Insert document requirements
  await knex('document_requirements').insert(documentRequirements);
}

export async function down(knex: Knex): Promise<void> {
  // Delete all document requirements
  await knex('document_requirements').del();

  // Delete all document types
  await knex('document_types').del();
}
