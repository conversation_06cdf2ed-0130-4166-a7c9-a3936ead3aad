import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prestige_membership_applications', (t) => {
    t.boolean('wants_subsidiary_accounts').notNullable().defaultTo(false);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('prestige_membership_applications', (t) => {
    // Drop the added columns
    t.dropColumn('wants_subsidiary_accounts');
  });
}
