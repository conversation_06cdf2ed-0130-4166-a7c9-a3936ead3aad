import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('segment_categories', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('name').notNullable();
      t.uuid('parent_id')
        .references('id')
        .inTable('segment_categories')
        .onDelete('CASCADE')
        .index('index_segment_categories_on_parent_id');
      t.text('user_segment').notNullable(); //  CARDIAC_SPECIALIST, ALLIED_CARDIAC, ORGANISATION etc
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER segment_categories_updated_at BEFORE UPDATE
ON segment_categories FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('segment_categories');
}
