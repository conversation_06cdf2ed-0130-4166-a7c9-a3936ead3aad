import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('organisations', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('workspace_id')
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_organisations_on_workspace_id')
        .notNullable();
      t.uuid('segment_category_id')
        .notNullable()
        .references('id')
        .inTable('segment_categories')
        .onDelete('CASCADE')
        .index('index_organisations_on_segment_category_id');
      t.uuid('parent_organisation_id')
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_organisations_on_parent_organisation_id');
      t.text('location');
      t.text('map_link');
      t.text('organisation_size');
      t.boolean('allow_subsidiaries');
      t.uuid('country_id')
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_organisations_on_country_id');
      t.text('registration_number');
      t.text('website');
      t.text('phone_number');
      t.text('city');
      t.text('zip_code');
      t.text('profile_summary');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER organisations_updated_at BEFORE UPDATE
ON organisations FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('organisations');
}
