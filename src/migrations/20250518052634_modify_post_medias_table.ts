import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('post_medias', (t) => {
    t.text('alt_text').nullable();
    t.smallint('order').notNullable().defaultTo(0);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('post_medias', (t) => {
    // Drop the added columns
    t.dropColumn('alt_text');
    t.dropColumn('order');
  });
}
