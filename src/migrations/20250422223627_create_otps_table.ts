import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Then create the OTP table
  return knex.schema
    .createTable('otps', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('user_id')
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_otps_on_user_id')
        .notNullable();
      t.text('otp').notNullable();
      t.text('type').notNullable();
      t.timestamp('expires_at').notNullable();
      t.boolean('used').notNullable().defaultTo(false);
      t.integer('status').notNullable().defaultTo(1);
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER otps_updated_at BEFORE UPDATE
ON otps FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('otps');
}
