import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspace_users', (t) => {
      t.primary(['user_id', 'workspace_id']);
      t.uuid('user_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_workspace_users_on_user_id');
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_workspace_users_on_workspace_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspace_users_updated_at BEFORE UPDATE
ON workspace_users FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspace_users');
}
