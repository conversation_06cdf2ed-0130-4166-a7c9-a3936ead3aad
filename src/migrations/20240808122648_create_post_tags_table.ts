import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('post_tags', (t) => {
      t.primary(['post_id', 'tag_id']);
      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_tags_on_post_id');
      t.uuid('tag_id')
        .notNullable()
        .references('id')
        .inTable('tags')
        .onDelete('CASCADE')
        .index('index_post_tags_on_tag_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER post_tags_updated_at BEFORE UPDATE
ON post_tags FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('post_tags');
}
