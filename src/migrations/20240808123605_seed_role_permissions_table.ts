import type { Knex } from 'knex';

const rolePermissions = [
  {
    role: 'SUPER_ADMIN',
    permissions: [
      'users:view',
      'users:create',
      'users:update',
      'users:delete',
      'organisations:view',
      'organisations:create',
      'organisations:update',
      'organisations:delete',
      'organisations:users:view',
      'organisations:users:create',
      'organisations:users:update',
      'organisations:users:delete',
      'roles:view',
      'roles:create',
      'roles:update',
      'roles:delete',
      'roles:assign',
    ],
  },
  {
    role: 'OWNER',
    permissions: [
      'users:view',
      'users:create',
      'users:update',
      'users:delete',
      'organisations:view',
      'organisations:create',
      'organisations:update',
      'organisations:delete',
      'organisations:users:view',
      'organisations:users:create',
      'organisations:users:update',
      'organisations:users:delete',
      'roles:view',
      'roles:create',
      'roles:update',
      'roles:delete',
      'roles:assign',
    ],
  },
  {
    role: 'SYSTEM_ADMIN',
    permissions: [
      'users:view',
      'users:create',
      'users:update',
      'organisations:view',
      'organisations:create',
      'organisations:update',
      'organisations:users:view',
      'organisations:users:create',
      'organisations:users:update',
      'roles:view',
      'roles:assign',
    ],
  },
  // {
  //   role: 'ORG_ADMIN',
  //   permissions: [
  //     'organisations:users:view',
  //     'organisations:users:create',
  //     'organisations:users:update',
  //     'organisations:users:delete',
  //     'roles:view',
  //     'roles:assign',
  //   ],
  // },
];

export async function up(knex: Knex): Promise<void> {
  const roles = await knex('roles').select('id', 'key');
  const roleMap = new Map(roles.map((role) => [role.key, role.id]));

  const permissions = await knex('permissions').select('id', 'key');
  const permissionMap = new Map(permissions.map((permission) => [permission.key, permission.id]));

  for (const rolePermission of rolePermissions) {
    const roleId = roleMap.get(rolePermission.role);
    if (!roleId) continue;

    const permissionsToInsert = rolePermission.permissions
      .map((permKey) => ({
        role_id: roleId,
        permission_id: permissionMap.get(permKey),
        status: 1,
      }))
      .filter((rp) => rp.permission_id);

    if (!permissionsToInsert.length) continue;

    await knex('role_permissions').insert(permissionsToInsert);
  }
}

export async function down(knex: Knex): Promise<void> {
  const roles = await knex('roles').select('id', 'key');
  const roleMap = new Map(roles.map((role) => [role.key, role.id]));

  const permissions = await knex('permissions').select('id', 'key');
  const permissionMap = new Map(permissions.map((permission) => [permission.key, permission.id]));

  for (const rolePermission of rolePermissions) {
    const roleId = roleMap.get(rolePermission.role);
    if (!roleId) continue;

    const permissionIds = rolePermission.permissions
      .map((permKey) => permissionMap.get(permKey))
      .filter((id) => id !== undefined);

    if (!permissionIds.length) continue;

    await knex('role_permissions')
      .where('role_id', roleId)
      .whereIn('permission_id', permissionIds)
      .del();
  }
}
