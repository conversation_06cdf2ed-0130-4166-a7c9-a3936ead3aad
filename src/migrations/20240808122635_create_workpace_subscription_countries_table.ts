import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('workspace_subscription_countries', (t) => {
      t.primary(['workspace_subscription_id', 'country_id']);
      t.uuid('workspace_subscription_id')
        .notNullable()
        .references('id')
        .inTable('workspace_subscriptions')
        .onDelete('CASCADE')
        .index('index_workspace_subscription_countries_on_workspace_subscription_id');
      t.uuid('country_id')
        .notNullable()
        .references('id')
        .inTable('countries')
        .onDelete('CASCADE')
        .index('index_workspace_subscription_countries_on_country_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER workspace_subscription_countries_updated_at BEFORE UPDATE
ON workspace_subscription_countries FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workspace_subscription_countries');
}
