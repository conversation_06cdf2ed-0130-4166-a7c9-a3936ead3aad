import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('posts', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('published_by_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_posts_on_published_by_id');

      t.uuid('updated_by_id')
        .notNullable()
        .references('id')
        .inTable('users')
        .onDelete('CASCADE')
        .index('index_posts_on_updated_by_id');
      t.text('content');
      t.uuid('workspace_id')
        .notNullable()
        .references('id')
        .inTable('workspaces')
        .onDelete('CASCADE')
        .index('index_posts_on_workspace_id');
      t.uuid('opportunity_id')
        .references('id')
        .inTable('opportunities')
        .onDelete('CASCADE')
        .unique()
        .index('index_posts_on_opportunity_id');
      t.text('post_status').notNullable(); // published, draft..
      t.text('post_type').notNullable(); // post, article
      t.timestamp('post_schedule_date', { useTz: true }).notNullable();
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER posts_updated_at BEFORE UPDATE
ON posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('posts');
}
