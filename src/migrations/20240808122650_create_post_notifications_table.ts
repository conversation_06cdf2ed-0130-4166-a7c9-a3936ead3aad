import { Knex } from 'knex';

import { EntityType } from '@/constants/user-types';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('post_notifications', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.uuid('entity_id').notNullable().index('index_post_notifications_on_entity_id');
      t.text('entity_type').notNullable().defaultTo(EntityType.USER);

      t.uuid('post_id')
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_post_notifications_on_post_id');
      t.uuid('comment_id')
        .nullable()
        .references('id')
        .inTable('post_comments')
        .onDelete('CASCADE')
        .index('index_post_notifications_on_comment_id');

      t.text('type').notNullable(); // 'like', 'comment'
      t.boolean('is_read').notNullable().defaultTo(false);

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER post_notifications_updated_at BEFORE UPDATE
ON post_notifications FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('post_notifications');
}
