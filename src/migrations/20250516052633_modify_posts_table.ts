import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('posts', (t) => {
    // Drop existing columns that will be replaced or modified
    t.dropColumn('content');

    // Add new columns
    t.text('community').notNullable(); // 'PROFESSIONAL', 'PUBLIC', 'BOTH'
    t.text('audience').notNullable(); // Will store PrimarySpeciality enum values
    t.specificType('tags', 'text ARRAY').defaultTo(knex.raw(`'{}'::text[]`));
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('posts', (t) => {
    // Revert column changes
    t.text('content');

    // Drop added columns
    t.dropColumn('community');
    t.dropColumn('audience');
    t.dropColumn('tags');
  });
}
