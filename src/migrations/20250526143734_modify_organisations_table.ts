import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.text('point_of_contact_name');
    t.renameColumn('phone_number', 'point_of_contact_phone');
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('organisations', (t) => {
    t.dropColumn('point_of_contact_name');
    t.renameColumn('point_of_contact_phone', 'phone_number');
  });
}
