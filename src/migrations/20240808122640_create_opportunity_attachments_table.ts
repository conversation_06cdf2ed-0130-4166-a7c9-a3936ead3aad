import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('opportunity_attachments', (t) => {
    t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
    t.uuid('opportunity_id')
      .notNullable()
      .references('id')
      .inTable('opportunities')
      .onDelete('CASCADE')
      .index('index_opportunity_attachments_on_opportunity_id');
    t.text('file_path').notNullable();
    t.text('file_type').notNullable();
    t.integer('file_size').notNullable();
    t.integer('status').notNullable();
    t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
  }).raw(`
    CREATE TRIGGER opportunity_attachments_updated_at BEFORE UPDATE
    ON opportunity_attachments FOR EACH ROW EXECUTE PROCEDURE 
    update_updated_at_column();
  `);
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('opportunity_attachments');
}
