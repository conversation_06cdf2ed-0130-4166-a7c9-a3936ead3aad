import type { K<PERSON> } from 'knex';
import { getAuth, CreateRequest, UserRecord } from 'firebase-admin/auth';
import { initializeApp, cert, getApps } from 'firebase-admin/app';

import envConfiguration from '@/config/configuration';

import { AuthConstants } from '@/constants/auth';
import { AccountSetupStage, AccountType } from '@/constants/users';
import { Role } from '@/constants/roles';
import { UserPermissionStatus } from '@/constants/user-permissions';

import { UserData } from '@/interfaces/auth';

const envConfig = envConfiguration();

const EMAIL = '<EMAIL>';
const DISPLAY_NAME = 'MiniCardiac Admin';
const PASS = envConfig.superAdminPassword;

if (!getApps().length) {
  initializeApp({
    credential: cert(envConfig.firebaseServiceAccount),
  });
}

export async function up(knex: Knex): Promise<void> {
  const superAdmin = {
    display_name: DISPLAY_NAME,
    email: EMAIL,
  };

  const firebaseObj: CreateRequest = {
    displayName: DISPLAY_NAME,
    email: superAdmin.email,
    emailVerified: true,
    password: PASS,
  };

  let createdNew = false;

  let firebaseUser: UserRecord | null;
  try {
    // check if firebase user already exists
    firebaseUser = await getAuth().getUserByEmail(superAdmin.email);
  } catch (error) {
    // firebase throws an error when the user is not found
    // that causes an exception so we are skipping that error
    if (error.code === 'auth/user-not-found') {
      firebaseUser = null;
    } else {
      // rethrow if it's an unexpected error
      throw new Error(`Error fetching Firebase user: ${error.message}`);
    }
  }

  try {
    if (!firebaseUser) {
      firebaseUser = await getAuth().createUser(firebaseObj);
      createdNew = true;
    }

    const userId = crypto.randomUUID();

    const cleanedUsername = superAdmin.display_name
      .trim()
      .replace(/[^a-zA-Z0-9_-]/g, '')
      .toLowerCase();

    const username = `${cleanedUsername}-${userId}`;

    const insertStaff = {
      ...(firebaseUser.customClaims?.userId && { id: firebaseUser.customClaims.userId }),
      ...superAdmin,
      username,
      current_stage: 'completed',
      account_type: AccountType.SUPER_ADMIN,
      provider_id: firebaseUser.uid,
      status: 1,
    };

    const [insertedUserRow] = await knex('users').insert(insertStaff).returning('id');

    const superAdminRole = await knex('roles').where('key', Role.SUPER_ADMIN).select('id').first();

    const [insertedUserRoleRow] = await knex('user_roles')
      .insert({
        user_id: insertedUserRow.id,
        role_id: superAdminRole.id,
        status: 1,
      })
      .returning('id');

    const superAdminPermissions = await knex('role_permissions')
      .where({
        role_id: superAdminRole.id,
        'permissions.status': 1,
      })
      .leftJoin('permissions', 'role_permissions.permission_id', 'permissions.id')
      .select('permissions.id', 'permissions.key');

    if (superAdminPermissions.length > 0) {
      const userPermissionsData = superAdminPermissions.map(({ id }) => ({
        user_role_id: insertedUserRoleRow.id,
        permission_id: id,
        status: UserPermissionStatus.ACTIVE,
      }));

      await knex('user_permissions').insert(userPermissionsData);
    }

    if (createdNew) {
      const additionalClaims: UserData = {
        [AuthConstants.FIREBASE_CLAIM_USER_ID]: insertedUserRow.id as string,
        [AuthConstants.FIREBASE_CLAIM_EMAIL]: superAdmin.email,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: Role.SUPER_ADMIN,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: superAdminPermissions.map((p) => p.key),
        [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: null,
        [AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE]: AccountType.SUPER_ADMIN,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
      };

      await getAuth().setCustomUserClaims(firebaseUser.uid, additionalClaims);
    }

    // Set Firebase custom claims
  } catch (error) {
    console.error('Error during user creation:', error);
    // Check if Firebase user was created and delete it
    if (firebaseUser?.uid) {
      await getAuth().deleteUser(firebaseUser.uid);
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('users').where('email', EMAIL).del().returning(['provider_id', 'id']);

  // Note: user_role and user_permissions will be automatically deleted due to cascade delete

  // disabled deleting admin from firebase, since other devs also having super admin
  // await getAuth().deleteUser(user.provider_id);
}
