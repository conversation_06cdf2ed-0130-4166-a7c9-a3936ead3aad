import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  const cardiacSpecialistsSubPlans = await knex('subscription_plans')
    .where({
      user_segment: 'CARDIAC_SPECIALIST',
      status: 1,
    })
    .select('id', 'title')
    .orderBy('price_monthly', 'asc');

  const assetUris = {
    Free: '/assets/subscription/cardiac-specialist/free.svg',
    Primary: '/assets/subscription/cardiac-specialist/primary.svg',
    Premium: '/assets/subscription/cardiac-specialist/premium.svg',
    Prestige: '/assets/subscription/cardiac-specialist/prestige.svg',
  };

  for (const plan of cardiacSpecialistsSubPlans) {
    const assetUri = assetUris[plan.title as keyof typeof assetUris];
    if (assetUri) {
      await knex('subscription_plans').where({ id: plan.id }).update({
        asset_uri: assetUri,
      });
    }
  }
}

export async function down(knex: Knex): Promise<void> {
  await knex('subscription_plans')
    .where({
      user_segment: 'CARDIAC_SPECIALIST',
      status: 1,
    })
    .update({ asset_uri: null });
}
