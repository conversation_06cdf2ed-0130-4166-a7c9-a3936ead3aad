import { EntityType } from '@/constants/user-types';
import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('tag_followers', (t) => {
      t.primary(['entity_id', 'tag_id']);
      t.text('entity_type').notNullable().defaultTo(EntityType.USER);
      t.uuid('entity_id').notNullable().index('index_tag_followers_on_entity_id');
      t.uuid('tag_id')
        .notNullable()
        .references('id')
        .inTable('tags')
        .onDelete('CASCADE')
        .index('index_tag_followers_on_tag_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER tag_followers_updated_at BEFORE UPDATE
ON tag_followers FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('tag_followers');
}
