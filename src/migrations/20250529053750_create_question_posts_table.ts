import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('question_posts', (t) => {
      t.uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_question_posts_on_post_id');
      t.text('question').notNullable();
      t.uuid('pinned_comment_id')
        .nullable()
        .references('id')
        .inTable('post_comments')
        .onDelete('SET NULL')
        .index('index_question_posts_on_comment_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER question_posts_updated_at BEFORE UPDATE
ON question_posts FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('question_posts');
}
