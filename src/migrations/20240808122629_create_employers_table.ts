import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('employers', (t) => {
      t.uuid('id', { primaryKey: true }).defaultTo(knex.raw('uuid_generate_v4()'));
      t.text('name').notNullable();
      t.text('url').notNullable();
      t.uuid('organisation_id')
        .references('id')
        .inTable('organisations')
        .onDelete('CASCADE')
        .index('index_employers_on_organisation_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER employers_updated_at BEFORE UPDATE
ON employers FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('employers');
}
