import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .createTable('subscription_plan_features', (t) => {
      t.primary(['subscription_plan_id', 'subscription_feature_id']);
      t.uuid('subscription_plan_id')
        .notNullable()
        .references('id')
        .inTable('subscription_plans')
        .onDelete('CASCADE')
        .index('index_subscription_plan_features_on_subscription_plan_id');
      t.uuid('subscription_feature_id')
        .notNullable()
        .references('id')
        .inTable('subscription_features')
        .onDelete('CASCADE')
        .index('index_subscription_plan_features_on_subscription_feature_id');
      t.boolean('boolean_value');
      t.integer('integer_value');
      t.text('text_value');

      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
CREATE TRIGGER subscription_plan_features_updated_at BEFORE UPDATE
ON subscription_plan_features FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('subscription_plan_features');
}
