import type { K<PERSON> } from 'knex';

// Define the structure of the seed data

enum ValueType {
  BOOLEAN = 1,
  INTEGER = 2,
  TEXT = 3,
}

interface SubscriptionFeatures {
  name: string;
  key: string;
  type: number;
  description?: string;
  status: number;
}

interface SubscriptionPlans {
  title: string;
  subtitle: string;
  description?: string;
  user_segment: 'CARDIAC_SPECIALIST' | 'ALLIED_CARDIAC' | 'STUDENT' | 'ORGANISATION';
  price_monthly: number;
  price_yearly: number;
  status: number;
}

const unlimited = 1000;

const subscriptionFeatures: SubscriptionFeatures[] = [
  {
    name: 'Access to news feed',
    key: 'access_to_new_feed',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Network with Professionals & Organisations',
    key: 'network_with_professionals_and_organisations',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Posts per month',
    key: 'posts_per_month',
    type: ValueType.INTEGER,
    status: 1,
  },
  {
    name: 'Post to the Public community & appear in Public search results',
    key: 'post_to_public_and_appear_in_public_search_results',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Dual-access to Cardiology and Cardiac Surgery Audiences',
    key: 'access_to_cardiology_and_cardiac_surgery_audiences',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Discover and apply for professional opportunities',
    key: 'discover_and_apply_for_professional_opportunities',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Advanced content creation tools & analytics',
    key: 'advanced_content_creation_tools_and_analytics',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Team access for account management',
    key: 'team_access_for_account_management',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Create listings for jobs, research opportunities, and more',
    key: 'create_listings_for_jobs_research_opportunities_and_more',
    type: ValueType.TEXT,
    status: 1,
  },
  {
    name: 'Priority listing in search results and on the news feed',
    key: 'priority_listing_in_search_results_and_on_the_news_feed',
    type: ValueType.BOOLEAN,
    status: 1,
  },
  {
    name: 'Business account with the ability to advertise and sponsor content',
    key: 'business_account_with_the_ability_to_advertise_and_sponsor_content',
    type: ValueType.BOOLEAN,
    status: 1,
  },
];

const subscriptionPlans: SubscriptionPlans[] = [
  // cardiac surgeon
  {
    title: 'Free',
    subtitle: 'Your first step into MiniCardiac',
    description:
      'Check out the platform and connect with your peers, with limited posting ability and no public community access.',
    price_monthly: 0,
    price_yearly: 0,
    status: 1,
    user_segment: 'CARDIAC_SPECIALIST',
  },
  {
    title: 'Primary',
    subtitle: 'Build your professional presence',
    description:
      'Appear in public search results and post to the public community to showcase your work to a wider audience and connect with patients.',
    price_monthly: 12,
    price_yearly: 120,
    status: 1,
    user_segment: 'CARDIAC_SPECIALIST',
  },
  {
    title: 'Premium',
    subtitle: 'For senior professionals',
    description:
      'Have your team manage your correspondence, and enjoy advanced posting capabilities and analytic insights.',
    price_monthly: 123,
    price_yearly: 1230,
    status: 1,
    user_segment: 'CARDIAC_SPECIALIST',
  },
  {
    title: 'Prestige',
    subtitle: 'The ultimate business account',
    description:
      'Enjoy all premium features, as well as priority listing, unlimited posting, and the ability to advertise and sponsor content.',
    price_monthly: 1234,
    price_yearly: 12340,
    status: 1,
    user_segment: 'CARDIAC_SPECIALIST',
  },

  // allied cardiac
  {
    title: 'Free',
    subtitle: 'Your first step into MiniCardiac',
    description:
      'Check out the platform and connect with your peers, with limited posting ability and no public community access.',
    price_monthly: 0,
    price_yearly: 0,
    status: 1,
    user_segment: 'ALLIED_CARDIAC',
  },
  {
    title: 'Primary',
    subtitle: 'Build your professional presence',
    description:
      'Appear in public search results and showcase your work to a wider audience, integrating with either the Cardiology or Cardiac Surgery ecosystem.',
    price_monthly: 12,
    price_yearly: 120,
    status: 1,
    user_segment: 'ALLIED_CARDIAC',
  },
  {
    title: 'Premium',
    subtitle: 'Advanced tools and access',
    description:
      'Have your team manage your account, enjoy advanced networking and analytics, and a dual access to Cardiology and Cardiac Surgery audiences.',
    price_monthly: 123,
    price_yearly: 1230,
    status: 1,
    user_segment: 'ALLIED_CARDIAC',
  },
  {
    title: 'Prestige',
    subtitle: 'The ultimate business account',
    description:
      'Enjoy all premium features, as well as priority listing, unlimited posting, and the ability to advertise and sponsor content.',
    price_monthly: 1234,
    price_yearly: 12340,
    status: 1,
    user_segment: 'ALLIED_CARDIAC',
  },

  // student
  {
    title: 'Student Plan',
    subtitle: 'Advance your career',
    price_monthly: 0,
    price_yearly: 0,
    status: 1,
    user_segment: 'STUDENT',
  },

  // organisation
  {
    title: 'Free',
    subtitle: 'Your first step into MiniCardiac',
    description:
      'Check out the platform and connect with professionals & other organisations, with limited posting ability and no public community access.',
    price_monthly: 0,
    price_yearly: 0,
    status: 1,
    user_segment: 'ORGANISATION',
  },
  {
    title: 'Primary',
    subtitle: 'Just the essentials',
    description:
      'Appear in public search results and showcase your organisation to a wider audience, integrating with either the Cardiology or Cardiac Surgery ecosystem.',
    price_monthly: 12,
    price_yearly: 120,
    status: 1,
    user_segment: 'ORGANISATION',
  },
  {
    title: 'Premium',
    subtitle: 'Advanced tools and access',
    description:
      'Have your team manage your account, enjoy advanced networking and analytics, and a dual access to Cardiology and Cardiac Surgery audiences.',
    price_monthly: 123,
    price_yearly: 1230,
    status: 1,
    user_segment: 'ORGANISATION',
  },
  {
    title: 'Prestige',
    subtitle: 'The ultimate business account',
    description:
      'Enjoy all premium features, as well as priority listing, unlimited posting, and the ability to advertise and sponsor content.',
    price_monthly: 1234,
    price_yearly: 12340,
    status: 1,
    user_segment: 'ORGANISATION',
  },
];

export async function up(knex: Knex): Promise<void> {
  const [
    accessToNewsFeed,
    networkWithProfessionalsAndOrganisations,
    postsPerMonth,
    postToPublicAndAppearInPublicSearchResults,
    accessToCardiologyAndCardiacSurgeryAudiences,
    discoverAndApplyForProfessionalOpportunities,
    advancedContentCreationToolsAndAnalytics,
    teamAccessForAccountManagement,
    ,
    priorityListingInSearchResultsAndOnTheNewsFeed,
    businessAccountWithTheAbilityToAdvertiseAndSponsorContent,
  ] = await knex('subscription_features').insert(subscriptionFeatures).returning(['id', 'type']);

  const [
    { id: cardiacSpecialistFree },
    { id: cardiacSpecialistPrimary },
    { id: cardiacSpecialistPremium },
    { id: cardiacSpecialistPrestige },
    { id: alliedFree },
    { id: alliedPrimary },
    { id: alliedPremium },
    { id: alliedPrestige },
    { id: studentFree },
    { id: organisationFree },
    { id: organisationPrimary },
    { id: organisationPremium },
    { id: organisationPrestige },
  ] = await knex('subscription_plans').insert(subscriptionPlans).returning('id');

  const subscription_plan_features = [
    {
      feature: accessToNewsFeed,
      values: [
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedFree,
            alliedPrimary,
            alliedPremium,
            alliedPrestige,
            studentFree,
            organisationFree,
            organisationPrimary,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
      ],
    },
    {
      feature: networkWithProfessionalsAndOrganisations,
      values: [
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedFree,
            alliedPrimary,
            alliedPremium,
            alliedPrestige,
            studentFree,
            organisationFree,
            organisationPrimary,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
      ],
    },
    {
      feature: postsPerMonth,
      values: [
        {
          plans: [cardiacSpecialistFree, alliedFree],
          value: 3,
        },
        {
          plans: [organisationFree],
          value: 5,
        },
        {
          plans: [cardiacSpecialistPrimary, alliedPrimary],
          value: 9,
        },
        {
          plans: [organisationPrimary],
          value: 15,
        },
        {
          plans: [cardiacSpecialistPremium, alliedPremium],
          value: 27,
        },
        {
          plans: [studentFree],
          value: 31,
        },
        {
          plans: [organisationPremium],
          value: 45,
        },
        {
          plans: [cardiacSpecialistPrestige, alliedPrestige, organisationPrestige],
          value: unlimited,
        },
      ],
    },
    {
      feature: postToPublicAndAppearInPublicSearchResults,
      values: [
        {
          plans: [
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedPrimary,
            alliedPremium,
            alliedPrestige,
            organisationPrimary,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
        {
          plans: [cardiacSpecialistFree, alliedFree, studentFree, organisationFree],
          value: false,
        },
      ],
    },
    {
      feature: accessToCardiologyAndCardiacSurgeryAudiences,
      values: [
        {
          plans: [
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedPremium,
            alliedPrestige,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            alliedFree,
            alliedPrimary,
            organisationFree,
            organisationPrimary,
          ],
          value: false,
        },
      ],
    },
    {
      feature: discoverAndApplyForProfessionalOpportunities,
      values: [
        {
          plans: [
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedPrimary,
            alliedPremium,
            alliedPrestige,
            organisationPrimary,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
        {
          plans: [cardiacSpecialistFree, alliedFree, studentFree],
          value: false,
        },
        {
          plans: [organisationFree],
          value: null,
        },
      ],
    },
    {
      feature: advancedContentCreationToolsAndAnalytics,
      values: [
        {
          plans: [
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedPremium,
            alliedPrestige,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
        {
          plans: [cardiacSpecialistFree, alliedFree, studentFree, organisationFree],
          value: false,
        },
        {
          plans: [cardiacSpecialistPrimary, alliedPrimary, organisationPrimary],
          value: null,
        },
      ],
    },
    {
      feature: teamAccessForAccountManagement,
      values: [
        {
          plans: [
            cardiacSpecialistPremium,
            cardiacSpecialistPrestige,
            alliedPremium,
            alliedPrestige,
            organisationPremium,
            organisationPrestige,
          ],
          value: true,
        },
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            alliedFree,
            alliedPrimary,
            studentFree,
            organisationFree,
            organisationPrimary,
          ],
          value: false,
        },
      ],
    },
    {
      feature: priorityListingInSearchResultsAndOnTheNewsFeed,
      values: [
        {
          plans: [cardiacSpecialistPrestige, alliedPrestige, organisationPrestige],
          value: true,
        },
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            alliedFree,
            alliedPrimary,
            alliedPremium,
            studentFree,
            organisationFree,
            organisationPrimary,
            organisationPremium,
          ],
          value: false,
        },
      ],
    },
    {
      feature: businessAccountWithTheAbilityToAdvertiseAndSponsorContent,
      values: [
        {
          plans: [cardiacSpecialistPrestige, alliedPrestige, organisationPrestige],
          value: true,
        },
        {
          plans: [
            cardiacSpecialistFree,
            cardiacSpecialistPrimary,
            cardiacSpecialistPremium,
            alliedFree,
            alliedPrimary,
            alliedPremium,
            studentFree,
            organisationFree,
            organisationPrimary,
            organisationPremium,
          ],
          value: false,
        },
      ],
    },
  ];

  for (const subscriptionPlanFeature of subscription_plan_features) {
    let valueInsertField = 'boolean_value';

    switch (subscriptionPlanFeature.feature.type) {
      case ValueType.INTEGER:
        valueInsertField = 'integer_value';
        break;
      case ValueType.TEXT:
        valueInsertField = 'text_value';
        break;
    }

    const insertValues = subscriptionPlanFeature.values.flatMap((plansAndValues) =>
      plansAndValues.plans.map((plan) => ({
        subscription_plan_id: plan,
        subscription_feature_id: subscriptionPlanFeature.feature.id,
        [valueInsertField]: plansAndValues.value,
        status: 1,
      })),
    );

    await knex('subscription_plan_features').insert(insertValues);
  }
}

export async function down(knex: Knex): Promise<void> {
  // First get the keys of all subscription features added in this migration
  const featureKeys = subscriptionFeatures.map((feature) => feature.key);

  // Find the IDs of the features we added
  const featuresAdded = await knex('subscription_features')
    .whereIn('key', featureKeys)
    .select('id');

  const featureIds = featuresAdded.map((feature) => feature.id);

  // Get titles and user segments of subscription plans added in this migration
  const planIdentifiers = subscriptionPlans.map((plan) => ({
    title: plan.title,
    user_segment: plan.user_segment,
  }));

  // Find the IDs of the plans we added
  const plansAdded = await knex('subscription_plans')
    .where(function () {
      for (const plan of planIdentifiers) {
        this.orWhere(function () {
          this.where('title', plan.title).where('user_segment', plan.user_segment);
        });
      }
    })
    .select('id');

  const planIds = plansAdded.map((plan) => plan.id);

  // Delete from subscription_plan_features first (foreign key relationships)
  if (planIds.length > 0 || featureIds.length > 0) {
    await knex('subscription_plan_features')
      .where(function () {
        if (planIds.length > 0) {
          this.whereIn('subscription_plan_id', planIds);
        }
        if (featureIds.length > 0) {
          if (planIds.length > 0) {
            this.orWhereIn('subscription_feature_id', featureIds);
          } else {
            this.whereIn('subscription_feature_id', featureIds);
          }
        }
      })
      .del();
  }

  // Delete the subscription plans
  if (planIds.length > 0) {
    await knex('subscription_plans').whereIn('id', planIds).del();
  }

  // Delete the subscription features
  if (featureIds.length > 0) {
    await knex('subscription_features').whereIn('id', featureIds).del();
  }
}
