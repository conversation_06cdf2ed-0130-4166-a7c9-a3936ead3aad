// TODO: The permission enums should be created based on each role.
// export enum UserPermissions {
//   VIEW_ORGANISATION_USERS = 'organisations:users:view',
//   CREATE_ORGANISATION_USER = 'organisations:users:create',
//   UPDATE_ORGANISATION_USER = 'organisations:users:update',
//   DELETE_ORGANISATION_USER = 'organisations:users:delete',
//   VIEW_ROLES = 'roles:view',
//   CREATE_ROLE = 'roles:create',
//   UPDATE_ROLE = 'roles:update',
//   DELETE_ROLE = 'roles:delete',
//   ASSIGN_ROLE = 'roles:assign',
// }

// export enum GlobalPermissions {
//   VIEW_USERS = 'users:view',
//   CREATE_USER = 'users:create',
//   UPDATE_USER = 'users:update',
//   DELETE_USER = 'users:delete',
//   VIEW_ORGANISATIONS = 'organisations:view',
//   CREATE_ORGANISATION = 'organisations:create',
//   UPDATE_ORGANISATION = 'organisations:update',
//   DELETE_ORGANISATION = 'organisations:delete',
// }

export enum Permissions {
  VIEW_ORGANISATION_USERS = 'organisations:users:view',
  CREATE_ORGANISATION_USER = 'organisations:users:create',
  UPDATE_ORGANISATION_USER = 'organisations:users:update',
  DELETE_ORGANISATION_USER = 'organisations:users:delete',
  VIEW_ROLES = 'roles:view',
  CREATE_ROLE = 'roles:create',
  UPDATE_ROLE = 'roles:update',
  DELETE_ROLE = 'roles:delete',
  ASSIGN_ROLE = 'roles:assign',
  VIEW_USERS = 'users:view',
  CREATE_USER = 'users:create',
  UPDATE_USER = 'users:update',
  DELETE_USER = 'users:delete',
  VIEW_ORGANISATIONS = 'organisations:view',
  CREATE_ORGANISATION = 'organisations:create',
  UPDATE_ORGANISATION = 'organisations:update',
  DELETE_ORGANISATION = 'organisations:delete',
}
