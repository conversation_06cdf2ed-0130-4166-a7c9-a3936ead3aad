export enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  ARCHIVED = -1,
}

export const GUEST_USER_PRE_DEFINED_VAL = {
  displayName: 'guest user',
};

export enum AccountType {
  // account types shown during signup
  PROFESSIONAL = 'PROFESSIONAL',
  ORGANISATION = 'ORGANISATION',
  PUBLIC = 'PUBLIC',
  // internal types
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
  OWNER = 'OWNER',
}

export enum AccountSetupStage {
  SUBSCRIPTION = 'onboarding:subscription',
  PROFILE_SETUP = 'onboarding:profile_setup',
  DOCUMENT_UPLOAD = 'onboarding:document_upload',
  ADDING_NETWORK = 'onboarding:adding_network',
  COMPLETED = 'completed',
}
