import { UserSegment } from '../user-segments';
import { AccountType } from '../users';

// All possible Role in the system
export enum Role {
  CARDIAC_SPECIALIST = 'CARDIAC_SPECIALIST',
  ALLIED_CARDIAC = 'ALLIED_CARDIAC',
  ORGANISATION = 'ORGANISATION',
  STUDENT = 'STUDENT',
  PUBLIC = 'PUBLIC',

  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
  OWNER = 'OWNER',
}

export enum PublicRole {
  CARDIAC_SPECIALIST = Role.CARDIAC_SPECIALIST,
  ALLIED_CARDIAC = Role.ALLIED_CARDIAC,
  STUDENT = Role.STUDENT,
  ORGANISATION = Role.ORGANISATION,
  PUBLIC = Role.PUBLIC,
}

export enum InternalRole {
  SYSTEM_ADMIN = Role.SYSTEM_ADMIN,
  SUPER_ADMIN = Role.SUPER_ADMIN,
  OWNER = Role.OWNER,
}

export enum RoleLevel {
  INTERNAL = 'INTERNAL',
  PUBLIC = 'PUBLIC',
}

// Mapping between global account types types and their Role
export const PUBLIC_ACCOUNT_TYPE_ROLES_MAP = {
  [AccountType.PROFESSIONAL]: [
    UserSegment.ALLIED_CARDIAC,
    UserSegment.CARDIAC_SPECIALIST,
    UserSegment.STUDENT,
  ],
  [AccountType.ORGANISATION]: [UserSegment.ORGANISATION],
};

export enum RoleStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}
