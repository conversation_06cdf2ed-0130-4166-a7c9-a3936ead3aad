import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Controller, Get, Query } from '@nestjs/common';

import { SegmentCategoriesService } from './segment-categories.service';

import { GetSegmentCategoriesQueryDto } from './dto/get-segment-categories.dto';

@Controller('segment-categories')
@ApiTags('segment-categories')
@ApiBearerAuth()
export class SegmentCategoriesController {
  constructor(private readonly segmentCateogories: SegmentCategoriesService) {}

  @Get()
  findAll(@Query() query: GetSegmentCategoriesQueryDto) {
    return this.segmentCateogories.getSegmentCategories(query);
  }
}
