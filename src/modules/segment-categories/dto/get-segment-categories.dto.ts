import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';

import { UserSegment } from '@/constants/user-segments';

export class GetSegmentCategoriesQueryDto {
  @ApiPropertyOptional({
    description: 'The subtype ID (UUID format)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  segmentCategoryId?: string;

  @ApiPropertyOptional({
    description: 'The user segment type',
    example: UserSegment,
  })
  @IsOptional()
  @IsEnum(UserSegment, {
    message: `type must be one of the following values: ${Object.values(UserSegment).join(', ')}`,
  })
  userSegment?: UserSegment;
}
