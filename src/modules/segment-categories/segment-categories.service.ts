import { SQL, and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { Inject, Injectable } from '@nestjs/common';

import { GetSegmentCategoriesQueryDto } from './dto/get-segment-categories.dto';

import * as schema from '@/db/schema';
import { segmentCategories, SegmentCategory } from '@/db/schema';

import { SegmentCategoriesStatus } from '@/constants/segment-categories';

type SegmentCategoryWithoutStatus = Omit<SegmentCategory, 'status' | 'createdAt' | 'updatedAt'>;

export interface AccountTypeMap {
  [key: string]: SegmentCategoryWithoutStatus[];
}

@Injectable()
export class SegmentCategoriesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async getSegmentCategories({
    userSegment,
    segmentCategoryId,
  }: GetSegmentCategoriesQueryDto): Promise<AccountTypeMap | SegmentCategoryWithoutStatus[]> {
    const where: SQL[] = [eq(segmentCategories.status, SegmentCategoriesStatus.ACTIVE)];

    if (userSegment) {
      where.push(eq(segmentCategories.userSegment, userSegment));
    }

    const segmentCategoriesDatas = await this.drizzleDev.query.segmentCategories.findMany({
      where: and(...where),
      columns: {
        id: true,
        name: true,
        parentId: true,
        userSegment: true,
      },
    });

    const segmentCategoriesMap: Record<
      string,
      SegmentCategoryWithoutStatus & { children: SegmentCategoryWithoutStatus[] }
    > = {};
    segmentCategoriesDatas.forEach((sc) => {
      segmentCategoriesMap[sc.id] = { ...sc, children: [] };
    });

    segmentCategoriesDatas.forEach((sc) => {
      if (sc.parentId && segmentCategoriesMap[sc.parentId]) {
        segmentCategoriesMap[sc.parentId].children.push(segmentCategoriesMap[sc.id]);
      }
    });

    // If an ID is provided, return only the children of that subtype
    if (segmentCategoryId) {
      return segmentCategoriesMap[segmentCategoryId]
        ? segmentCategoriesMap[segmentCategoryId].children
        : [];
    }

    const userSegmentMap: Record<string, SegmentCategoryWithoutStatus[]> = {};

    segmentCategoriesDatas.forEach((sc) => {
      if (!sc.parentId) {
        if (!userSegmentMap[sc.userSegment]) {
          userSegmentMap[sc.userSegment] = [];
        }
        userSegmentMap[sc.userSegment].push(sc);
      }
    });

    // If accountType is provided, return only that accountType group
    if (userSegment) {
      return userSegmentMap[userSegment] || [];
    }

    // Otherwise, return the entire accountTypeMap with all subtypes
    return userSegmentMap;
  }
}
