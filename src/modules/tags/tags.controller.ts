import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { TagsService } from './tags.service';

import { TagDto, TagSearchDto } from './dto/tag.dto';

@ApiTags('Tags')
@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Get('search')
  @ApiOperation({ summary: 'Search tags for autocompletion' })
  @ApiResponse({
    status: 200,
    description: 'Returns list of matching tags',
    type: [TagDto],
  })
  async searchTags(@Query() searchDto: TagSearchDto): Promise<TagDto[]> {
    return this.tagsService.searchTags(searchDto.query, searchDto.limit);
  }
}
