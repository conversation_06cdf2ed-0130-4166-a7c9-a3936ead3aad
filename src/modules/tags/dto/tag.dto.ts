import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, <PERSON>N<PERSON>ber, IsString, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class TagDto {
  @ApiProperty({
    description: 'Tag ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tag name',
    example: 'cardiology',
  })
  name: string;
}

export class TagSearchDto {
  @ApiProperty({
    description: 'Search query for tag name',
    example: 'card',
    required: false,
  })
  @IsString()
  @IsOptional()
  query?: string;

  @ApiProperty({
    description: 'Maximum number of results to return',
    example: 10,
    required: false,
    default: 10,
  })
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(50)
  limit?: number;
}
