import { Inject, Injectable } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { itemAlreadyExists } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';

import * as schema from '@/db/schema';
import { workspaceUsers } from '@/db/schema';
import { CreateWorkspaceUserDto } from './dto/create-workspace-users.dto';
import { WorkspaceUsersStatus } from '@/constants/workspaces';
// import { CreateUserDto } from './dto/create-workspace-users.dto';
// import { UpdateWorkspaceUserDto } from './dto/update-workspace-users.dto';
// import { UsersService } from '@/modules/users/users.service';

@Injectable()
export class WorkspaceUsersService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async create(
    createWorkspaceUserDto: CreateWorkspaceUserDto,
    transaction: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const workspaceUser = await dbOrTransaction.query.workspaceUsers.findFirst({
      where: and(
        eq(workspaceUsers.workspaceId, createWorkspaceUserDto.workspaceId),
        eq(workspaceUsers.userId, createWorkspaceUserDto.userId),
      ),
    });

    if (workspaceUser) throw itemAlreadyExists(EntityName.WORKSPACE);

    const [newOrgUser] = await dbOrTransaction
      .insert(workspaceUsers)
      .values({
        ...createWorkspaceUserDto,
        status: WorkspaceUsersStatus.ACTIVE,
      })
      .returning();

    return newOrgUser;
  }
}
