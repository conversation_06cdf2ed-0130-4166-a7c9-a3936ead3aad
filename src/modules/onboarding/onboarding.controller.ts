import { ApiTags } from '@nestjs/swagger';
import { Body, Controller, Inject, Post } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import { User } from '@/decorators/user.decorator';
import { AccountTypes } from '@/decorators/account-types.decorator';
import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import * as schema from '@/db/schema';
import { users } from '@/db/schema';

import { UserData } from '@/interfaces/auth';

import { AuthConstants } from '@/constants/auth';
import { Role } from '@/constants/roles';
import { AccountSetupStage, AccountType } from '@/constants/users';
import { EntityName } from '@/constants/entities';

import { itemNotFound } from '@/exceptions/common';

import { PublicProfileDto } from './dto/public-onboarding.dto';
import { DocumentsArrayDto, SurgeonProfileDto } from './dto/specialist-onboarding.dto';
import { ProfessionalProfileDto } from './dto/professional-onboarding.dto';
import { StudentProfileDto } from './dto/student-onboarding.dto';
import { OrganisationProfileDto } from './dto/organisation-onboarding.dto';

import { OnboardingService } from './onboarding.service';
import { RolesService } from '@/modules/roles/roles.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';

@Controller('onboarding')
@ApiTags('onboarding')
export class OnboardingController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly onboardingService: OnboardingService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
  ) {}

  @Post('profile-setup/public')
  @AccountTypes(AccountType.PUBLIC)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async publicProfileSetup(
    @Body() profileSetupDto: PublicProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.drizzleDev.transaction(async (txn) => {
      const user = await this.onboardingService.publicProfileSetup(userId, profileSetupDto, txn);

      const role = await this.rolesService.findRoleByKey(Role.PUBLIC);
      if (!role) throw itemNotFound(EntityName.ROLE);

      // Create user role and associate it
      await this.userRolesService.createUserRole({ userId, roleId: role.id }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions?.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      await this.updateFirebaseClaims(user[0].providerId, {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
      });

      return { nextStep: AccountSetupStage.COMPLETED };
    });
  }

  @Post('profile-setup/specialist')
  @UserRoles(Role.CARDIAC_SPECIALIST)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async specialistProfileSetup(
    @Body() profileSetupDto: SurgeonProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const user = await this.onboardingService.specialistProfileSetup(
      userId,
      workspaceId,
      profileSetupDto,
    );

    await this.updateFirebaseClaims(user.providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
    });

    return user;
  }

  @Post('profile-setup/allied-cardiac')
  @UserRoles(Role.ALLIED_CARDIAC)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async alliedCardiacProfileSetup(
    @Body() profileSetupDto: ProfessionalProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const user = await this.onboardingService.professionalProfileSetup(
      userId,
      workspaceId,
      profileSetupDto,
    );

    await this.updateFirebaseClaims(user.providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
    });

    return user;
  }

  @Post('profile-setup/student')
  @UserRoles(Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async studentProfileSetup(
    @Body() profileSetupDto: StudentProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const user = await this.onboardingService.studentProfileSetup(
      userId,
      workspaceId,
      profileSetupDto,
    );

    await this.updateFirebaseClaims(user.providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
    });

    return user;
  }

  @Post('profile-setup/organisation')
  @UserRoles(Role.ORGANISATION)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async organisationProfileSetup(
    @Body() profileSetupDto: OrganisationProfileDto,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    const user = await this.onboardingService.organisationProfileSetup(
      userId,
      workspaceId,
      profileSetupDto,
    );

    await this.updateFirebaseClaims(user.providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: user.currentStage,
    });

    return user;
  }

  /**
   * Unified document upload endpoint for all user types
   */
  @Post('document-upload')
  @UserRoles(Role.CARDIAC_SPECIALIST, Role.ALLIED_CARDIAC, Role.ORGANISATION, Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.DOCUMENT_UPLOAD)
  async documentUploads(
    @Body() documentsDto: DocumentsArrayDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const userResult = await this.onboardingService.documentUpload(
      userId,
      workspaceId,
      documentsDto.documents,
    );

    const { providerId } = userResult[0];
    await this.updateFirebaseClaims(providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.ADDING_NETWORK,
    });

    return userResult;
  }

  @Post('complete-networking-stage')
  @UserRoles(Role.CARDIAC_SPECIALIST, Role.ALLIED_CARDIAC, Role.ORGANISATION, Role.STUDENT)
  @AccountSetupStages(AccountSetupStage.ADDING_NETWORK)
  async completeNetworkingStage(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_UID) providerId: string,
  ) {
    await this.drizzleDev
      .update(users)
      .set({
        currentStage: AccountSetupStage.COMPLETED,
      })
      .where(and(eq(users.id, userId)));

    await this.updateFirebaseClaims(providerId, {
      [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.COMPLETED,
    });

    return 'ok';
  }

  /**
   * Helper method to update Firebase custom claims
   */
  private async updateFirebaseClaims(providerId: string, newClaims: Record<string, any>) {
    const userRecord = await getAuth().getUser(providerId);
    const currentClaims: UserData = userRecord.customClaims as UserData;

    // Merge the new claims with the existing ones
    const updatedClaims = {
      ...currentClaims,
      ...newClaims,
    };

    // Update Firebase custom claims
    await getAuth().setCustomUserClaims(providerId, updatedClaims);
  }
}
