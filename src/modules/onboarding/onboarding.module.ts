import { Module } from '@nestjs/common';

import { OnboardingService } from './onboarding.service';
import { OnboardingController } from './onboarding.controller';

import { RolesModule } from '@/modules/roles/roles.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';

@Module({
  imports: [RolesModule, UserRolesModule],
  controllers: [OnboardingController],
  providers: [OnboardingService],
})
export class OnboardingModule {}
