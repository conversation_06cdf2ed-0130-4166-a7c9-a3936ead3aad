import {
  <PERSON>B<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON>al,
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseProfileDto } from './base-onboarding.dto';

import { PrimarySpeciality } from '@/constants/workspaces';
import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

// Organisation-specific DTO
export class OrganisationProfileDto extends BaseProfileDto {
  @ApiProperty({
    description: 'Organization website',
    required: false,
    example: 'https://example.com',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  website?: string;

  @ApiProperty({
    description: 'Organization size',
    required: false,
    example: '50-100',
  })
  @IsString()
  @IsOptional()
  @MaxLength(MAX_LENGTH)
  organisationSize?: string;

  @ApiProperty({
    description: 'Name of the point of contact',
    required: false,
    example: '<PERSON>',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  pointOfContactName?: string;

  @ApiProperty({
    description: 'Phone number of the point of contact',
    required: false,
    example: '+**********',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  pointOfContactPhone?: string;

  @ApiProperty({
    description: 'wants subsidiary accounts or not',
    required: false,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  allowSubsidiaries?: boolean;

  // Organisation-specific fields
  @IsNotEmpty()
  @IsUUID()
  segmentCategoryId: string;

  @ApiProperty({
    name: 'primarySpeciality',
    enum: PrimarySpeciality,
    required: false,
    default: PrimarySpeciality.CARDIAC_SURGEON,
    description: "send this if it's professional account type",
  })
  @IsEnum(PrimarySpeciality)
  primarySpeciality: PrimarySpeciality;

  @IsString()
  @IsNotEmpty()
  location: string;

  @IsString()
  @IsOptional()
  mapLink: string;

  @IsUUID()
  @IsOptional()
  parentOrganisationId: string;
}

// Helper function to extract only basic fields
export function extractNonPrestigeFields(
  dto: OrganisationProfileDto,
): Omit<OrganisationProfileDto, 'organisationSize' | 'wantsSubsidiaryAccounts'> {
  return {
    profileImageUrl: dto.profileImageUrl,
    profileImageUrlThumbnail: dto.profileImageUrlThumbnail,
    introductoryStatement: dto.introductoryStatement,
    segmentCategoryId: dto.segmentCategoryId,
    primarySpeciality: dto.primarySpeciality,
    location: dto.location,
    mapLink: dto.mapLink,
    parentOrganisationId: dto.parentOrganisationId,
  };
}
