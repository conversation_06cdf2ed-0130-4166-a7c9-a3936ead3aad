import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export class BaseProfileDto {
  @ApiProperty({
    name: 'introductoryStatement',
    type: 'string',
    required: false,
    example: 'Just for exploring',
  })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MaxLength(240, { message: 'maximum 240 characters only' })
  @IsNotEmpty()
  introductoryStatement: string;

  @ApiProperty({
    name: 'profileImageUrl',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrl?: string;

  @ApiProperty({
    name: 'profileImageUrlThumbnail',
    type: 'string',
    required: false,
    example: 'public/image-url.com',
  })
  @IsString()
  @IsOptional()
  profileImageUrlThumbnail?: string;
}
