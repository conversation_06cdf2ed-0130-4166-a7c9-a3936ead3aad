import { IsEnum, <PERSON>NotEmpty, IsString, IsUUID } from 'class-validator';

import { BaseProfileDto } from './base-onboarding.dto';
import { PrimarySpeciality } from '@/constants/workspaces';
import { ApiProperty } from '@nestjs/swagger';

// Surgeon-specific DTO
export class ProfessionalProfileDto extends BaseProfileDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  qualifications: string;

  @IsNotEmpty()
  @IsUUID()
  segmentCategoryId: string;

  @ApiProperty({
    name: 'primarySpeciality',
    enum: PrimarySpeciality,
    required: false,
    default: PrimarySpeciality.CARDIAC_SURGEON,
    description: "send this if it's professional account type",
  })
  @IsEnum(PrimarySpeciality)
  primarySpeciality: PrimarySpeciality;

  @IsNotEmpty()
  @IsString()
  designation: string;

  @IsNotEmpty()
  @IsUUID()
  employerId: string;
}
