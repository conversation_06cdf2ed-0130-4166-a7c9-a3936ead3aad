import { Inject, Injectable } from '@nestjs/common';
// import { CreateUserFollowerDto } from './dto/create-user-follower.dto';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, lte, sql, inArray, notInArray } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  tagFollowers,
  tags,
  postTags,
  posts,
  postMedias,
  mediaPosts,
  textPosts,
} from '@/db/schema';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { SUGGESTED_TAGS_LIMIT, TagFollowerStatus } from '@/constants/tag-follow';
import { PostActiveStatus, PostType } from '@/constants/posts';
import { TagStatus } from '@/constants/tags';
import { PostTagsStatus } from '@/constants/post-tags';
import { PostMediaStatus } from '@/constants/post-media';

// ------------------------------------------------------------

type PostRow = {
  tagId: string;
  tagName: string;
  postId: string;
  postType: PostType.MEDIA | PostType.TEXT;
  postScheduleDate: Date;
  mediaCaption?: string | null;
  mediaCount?: number | null;
  mediaItems?: {
    mediaPath: string;
    mediaType: string;
    altText: string | null;
  }[];
  textContent?: string | null;
};

type FormattedPost = {
  postId: string;
  postType: PostType.MEDIA | PostType.TEXT;
  content?: string | null;
  mediaCount?: number | null;
  postMedias?: {
    mediaPath: string;
    mediaType: string;
    altText: string | null;
  }[];
};

type GroupedTagPost = {
  tagId: string;
  tagName: string;
  posts: FormattedPost[];
};

@Injectable()
export class TagFollowService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createFollower(entityId: string, entityType: EntityType, tagId: string) {
    const tagFollower = await this.drizzleDev.query.tagFollowers.findFirst({
      where: and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)),
    });

    if (tagFollower) {
      if (tagFollower.status === TagFollowerStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.TAG_FOLLOWER);
      } else if (tagFollower.status === TagFollowerStatus.INACTIVE) {
        const [updatedTagFollower] = await this.drizzleDev
          .update(tagFollowers)
          .set({ status: TagFollowerStatus.ACTIVE })
          .where(and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.tagId, tagId)))
          .returning();

        return updatedTagFollower;
      }
    }

    const [newTagFollower] = await this.drizzleDev
      .insert(tagFollowers)
      .values({
        tagId,
        entityId,
        entityType,
        status: TagFollowerStatus.ACTIVE,
      })
      .returning();

    return newTagFollower;
  }

  async softDeleteTagFollower(entityId: string, tagId: string) {
    const [deletedTagFollower] = await this.drizzleDev
      .update(tagFollowers)
      .set({ status: TagFollowerStatus.INACTIVE })
      .where(
        and(
          eq(tagFollowers.entityId, entityId),
          eq(tagFollowers.tagId, tagId),
          eq(tagFollowers.status, TagFollowerStatus.ACTIVE),
        ),
      )
      .returning();

    if (!deletedTagFollower) throw itemNotFound(EntityName.TAG_FOLLOWER);

    return deletedTagFollower;
  }

  async findLatestPostsForFollowingTag(entityId: string) {
    const db = this.drizzleDev;

    const userTags = await db
      .select({ tagId: tags.id, tagName: tags.name })
      .from(tagFollowers)
      .innerJoin(tags, and(eq(tagFollowers.tagId, tags.id), eq(tags.status, TagStatus.ACTIVE)))
      .where(
        and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.status, TagFollowerStatus.ACTIVE)),
      )
      .orderBy(sql`RANDOM()`)
      .limit(SUGGESTED_TAGS_LIMIT);

    if (userTags.length === 0) return [];

    const postsData = await this.getTopPostsForTags(userTags.map((t) => t.tagId));
    return this.groupAndScorePosts(postsData, userTags);
  }

  async findSuggestedTagsWithPosts(entityId: string) {
    const db = this.drizzleDev;

    const followed = await db
      .select({ tagId: tagFollowers.tagId })
      .from(tagFollowers)
      .where(
        and(eq(tagFollowers.entityId, entityId), eq(tagFollowers.status, TagFollowerStatus.ACTIVE)),
      );

    const followedIds = followed.map((f) => f.tagId);

    const suggestedTags = await db
      .select({ tagId: tags.id, tagName: tags.name })
      .from(tags)
      .where(
        and(
          eq(tags.status, TagStatus.ACTIVE),
          followedIds.length > 0 ? notInArray(tags.id, followedIds) : sql`TRUE`,
        ),
      )
      .orderBy(sql`RANDOM()`)
      .limit(SUGGESTED_TAGS_LIMIT);

    if (suggestedTags.length === 0) return [];

    const postsData = await this.getTopPostsForTags(suggestedTags.map((t) => t.tagId));
    return this.groupAndScorePosts(postsData, suggestedTags);
  }

  private groupAndScorePosts(
    postsData: PostRow[],
    tagsDetails: { tagId: string; tagName: string }[],
  ): GroupedTagPost[] {
    return tagsDetails.map((tag) => {
      const relevantPosts = postsData
        .filter((p) => p.tagId === tag.tagId)
        .map((p) => {
          const hasCaption = (p.mediaCaption?.trim()?.length || 0) > 0;
          const hasContent = (p.textContent?.trim()?.length || 0) > 0;
          let priority = 0;

          if (p.postType === PostType.MEDIA) {
            if (p.mediaCount! >= 3 && hasCaption) priority = 1;
            else if (p.mediaCount === 2 && hasCaption) priority = 2;
            else if (p.mediaCount === 1 && hasCaption) priority = 3;
          } else if (p.postType === PostType.TEXT && hasContent) {
            priority = 4;
          }

          return { ...p, priority };
        })
        .filter((p) => p.priority > 0)
        .sort(
          (a, b) =>
            a.priority - b.priority ||
            b.postScheduleDate.toISOString().localeCompare(a.postScheduleDate.toISOString()),
        )
        .slice(0, 2)
        .map((p) => ({
          postId: p.postId,
          postType: p.postType,
          content: p.mediaCaption ?? p.textContent,
          mediaCount: p.mediaCount,
          postMedias: p.mediaItems,
        }));

      return {
        tagId: tag.tagId,
        tagName: tag.tagName,
        posts: relevantPosts,
      };
    });
  }

  private async getTopPostsForTags(tagIds: string[]): Promise<PostRow[]> {
    const db = this.drizzleDev;

    const data = await db
      .select({
        tagId: postTags.tagId,
        tagName: tags.name,
        postId: posts.id,
        postType: posts.postType,
        postScheduleDate: posts.postScheduleDate,
        mediaCaption: mediaPosts.caption,
        mediaCount: sql<number>`count(distinct ${postMedias.id})`.mapWith(Number),
        mediaItems: sql<
          {
            mediaPath: string;
            mediaType: string;
            altText: string | null;
          }[]
        >`
        json_agg(
          jsonb_build_object(
            'mediaPath', ${postMedias.mediaPath},
            'mediaType', ${postMedias.mediaType},
            'altText', ${postMedias.altText}
          )
          ORDER BY ${postMedias.order} ASC
        )
      `.mapWith((v) => v ?? []),
        textContent: textPosts.content,
      })
      .from(postTags)
      .innerJoin(tags, and(eq(postTags.tagId, tags.id), eq(tags.status, TagStatus.ACTIVE)))
      .innerJoin(
        posts,
        and(
          eq(postTags.postId, posts.id),
          eq(posts.status, PostActiveStatus.ACTIVE),
          inArray(posts.postType, [PostType.MEDIA, PostType.TEXT]),
          lte(posts.postScheduleDate, new Date()),
        ),
      )
      .leftJoin(
        mediaPosts,
        and(eq(posts.id, mediaPosts.postId), eq(posts.postType, PostType.MEDIA)),
      )
      .leftJoin(
        postMedias,
        and(eq(posts.id, postMedias.postId), eq(postMedias.status, PostMediaStatus.ACTIVE)),
      )
      .leftJoin(textPosts, and(eq(posts.id, textPosts.postId), eq(posts.postType, PostType.TEXT)))
      .where(and(inArray(postTags.tagId, tagIds), eq(postTags.status, PostTagsStatus.ACTIVE)))
      .groupBy(
        postTags.tagId,
        tags.name,
        posts.id,
        posts.postType,
        posts.postScheduleDate,
        mediaPosts.caption,
        textPosts.content,
      );

    return data.map((d) => ({
      ...d,
      postType: d.postType === PostType.MEDIA ? PostType.MEDIA : PostType.TEXT,
    }));
  }
  // async findFollowStatsOfAWorkspaceById(workspaceId: string, entityId?: string) {
  //   const [{ followingCount }] = await this.drizzleDev
  //     .select({ followingCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.entityId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const [{ followersCount }] = await this.drizzleDev
  //     .select({ followersCount: count() })
  //     .from(workspaceFollowers)
  //     .where(
  //       and(
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     );

  //   const response: { followersCount: number; followingCount: number; isFollowing?: boolean } = {
  //     followersCount,
  //     followingCount,
  //   };

  //   if (entityId) {
  //     const isExist = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //       where: and(
  //         eq(workspaceFollowers.entityId, entityId),
  //         eq(workspaceFollowers.workspaceId, workspaceId),
  //         eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //       ),
  //     });

  //     if (isExist) response.isFollowing = true;
  //   }

  //   return response;
  // }

  // async findAllFollowingForId(entityId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // findAllFollowersForId(workspaceId: string) {
  //   return this.drizzleDev.query.workspaceFollowers.findMany({
  //     where: and(
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //     columns: {},
  //     // eslint-disable-next-line @typescript-eslint/no-shadow
  //     orderBy: (workspaceFollowers, { desc }) => [desc(workspaceFollowers.createdAt)],
  //     with: {
  //       workspace: {
  //         columns: {
  //           id: true,
  //           type: true,
  //           workspacename: true,
  //           label: true,
  //         },
  //       },
  //     },
  //   });
  // }

  // async isUserOrWorkspaceFollowingParticularWorkspaceById(entityId: string, workspaceId: string) {
  //   const workspaceFollowerQuery = await this.drizzleDev.query.workspaceFollowers.findFirst({
  //     where: and(
  //       eq(workspaceFollowers.entityId, entityId),
  //       eq(workspaceFollowers.workspaceId, workspaceId),
  //       eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
  //     ),
  //   });

  //   if (workspaceFollowerQuery) return true;

  //   return false;
  // }
}
