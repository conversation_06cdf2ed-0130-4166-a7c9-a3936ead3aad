import { Body, Controller, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { CreateEmployerDto } from './dto/create-employer.dto';

import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';
import { UserRoles } from '@/decorators/user-roles.decorator';

import { AccountSetupStage } from '@/constants/users';
import { Role } from '@/constants/roles';

import { EmployersService } from './employer.service';

@Controller('employer')
@ApiBearerAuth()
@ApiTags('employer')
export class EmployersController {
  constructor(private readonly employerService: EmployersService) {}

  @Post()
  @UserRoles(Role.ALLIED_CARDIAC, Role.CARDIAC_SPECIALIST, Role.ORGANISATION)
  @AccountSetupStages(AccountSetupStage.PROFILE_SETUP)
  async createEmployer(@Body() createEmployerDto: CreateEmployerDto) {
    return this.employerService.createEmployer(createEmployerDto);
  }
}
