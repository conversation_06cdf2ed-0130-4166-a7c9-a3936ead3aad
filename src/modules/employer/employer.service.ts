import { Inject, Injectable } from '@nestjs/common';
import { and, eq, sql, SQL } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import * as schema from '@/db/schema';
import { employers } from '@/db/schema';

import { EntityName } from '@/constants/entities';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { CreateEmployerDto } from './dto/create-employer.dto';
import { EmployersStatus } from '@/constants/employers';
// import { PermissionsService } from '../permissions/permissions.service';

@Injectable()
export class EmployersService {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    // private readonly permissionsService: PermissionsService,
  ) {}

  async createEmployer(createEmployerDto: CreateEmployerDto) {
    const { name, url } = createEmployerDto;

    const existingEmployer = await this.drizzleDev.query.employers.findFirst({
      where: and(eq(sql`lower(${employers.name})`, name.toLowerCase())),
    });

    if (existingEmployer) {
      if (existingEmployer.status === EmployersStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.EMPLOYER);
      } else {
        await this.drizzleDev
          .update(employers)
          .set({
            status: EmployersStatus.ACTIVE,
          })
          .where(eq(employers.id, existingEmployer.id));

        return existingEmployer.id;
      }
    }

    const [newEmployer] = await this.drizzleDev
      .insert(employers)
      .values({
        name,
        url,
        status: EmployersStatus.ACTIVE,
      })
      .returning({ insertedId: employers.id });

    return newEmployer.insertedId;
  }

  async findEmployerById(id: string) {
    return this.findRoleByCondition(eq(employers.id, id));
  }

  private async findRoleByCondition(condition: SQL) {
    const role = await this.drizzleDev.query.roles.findFirst({
      where: and(condition, eq(employers.status, EmployersStatus.ACTIVE)),
    });

    if (!role) throw itemNotFound(EntityName.ROLE);

    return role;
  }
}
