import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsEmail, IsEnum, IsIn, IsNotEmpty, IsString, ValidateIf } from 'class-validator';

import { AccountType } from '@/constants/users';

export class CreateUserRegisterDto {
  @ApiProperty({
    name: 'displayName',
    type: 'string',
    required: true,
    example: 'john doe',
  })
  @IsString()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  displayName: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    name: 'accountType',
    enum: AccountType,
    required: true,
    example: AccountType.ORGANISATION,
  })
  @IsNotEmpty()
  @IsEnum(AccountType)
  @IsIn([AccountType.PROFESSIONAL, AccountType.ORGANISATION, AccountType.PUBLIC], {
    message: 'account type must be either professional, organisation or public',
  })
  accountType: AccountType;

  @ApiProperty({
    name: 'password',
    type: 'string',
    required: false,
    example: 'abcefg',
    minimum: 6,
    description: 'if there is a token, the password will be ignored',
  })
  @ValidateIf((o) => !o.token)
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @IsNotEmpty()
  password?: string;

  @ApiProperty({
    name: 'token',
    type: 'string',
    required: false,
    example:
      'eyJhbGciOiJSUzI1NiIsImtpZCI6IjI3YTc1ZjhkNmQ5ODZmNTA1MzVjOTdjMTliMjA2MzcxNzMwNDA3MTgiLCJ0eXAiOiJKV1QifQ...',
    description: 'if there is a token, the password will be ignored',
  })
  @ValidateIf((o) => !o.password)
  @IsString()
  @IsNotEmpty()
  token?: string;
}
