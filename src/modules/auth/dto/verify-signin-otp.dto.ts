import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';
import { OTP_LENGTH } from '@/constants/otp';

export class VerifySigninOtpDto {
  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    name: 'otp',
    type: 'string',
    required: true,
    example: '123456',
    minLength: OTP_LENGTH,
    maxLength: OTP_LENGTH,
  })
  @IsString()
  @IsNotEmpty()
  @Length(OTP_LENGTH, OTP_LENGTH)
  otp: string;
}
