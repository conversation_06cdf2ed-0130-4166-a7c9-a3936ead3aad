import { forwardRef, Module } from '@nestjs/common';

import { Auth<PERSON>ontroller } from './auth.controller';
import { AuthService } from './auth.service';

import { MailModule } from '@/common/mail/mail.module';

import { CustomConfigService } from '@/config/configuration.service';

import { UsersModule } from '@/modules/users/users.module';
import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { WorkspaceUsersModule } from '@/modules/workspace-users/workspace-users.module';

@Module({
  controllers: [AuthController],
  providers: [CustomConfigService, AuthService],
  imports: [UsersModule, MailModule, WorkspacesModule, forwardRef(() => WorkspaceUsersModule)],
})
export class AuthModule {}
