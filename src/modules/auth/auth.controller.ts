import { <PERSON>, Post, Body, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { getAuth } from 'firebase-admin/auth';
import { CookieOptions, Response } from 'express';

import { CreateUserRegisterDto } from './dto/create-register.dto';
import { SessionLoginDto } from './dto/session-login.dto';
import { GenerateOtpDto } from './dto/generate-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

import { AuthService } from './auth.service';

import { recentSignInRequired } from '@/exceptions/auth';

import { <PERSON>ieKey } from '@/constants/auth';
import { OtpType } from '@/constants/otp';

import { Public } from '@/decorators/public.decorator';
import { User } from '@/decorators/user.decorator';
import { SessionOnly } from '@/decorators/session-only.decorator';
import { TokenOnly } from '@/decorators/token-only.decorator';

import { UserData } from '@/interfaces/auth';

import { CustomConfigService } from '@/config/configuration.service';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly customConfigService: CustomConfigService,
  ) {}

  @Post('register')
  @Public()
  async create(@Body() createRegisterDto: CreateUserRegisterDto) {
    return this.authService.registerUser(createRegisterDto);
  }

  @Post('verify-email')
  @Public()
  async verifyEmail(@Body() verifyOtpDto: VerifyOtpDto) {
    const { email, otp } = verifyOtpDto;
    return this.authService.verifyUserEmail(email, otp);
  }

  @Post('regenerate-otp')
  @Public()
  async generateOtp(@Body() generateOtpDto: GenerateOtpDto) {
    return this.authService.generateOtp(generateOtpDto, OtpType.SIGNUP);
  }

  @Public()
  @Post('session-login')
  async login(@Res({ passthrough: true }) res: Response, @Body() sessionLoginDto: SessionLoginDto) {
    // Verify Firebase ID token
    const { idToken } = sessionLoginDto;
    const expiresIn = 60 * 60 * 24 * 5 * 1000; // 5 days

    const decodedToken = await getAuth().verifyIdToken(idToken, true);

    const currentTime = new Date().getTime() / 1000;
    if (currentTime - decodedToken.auth_time > 5 * 60) {
      throw recentSignInRequired;
    }

    // Create session cookie
    const sessionCookie = await getAuth().createSessionCookie(idToken, { expiresIn });

    const { isLocal } = this.customConfigService.getEnvironment();

    // Set cookie options
    const options: CookieOptions = {
      maxAge: expiresIn,
      httpOnly: true,
      path: '/',
      secure: isLocal === false,
      sameSite: 'lax',
      domain: this.customConfigService.getAppConfig()?.hostDomain,
    };

    res.cookie(CookieKey, sessionCookie, options);
  }

  @SessionOnly()
  @Post('verify-session')
  async checkAuthStatus(@Res({ passthrough: true }) res: Response, @User() user: UserData) {
    try {
      // Generate custom token for cross-domain auth
      const customToken = await getAuth().createCustomToken(user.uid!);

      return { customToken };
    } catch (error) {
      // Clear invalid cookie
      res.clearCookie(CookieKey, {
        domain: this.customConfigService.getAppConfig()?.hostDomain,
      });
      throw new Error(error);
    }
  }

  @TokenOnly()
  @Post('logout')
  async logout(@Res({ passthrough: true }) res: Response, @User() user: UserData) {
    // Revoke all refresh tokens
    await getAuth().revokeRefreshTokens(user.uid!);

    // Clear session cookie
    res.clearCookie(CookieKey, {
      domain: this.customConfigService.getAppConfig()?.hostDomain,
    });
  }
}
