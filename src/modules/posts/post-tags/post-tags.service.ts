import { Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, inArray } from 'drizzle-orm';

import { postTags } from '@/db/schema';
import * as schema from '@/db/schema';
import { PostTagsStatus } from '@/constants/post-tags';

export class PostTagsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async associateTagsWithPost(
    postId: string,
    tagIds: string[],
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // check tag ids exist
    if (tagIds.length === 0) {
      return;
    }

    const postAssociateTags = tagIds.map((tagId) => ({
      postId,
      tagId,
      status: PostTagsStatus.ACTIVE,
    }));

    // Add tag to post
    await queryRunner.insert(postTags).values(postAssociateTags);
  }

  async updatePostTags(
    postId: string,
    newTagIds: string[],
    tx?: PostgresJsDatabase<typeof schema>,
  ): Promise<void> {
    const queryRunner = tx || this.db;

    // Get all current post-tag associations (both active and inactive) in single query
    const currentAssociations = await queryRunner
      .select({
        tagId: postTags.tagId,
        status: postTags.status,
      })
      .from(postTags)
      .where(eq(postTags.postId, postId));

    // Separate active and inactive associations
    const activeTagIds: string[] = [];
    const inactiveTagIds: string[] = [];

    currentAssociations.forEach((association) => {
      if (association.status === PostTagsStatus.ACTIVE) {
        activeTagIds.push(association.tagId);
      } else {
        inactiveTagIds.push(association.tagId);
      }
    });

    // Create sets for efficient lookups
    const newTagIdsSet = new Set(newTagIds);
    const activeTagIdsSet = new Set(activeTagIds);
    const inactiveTagIdsSet = new Set(inactiveTagIds);

    // Determine operations needed
    const tagsToDeactivate = activeTagIds.filter((id) => !newTagIdsSet.has(id));
    const tagsToReactivate = newTagIds.filter((id) => inactiveTagIdsSet.has(id));
    const tagsToCreate = newTagIds.filter(
      (id) => !activeTagIdsSet.has(id) && !inactiveTagIdsSet.has(id),
    );

    // Execute operations in parallel for better performance
    const operations: Promise<any>[] = [];

    // Deactivate tags that are no longer needed
    if (tagsToDeactivate.length > 0) {
      operations.push(
        queryRunner
          .update(postTags)
          .set({ status: PostTagsStatus.INACTIVE })
          .where(
            and(
              eq(postTags.postId, postId),
              inArray(postTags.tagId, tagsToDeactivate),
              eq(postTags.status, PostTagsStatus.ACTIVE),
            ),
          ),
      );
    }

    // Reactivate existing inactive associations
    if (tagsToReactivate.length > 0) {
      operations.push(
        queryRunner
          .update(postTags)
          .set({ status: PostTagsStatus.ACTIVE })
          .where(
            and(
              eq(postTags.postId, postId),
              inArray(postTags.tagId, tagsToReactivate),
              eq(postTags.status, PostTagsStatus.INACTIVE),
            ),
          ),
      );
    }

    // Create new associations
    if (tagsToCreate.length > 0) {
      const newAssociations = tagsToCreate.map((tagId) => ({
        postId,
        tagId,
        status: PostTagsStatus.ACTIVE,
      }));

      operations.push(queryRunner.insert(postTags).values(newAssociations));
    }

    await Promise.all(operations);
  }
}
