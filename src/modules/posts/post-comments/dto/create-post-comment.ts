import { ApiProperty } from '@nestjs/swagger';
import { Transform, TransformFnParams } from 'class-transformer';
import { IsString, IsNotEmpty, MaxLength, MinLength } from 'class-validator';

import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

export class CreateOrUpdatePostCommentDto {
  @ApiProperty({
    name: 'comment',
    type: 'string',
    required: true,
    example: 'very informative',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  comment: string;
}
