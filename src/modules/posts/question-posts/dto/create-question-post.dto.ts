import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';

import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

export class CreateQuestionPostDto extends CommonPostCreateDto {
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  question: string;
}
