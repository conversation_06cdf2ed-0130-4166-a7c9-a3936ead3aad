import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, inArray } from 'drizzle-orm';

import { MediaItemDto } from './dto/create-post-media.dto';

import * as schema from '@/db/schema';
import { postMedias, NewPostMedia } from '@/db/schema/post-medias';

import { PostMediaStatus } from '@/constants/post-media';

@Injectable()
export class PostMediasService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createPostMedia(
    postId: string,
    postMediaData: MediaItemDto,
    tx?: PostgresJsDatabase<typeof schema>,
    isCoverPic: boolean = false,
  ) {
    const queryRunner = tx || this.db;

    // Insert the post media
    const [postMedia] = await queryRunner
      .insert(postMedias)
      .values({
        ...postMediaData,
        postId,
        isCoverPic,
        status: PostMediaStatus.ACTIVE,
      })
      .returning();

    return postMedia;
  }

  async createMultiplePostMedia(
    postId: string,
    mediaItems: MediaItemDto[],
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    if (!mediaItems.length) {
      return [];
    }

    // Prepare media items with postId and order
    const postMediaItems: NewPostMedia[] = mediaItems.map((item, index) => ({
      ...item,
      postId,
      status: PostMediaStatus.ACTIVE,
      order: item.order ?? index, // Set order based on array index
    }));

    // Insert all media items
    const postMedia = await queryRunner.insert(postMedias).values(postMediaItems).returning();

    return postMedia;
  }

  async updateMultiplePostMedias(
    postId: string,
    newMediaItems: MediaItemDto[],
    tx?: PostgresJsDatabase<typeof schema>,
  ): Promise<void> {
    const queryRunner = tx || this.db;

    // Get all current post-media associations
    const currentMedias = await queryRunner
      .select({
        id: postMedias.id,
        mediaPath: postMedias.mediaPath,
        order: postMedias.order,
      })
      .from(postMedias)
      .where(
        and(
          eq(postMedias.postId, postId),
          eq(postMedias.isCoverPic, false),
          eq(postMedias.status, PostMediaStatus.ACTIVE),
        ),
      )
      .orderBy(postMedias.order);

    // Create efficient lookup maps - O(n) time, O(n) space
    const currentMediaMap = new Map(currentMedias.map((item) => [item.mediaPath, item]));

    // Create new media path to order map
    const newMediaOrderMap = new Map(newMediaItems.map((item, index) => [item.mediaPath, index]));

    const newMediaPathsSet = new Set(newMediaItems.map((item) => item.mediaPath));

    // Determine operations
    const pathsToDeactivate: string[] = [];
    const mediaToUpdateOrder: { id: string; newOrder: number }[] = [];
    const newMediaToCreate: (MediaItemDto & { order: number })[] = [];

    // Process current media
    for (const currentMedia of currentMedias) {
      if (!newMediaPathsSet.has(currentMedia.mediaPath)) {
        // Media no longer exists in new list
        pathsToDeactivate.push(currentMedia.mediaPath);
      } else {
        // Media exists, check if order changed
        const newOrder = newMediaOrderMap.get(currentMedia.mediaPath)!;
        if (currentMedia.order !== newOrder) {
          mediaToUpdateOrder.push({
            id: currentMedia.id,
            newOrder,
          });
        }
      }
    }

    // Process new media items
    for (let i = 0; i < newMediaItems.length; i++) {
      const newItem = newMediaItems[i];
      if (!currentMediaMap.has(newItem.mediaPath)) {
        newMediaToCreate.push({
          ...newItem,
          order: i,
        });
      }
    }

    // Execute database operations in parallel for better performance
    const operations: Promise<any>[] = [];

    // 1. Deactivate removed media - Single batch operation
    if (pathsToDeactivate.length > 0) {
      operations.push(
        queryRunner
          .update(postMedias)
          .set({ status: PostMediaStatus.INACTIVE })
          .where(
            and(
              eq(postMedias.postId, postId),
              inArray(postMedias.mediaPath, pathsToDeactivate),
              eq(postMedias.status, PostMediaStatus.ACTIVE),
            ),
          ),
      );
    }

    // 2. Update orders for existing media - Individual updates but in parallel
    if (mediaToUpdateOrder.length > 0) {
      const orderUpdatePromises = mediaToUpdateOrder.map(({ id, newOrder }) =>
        queryRunner.update(postMedias).set({ order: newOrder }).where(eq(postMedias.id, id)),
      );

      operations.push(...orderUpdatePromises);
    }

    // 3. Create new media items - Single batch insert
    if (newMediaToCreate.length > 0) {
      const postMediaItems: NewPostMedia[] = newMediaToCreate.map((item) => ({
        ...item,
        postId,
        status: PostMediaStatus.ACTIVE,
      }));

      operations.push(queryRunner.insert(postMedias).values(postMediaItems));
    }

    // Execute all operations in parallel
    if (operations.length > 0) {
      await Promise.all(operations);
    }
  }

  async deletePostMedia(mediaPath: string, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    await queryRunner
      .update(postMedias)
      .set({ status: PostMediaStatus.INACTIVE })
      .where(eq(postMedias.mediaPath, mediaPath));

    return true;
  }
}
