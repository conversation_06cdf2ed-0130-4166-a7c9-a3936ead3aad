import {
  IsIn,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { MAX_LENGTH, MEDIA_TYPES_INFO, MIN_LENGTH } from '@/constants/posts';

export class MediaItemDto {
  @ApiProperty({
    name: 'mediaPath',
    type: 'string',
    required: true,
    example: '/uploads/images/sample.png',
  })
  @IsString()
  @IsNotEmpty()
  mediaPath: string;

  @ApiProperty({
    name: 'mediaType',
    enum: [
      ...MEDIA_TYPES_INFO.image.accept,
      ...MEDIA_TYPES_INFO.video.accept,
      ...MEDIA_TYPES_INFO.document.accept,
    ],
    example: MEDIA_TYPES_INFO.image.accept[0],
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsIn([
    ...MEDIA_TYPES_INFO.image.accept,
    ...MEDIA_TYPES_INFO.video.accept,
    ...MEDIA_TYPES_INFO.document.accept,
  ])
  mediaType: string;

  @ApiProperty({
    name: 'altText',
    type: 'string',
    required: false,
    example: 'A beautiful landscape image',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  altText?: string;

  @ApiProperty({
    name: 'order',
    type: 'number',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  order?: number;
}
