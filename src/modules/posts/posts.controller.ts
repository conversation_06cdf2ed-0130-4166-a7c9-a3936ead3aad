import { Body, Controller, Delete, Get, Inject, Param, Patch, Post, Query } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { ApiTags } from '@nestjs/swagger';

import * as schema from '@/db/schema';
import { textPosts } from '@/db/schema/text-posts';
import { questionPosts } from '@/db/schema/question-posts';
import { articlePosts } from '@/db/schema/article-posts';
import { mediaPosts } from '@/db/schema/media-posts';

import { User } from '@/decorators/user.decorator';
import { ActiveWorkspace } from '@/decorators/active-workspace.decorator';
import { Public } from '@/decorators/public.decorator';

import {
  invalidPostStatusTransitionException,
  postScheduleDateMinimum10MinutesRequired,
} from '@/exceptions/posts';
import { unauthorized } from '@/exceptions/system';
import { itemNotFound } from '@/exceptions/common';

import { CreateTextPostDto } from './text-posts/dto/create-text-post.dto';
import { CreateMediaPostDto } from './media-posts/dto/create-media-post.dto';
import { CreateArticlePostDto } from './article-posts/dto/create-article-post.dto';
import { CreateQuestionPostDto } from './question-posts/dto/create-question-post.dto';
import { UpdateTextPostDto } from './text-posts/dto/update-text-post.dto';
import { UpdateCommonPostCreateDto } from './dto/update-post.dto';
import { UpdateQuestionPostDto } from './question-posts/dto/update-question-post.dto';
import { UpdateArticlePostDto } from './article-posts/dto/update-article-post.dto';
import { UpdateMediaPostDto } from './media-posts/dto/update-media-post.dto';
import { CreateOrUpdatePostCommentDto } from './post-comments/dto/create-post-comment';
import { FeedFetchPostDto, TagFeedFetchPostDto } from './dto/get-post.dto';

import { extractImagePathsFromRichText } from '@/utils/rich-text';
import { guessMediaTypeFromPath } from '@/utils/files';
import { sanitizeContent } from '@/utils/sanitize';

import { PrimarySpeciality } from '@/constants/workspaces';
import { AuthConstants } from '@/constants/auth';
import { PostActiveStatus, PostStatus, PostType } from '@/constants/posts';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { ENABLE_PUBLIC_FEED_ACCESS } from '@/constants/system';

import { TagsService } from '@/modules/tags/tags.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UsersService } from '@/modules/users/users.service';
import { PostsService } from './posts.service';
import { TextPostsService } from './text-posts/text-posts.service';
import { MediaPostsService } from './media-posts/media-posts.service';
import { ArticlePostsService } from './article-posts/article-posts.service';
import { PostTagsService } from './post-tags/post-tags.service';
import { PostMediasService } from './media/post-medias.service';
import { QuestionPostsService } from './question-posts/question-posts.service';
import { PostLikesService } from './post-likes/post-likes.service';
import { PostCommentsService } from './post-comments/post-comments.service';

@ApiTags('posts')
@Controller('posts')
export class PostsController {
  constructor(
    private readonly postsService: PostsService,
    private readonly textPostsService: TextPostsService,
    private readonly mediaPostsService: MediaPostsService,
    private readonly articlePostsService: ArticlePostsService,
    private readonly postTagsService: PostTagsService,
    private readonly tagsService: TagsService,
    private readonly workspacesService: WorkspacesService,
    private readonly postMediasService: PostMediasService,
    private readonly questionPostsService: QuestionPostsService,
    private readonly usersService: UsersService,
    private readonly postLikesService: PostLikesService,
    private readonly postCommentsService: PostCommentsService,
    @Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>,
  ) {}

  @Get('feed')
  @Public()
  async findAll(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: FeedFetchPostDto,
  ) {
    if (!ENABLE_PUBLIC_FEED_ACCESS && !userId) {
      throw unauthorized();
    }

    const { limit, offset, searchKeyword, postType } = query;

    const response = await this.postsService.findAll({
      limit,
      offset,
      searchKeyword,
      postType,
      entityId: workspaceId ?? userId,
    });

    return response;
  }

  @Get('tag/:tagName')
  @Public()
  async findByTag(
    @Param('tagName') tagName: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
    @Query() query: TagFeedFetchPostDto,
  ) {
    if (!ENABLE_PUBLIC_FEED_ACCESS && !userId) {
      throw unauthorized();
    }

    const { limit, offset, searchKeyword, postType } = query;

    const response = await this.postsService.findAllByTag({
      tagName,
      limit,
      offset,
      searchKeyword,
      postType,
      entityId: workspaceId ?? userId,
    });

    return response;
  }

  @Post('text')
  @ActiveWorkspace()
  async createTextPost(
    @Body() createTextPostDto: CreateTextPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { content, audience, tags } = createTextPostDto;

    const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
      workspaceId,
      true,
    );

    // CHECK THE WORKSPACE COMPLETED THE ONBOARDING PROCESS IF NEEDS

    // TODO: check has the permission to create post for community which is passed

    // check has the permission to create post for audience which is passed
    await this.postsService.checkAudiencePermission(
      workspacesDetails?.primarySpeciality as PrimarySpeciality,
      audience,
    );

    // start transaction
    const postId = await this.db.transaction(async (tx) => {
      // Create the tags
      const newTags = await this.tagsService.createMissingTags(tags, tx);

      // Create the post
      const newPost = await this.postsService.createPost(
        {
          ...createTextPostDto,
          publishedById: publisherId,
          updatedById: publisherId,
          workspaceId,
          status: PostActiveStatus.ACTIVE,
          postType: PostType.TEXT,
          tags: newTags.map((tag) => tag.name),
        },
        tx,
      );

      // Associate tags with the post
      await this.postTagsService.associateTagsWithPost(
        newPost.id,
        newTags.map((tag) => tag.id),
        tx,
      );

      // Create the text post
      await this.textPostsService.createTextPost(
        {
          content,
          postId: newPost.id,
        },
        tx,
      );

      return newPost.id;
    });

    return postId;
  }

  @Post('media')
  @ActiveWorkspace()
  async createMediaPost(
    @Body() createMediaPostDto: CreateMediaPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { caption, audience, tags, medias } = createMediaPostDto;

    const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
      workspaceId,
      true,
    );

    // CHECK THE WORKSPACE COMPLETED THE ONBOARDING PROCESS IF NEEDS

    // TODO: check has the permission to create post for community which is passed

    // check has the permission to create post for audience which is passed
    await this.postsService.checkAudiencePermission(
      workspacesDetails?.primarySpeciality as PrimarySpeciality,
      audience,
    );

    // start transaction
    const postId = await this.db.transaction(async (tx) => {
      // Create the tags
      const newTags = await this.tagsService.createMissingTags(tags, tx);

      // Create the post
      const newPost = await this.postsService.createPost(
        {
          ...createMediaPostDto,
          publishedById: publisherId,
          updatedById: publisherId,
          workspaceId,
          status: PostActiveStatus.ACTIVE,
          postType: PostType.MEDIA,
          tags: newTags.map((tag) => tag.name),
        },
        tx,
      );

      // Associate tags with the post
      await this.postTagsService.associateTagsWithPost(
        newPost.id,
        newTags.map((tag) => tag.id),
        tx,
      );

      // Create the media post
      await this.mediaPostsService.createMediaPost(
        {
          caption,
          postId: newPost.id,
        },
        tx,
      );

      // Associate medias with the post
      await this.postMediasService.createMultiplePostMedia(newPost.id, medias, tx);

      return newPost.id;
    });

    return postId;
  }

  @Post('article')
  @ActiveWorkspace()
  async createArticlePost(
    @Body() createArticlePostDto: CreateArticlePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { title, summary, coverImagePath, audience, tags } = createArticlePostDto;
    // Sanitize the content to prevent XSS attacks
    const content = sanitizeContent(createArticlePostDto.content);

    const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
      workspaceId,
      true,
    );

    // Check audience permission
    await this.postsService.checkAudiencePermission(
      workspacesDetails?.primarySpeciality as PrimarySpeciality,
      audience,
    );

    // Extract image URLs from rich text content (for storing as media)
    const imagePathsFromContent = extractImagePathsFromRichText(content);

    // start transaction
    const postId = await this.db.transaction(async (tx) => {
      // Create the tags
      const newTags = await this.tagsService.createMissingTags(tags, tx);

      // Create the post
      const newPost = await this.postsService.createPost(
        {
          ...createArticlePostDto,
          publishedById: publisherId,
          updatedById: publisherId,
          workspaceId,
          status: PostActiveStatus.ACTIVE,
          postType: PostType.ARTICLE,
          tags: newTags.map((tag) => tag.name),
        },
        tx,
      );

      // Associate tags with the post
      await this.postTagsService.associateTagsWithPost(
        newPost.id,
        newTags.map((tag) => tag.id),
        tx,
      );

      // Create the article post
      await this.articlePostsService.createArticlePost(
        {
          title,
          summary,
          content,
          coverImagePath,
          postId: newPost.id,
        },
        tx,
      );

      // Store all embedded images as post media for tracking
      if (imagePathsFromContent.length > 0) {
        const mediaItems = imagePathsFromContent.map((path) => ({
          mediaPath: path,
          mediaType: guessMediaTypeFromPath(path),
        }));

        await this.postMediasService.createMultiplePostMedia(newPost.id, mediaItems, tx);
      }

      // If there's a cover image, store it as post media too
      if (coverImagePath) {
        await this.postMediasService.createPostMedia(
          newPost.id,
          {
            mediaPath: coverImagePath,
            mediaType: guessMediaTypeFromPath(coverImagePath),
            altText: `${title} cover image`,
          },
          tx,
          true, // to track it as cover pic
        );
      }

      return newPost.id;
    });

    return postId;
  }

  @Post('question')
  @ActiveWorkspace()
  async createQuestionPost(
    @Body() createQuestionPostDto: CreateQuestionPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) publisherId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { question, audience, tags } = createQuestionPostDto;

    const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
      workspaceId,
      true,
    );

    // CHECK THE WORKSPACE COMPLETED THE ONBOARDING PROCESS IF NEEDS

    // TODO: check has the permission to create post for community which is passed

    // check has the permission to create post for audience which is passed
    await this.postsService.checkAudiencePermission(
      workspacesDetails?.primarySpeciality as PrimarySpeciality,
      audience,
    );

    // start transaction
    const postId = await this.db.transaction(async (tx) => {
      // Create the tags
      const newTags = await this.tagsService.createMissingTags(tags, tx);

      // Create the post
      const newPost = await this.postsService.createPost(
        {
          ...createQuestionPostDto,
          publishedById: publisherId,
          updatedById: publisherId,
          workspaceId,
          status: PostActiveStatus.ACTIVE,
          postType: PostType.QUESTION,
          tags: newTags.map((tag) => tag.name),
        },
        tx,
      );

      // Associate tags with the post
      await this.postTagsService.associateTagsWithPost(
        newPost.id,
        newTags.map((tag) => tag.id),
        tx,
      );

      // Create the question post
      await this.questionPostsService.createQuestionPost(
        {
          question,
          postId: newPost.id,
        },
        tx,
      );

      return newPost.id;
    });

    return postId;
  }

  @Patch('text/:postId')
  @ActiveWorkspace()
  async updateTextPost(
    @Param('postId') postId: string,
    @Body() updateTextPostDto: UpdateTextPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID)
    userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags } = updateTextPostDto;

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        { postStatus, postScheduleDate, audience, community, tags },
        tx,
      );

      // Get the keys from UpdateTextPostDto to check for text post specific updates
      const textPostFields = Object.keys(updateTextPostDto).filter((key) => key in textPosts);

      if (textPostFields.length > 0) {
        await this.textPostsService.updateTextPost(postId, updateTextPostDto, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('question/:postId')
  @ActiveWorkspace()
  async updateQuestionPost(
    @Param('postId') postId: string,
    @Body() updateQuestionPost: UpdateQuestionPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID)
    userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags } = updateQuestionPost;

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        { postStatus, postScheduleDate, audience, community, tags },
        tx,
      );

      // Get the keys from UpdateTextPostDto to check for text post specific updates
      const questionPostFields = Object.keys(updateQuestionPost).filter(
        (key) => key in questionPosts,
      );

      if (questionPostFields.length > 0) {
        await this.questionPostsService.updateQuestionPost(postId, updateQuestionPost, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('article/:postId')
  @ActiveWorkspace()
  async updateArticlePost(
    @Param('postId') postId: string,
    @Body() updateArticlePost: UpdateArticlePostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags } = updateArticlePost;

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        { postStatus, postScheduleDate, audience, community, tags },
        tx,
      );

      // Get the keys from UpdateTextPostDto to check for text post specific updates
      const articlePostFields = Object.keys(updateArticlePost).filter((key) => key in articlePosts);

      if (articlePostFields.length > 0) {
        const { content, coverImagePath } = updateArticlePost;

        if (content) {
          const sanitizedContent = sanitizeContent(content);

          const imagePathsFromContent = extractImagePathsFromRichText(sanitizedContent);

          // Store all embedded images as post media for tracking
          const mediaItems = imagePathsFromContent.map((path) => ({
            mediaPath: path,
            mediaType: guessMediaTypeFromPath(path),
          }));

          await this.postMediasService.updateMultiplePostMedias(postId, mediaItems, tx);
        }

        if (coverImagePath) {
          const articlePostDetails = await this.articlePostsService.findAriticlePostById(postId);

          // only do operation if it's a new cover image
          if (articlePostDetails?.coverImagePath !== coverImagePath) {
            // there may have chance to not have any cover image previously. so only delete if existed before.
            if (articlePostDetails?.coverImagePath)
              await this.postMediasService.deletePostMedia(articlePostDetails.coverImagePath);

            await this.postMediasService.createPostMedia(
              postId,
              {
                mediaPath: coverImagePath,
                mediaType: guessMediaTypeFromPath(coverImagePath),
                altText: `${articlePostDetails?.title} cover image`,
              },
              tx,
              true, // to track it as cover pic
            );
          }
        }

        await this.articlePostsService.updateArticlePost(postId, updateArticlePost, tx);
      }

      return updatedPostBase;
    });
  }

  @Patch('media/:postId')
  @ActiveWorkspace()
  async updateMediaPost(
    @Param('postId') postId: string,
    @Body() updateMediaPost: UpdateMediaPostDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags, medias, caption } =
      updateMediaPost;

    // Start transaction
    return this.db.transaction(async (tx) => {
      // Handle common post update logic
      const updatedPostBase = await this.handlePostUpdate(
        postId,
        userId,
        workspaceId,
        { postStatus, postScheduleDate, audience, community, tags },
        tx,
      );

      // Get the keys from UpdateMediaPostDto to check for media post specific updates
      const mediaPostFields = Object.keys(updateMediaPost).filter((key) => key in mediaPosts);

      if (mediaPostFields.length > 0) {
        // Update caption if provided
        if (caption !== undefined) {
          await this.mediaPostsService.updateMediaPost(postId, { caption }, tx);
        }

        // Update media items if provided
        if (medias) {
          await this.postMediasService.updateMultiplePostMedias(postId, medias, tx);
        }
      }

      return updatedPostBase;
    });
  }

  @Get('drafts')
  @ActiveWorkspace()
  async getDraftPosts(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    return this.postsService.getDraftPosts(workspaceId);
  }

  @Get('scheduled')
  @ActiveWorkspace()
  async getScheduledPosts(
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID)
    workspaceId: string,
  ) {
    return this.postsService.getScheduledPosts(workspaceId);
  }

  private async handlePostUpdate(
    postId: string,
    userId: string,
    workspaceId: string,
    updateData: UpdateCommonPostCreateDto,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const { postStatus, postScheduleDate, audience, community, tags } = updateData;

    // Get the post to check if it belongs to this workspace
    const post = await this.postsService.getPostById(postId);

    if (post.workspaceId !== workspaceId) {
      throw unauthorized();
    }

    // Check if post is already live (published or scheduled time has passed)
    const now = new Date();
    const existingScheduledDate = new Date(post.postScheduleDate);

    const isPostLive =
      post.postStatus === PostStatus.PUBLISHED ||
      (post.postStatus === PostStatus.SCHEDULED && existingScheduledDate < now);

    // If trying to update status or schedule date, check if post is already live
    if ((postStatus || postScheduleDate) && isPostLive) {
      throw invalidPostStatusTransitionException();
    }

    let newPostScheduledDate: Date | undefined;
    if (postStatus || postScheduleDate) {
      // updation
      if ([PostStatus.DRAFT, PostStatus.PUBLISHED].includes(postStatus as PostStatus)) {
        newPostScheduledDate = new Date();
      } else if (postStatus === PostStatus.SCHEDULED) {
        // since there is a validation inside the dto, the schedule post will be there
        newPostScheduledDate = postScheduleDate;
      } else if (!postStatus && postScheduleDate) {
        // check in case if they didnt passed the post status, but only scheudled date. then update it if it's only scheduled post.
        if (post.postStatus === PostStatus.SCHEDULED) {
          const scheduleDate = new Date(postScheduleDate);
          const currentTime = new Date();
          currentTime.setMinutes(now.getMinutes() + 10); // Ensure it's at least 10 minutes later
          if (scheduleDate < currentTime) throw postScheduleDateMinimum10MinutesRequired();
          newPostScheduledDate = postScheduleDate;
        } else {
          // incase it's draft post
          newPostScheduledDate = postScheduleDate;
        }
      }
    }

    // Check audience permission if changing audience
    if (audience) {
      const workspacesDetails = await this.workspacesService.findOneWorkspaceByWorkspaceId(
        workspaceId,
        true,
      );

      await this.postsService.checkAudiencePermission(
        workspacesDetails?.primarySpeciality as PrimarySpeciality,
        audience,
      );
    }

    // Handle tags if provided
    const newTags = tags ? await this.tagsService.createMissingTags(tags, tx) : [];

    // Update post tags
    await this.postTagsService.updatePostTags(
      postId,
      newTags.map((tag) => tag.id),
      tx,
    );

    // Update the base post
    return this.postsService.updatePost(
      postId,
      {
        postStatus,
        postScheduleDate: newPostScheduledDate,
        audience,
        community,
        tags: tags ? newTags.map((tag) => tag.name) : undefined,
      },
      userId,
      tx,
    );
  }

  // POST LIKES 👍
  @Post('likes/:postId')
  @ActiveWorkspace()
  async createPostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    const isExist = await (workspaceId
      ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
      : this.usersService.findUserById(userId));

    if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

    return this.db.transaction(async (txn) => {
      const likeData = await this.postLikesService.createPostLike(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        txn,
      );

      // const postDetails = await this.postsService.getPostById(postId);

      // TODO: NOTIFICATION CAN BE HANDLED LATER
      // if (postDetails.workspaceId !== workspaceId) {
      //   await this.notificationService.createLikeNotification(
      //     {
      //       entityId: workspaceId ?? userId,
      //       entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
      //       postId,
      //       type: PostNotificationsType.LIKE,
      //     },
      //     txn,
      //   );
      // }

      return likeData;
    });
  }

  @Delete('likes/:postId')
  @ActiveWorkspace()
  softDeletePostLike(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.db.transaction(async (txn) => {
      const likeData = await this.postLikesService.softDeletePostLike(
        postId,
        workspaceId ?? userId,
        txn,
      );

      // TODO: NOTIFICATION CAN BE HANDLED LATER
      // await this.notificationService.invalidatePostNotification(
      //   {
      //     entityId: workspaceId ?? userId,
      //     postId: likeData.postId,
      //     type: PostNotificationsType.LIKE,
      //   },
      //   txn,
      // );

      return likeData;
    });
  }

  // Post Comments 💬
  @Post('comments/:postId')
  @ActiveWorkspace()
  createPostComment(
    @Param('postId') postId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() createPostCommentDto: CreateOrUpdatePostCommentDto,
  ) {
    return this.db.transaction(async (txn) => {
      const isExist = await (workspaceId
        ? this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true)
        : this.usersService.findUserById(userId));

      if (!isExist) itemNotFound(workspaceId ? EntityName.WORKSPACE : EntityName.USER);

      const commentData = await this.postCommentsService.createPostComment(
        postId,
        workspaceId ?? userId,
        workspaceId ? EntityType.WORKSPACE : EntityType.USER,
        createPostCommentDto.comment,
        txn,
      );

      /*    
      TODO: NOTIFICATION CAN BE HANDLED LATER

      const postDetails = await this.postsService.getPostById(postId);

      if (postDetails.workspaceId !== workspaceId) {
        await this.notificationService.createCommentNotification(
          {
            entityId: workspaceId ?? userId,
            entityType: workspaceId ? EntityType.WORKSPACE : EntityType.USER,
            postId,
            type: PostNotificationsType.COMMENT,
            commentId: commentData.id,
          },
          txn,
        );
      }  */

      return commentData;
    });
  }

  @Patch('comments/:commentId')
  @ActiveWorkspace()
  updatePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() updatePostCommentDto: CreateOrUpdatePostCommentDto,
  ) {
    return this.postCommentsService.updatePostComment(
      commentId,
      workspaceId ?? userId,
      updatePostCommentDto.comment,
    );
  }

  @Delete('comments/:commentId')
  @ActiveWorkspace()
  softDeletePostComment(
    @Param('commentId') commentId: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  ) {
    return this.db.transaction(async (txn) => {
      const commentData = await this.postCommentsService.softDeletePostComment(
        commentId,
        workspaceId ?? userId,
        txn,
      );

      /* 
      TODO: NOTIFICATION CAN BE HANDLED LATER
      await this.notificationService.invalidatePostNotification(
        {
          entityId: workspaceId ?? userId,
          postId: commentData.postId,
          type: PostNotificationsType.COMMENT,
          commentId: commentData.id,
        },
        txn,
      ); */

      return commentData;
    });
  }
}
