import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { mediaPosts, NewMediaPost } from '@/db/schema/media-posts';

@Injectable()
export class MediaPostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createMediaPost(postData: NewMediaPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    // Insert the post
    const post = await queryRunner.insert(mediaPosts).values(postData).returning();

    return post;
  }

  async updateMediaPost(
    postId: string,
    updateData: Partial<NewMediaPost>,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    await queryRunner.update(mediaPosts).set(updateData).where(eq(mediaPosts.postId, postId));
  }
}
