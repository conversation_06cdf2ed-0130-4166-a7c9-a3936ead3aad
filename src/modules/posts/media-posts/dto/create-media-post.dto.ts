import {
  Is<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
  ValidateNested,
  ArrayMinSize,
  ArrayMaxSize,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';
import { MediaItemDto } from '@/modules/posts/media/dto/create-post-media.dto';

import { MAX_FILES, MAX_LENGTH, MIN_FILES, MIN_LENGTH } from '@/constants/posts';

export class CreateMediaPostDto extends CommonPostCreateDto {
  @ApiProperty({
    name: 'caption',
    type: 'string',
    required: true,
    example: 'Check out this amazing view!',
  })
  @IsString()
  @IsOptional()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  caption: string;

  @ApiProperty({
    name: 'medias',
    type: [MediaItemDto],
    required: true,
    example: [
      {
        mediaPath: '/uploads/images/sample1.png',
        mediaType: 'image/png',
        altText: 'A beautiful landscape image',
      },
      {
        mediaPath: '/uploads/videos/sample2.mp4',
        mediaType: 'video/mp4',
        altText: 'A short video clip',
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(MIN_FILES)
  @ArrayMaxSize(MAX_FILES)
  @ValidateNested({ each: true })
  @Type(() => MediaItemDto)
  medias: MediaItemDto[];
}
