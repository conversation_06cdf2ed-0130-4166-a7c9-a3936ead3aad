import {
  <PERSON><PERSON>otE<PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';
import {
  ARTICLE_CONTENT_MAX_LENGTH,
  ARTICLE_CONTENT_MIN_LENGTH,
  MAX_LENGTH,
  MIN_LENGTH,
  MEDIA_CATEGORY,
  MEDIA_TYPES_INFO,
  MAX_FILES,
} from '@/constants/posts';
import { extractImagePathsFromRichText } from '@/utils/rich-text';

export function IsImagePath(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isImagePath',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (typeof value !== 'string' || !value) return true; // Skip validation if empty (handled by @IsOptional)

          // Get the file extension from the path
          const extension = value.split('.').pop()?.toLowerCase();
          if (!extension) return false;

          // Check if the extension matches any of the allowed image extensions
          const imageExtensions = Object.values(MEDIA_TYPES_INFO[MEDIA_CATEGORY.IMAGE].extensions);

          return imageExtensions.includes(extension);
        },
        defaultMessage(args: ValidationArguments) {
          const imageExtensions = Object.values(
            MEDIA_TYPES_INFO[MEDIA_CATEGORY.IMAGE].extensions,
          ).join(', ');

          return `${args.property} must be a valid image path with one of these extensions: ${imageExtensions}`;
        },
      },
    });
  };
}

export function MaxArticleMedia(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'maxArticleMedia',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (typeof value !== 'string') return true;

          // Extract image URLs from the rich text content
          const imageUrls = extractImagePathsFromRichText(value);

          // total media count
          const totalMediaCount = imageUrls.length;

          // Check if total media count is within limits
          return totalMediaCount <= MAX_FILES;
        },
        defaultMessage() {
          return `Article content contains too many media items. Maximum allowed is ${MAX_FILES}.`;
        },
      },
    });
  };
}

export class CreateArticlePostDto extends CommonPostCreateDto {
  @ApiProperty({
    name: 'title',
    type: 'string',
    required: true,
    example: 'Hello World',
    description: `Title of the article with a minimum length of ${MIN_LENGTH} and a maximum length of ${MAX_LENGTH}`,
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  title: string;

  @ApiProperty({
    name: 'summary',
    type: 'string',
    required: true,
    example: 'hello world is a great place to start',
    description: `A brief summary of the article content with a minimum length of ${ARTICLE_CONTENT_MIN_LENGTH} and a maximum length of ${ARTICLE_CONTENT_MAX_LENGTH}`,
  })
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  summary: string;

  @ApiProperty({
    name: 'content',
    type: 'string',
    required: true,
    example:
      '<h1>Hello world</h1><p>its a great place to start coding</p><h2>heading 2</h2><img src="https://assets.dev.minicardiac.com/uploads/images/sample1.png" /><p>heading 3</p><ol><li>one</li><li>two</li><li>three</li><li>four</li><li>five</li></ol><p><br></p><ul><li>point 1</li><li>point 2</li><li>point 3</li><li>point 4</li><li>point 5</li></ul>',
    description: `content must be between ${ARTICLE_CONTENT_MIN_LENGTH} and ${ARTICLE_CONTENT_MAX_LENGTH} characters`,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(ARTICLE_CONTENT_MIN_LENGTH)
  @MaxLength(ARTICLE_CONTENT_MAX_LENGTH)
  @MaxArticleMedia()
  content: string;

  @ApiProperty({
    name: 'coverImagePath',
    type: 'string',
    required: false,
    example: '/uploads/images/article-cover.jpg',
    description: `Cover image must be one of these types: ${Object.keys(MEDIA_TYPES_INFO[MEDIA_CATEGORY.IMAGE].extensions).join(', ')}`,
  })
  @IsOptional()
  @IsString()
  @IsImagePath()
  coverImagePath?: string;
}
