import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { articlePosts, NewArticlePost } from '@/db/schema/article-posts';

import { itemNotFound } from '@/exceptions/common';

import { ENTITY_TYPES } from '@/constants/posts';

@Injectable()
export class ArticlePostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createArticlePost(postData: NewArticlePost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    // Insert the post
    const articlePost = await queryRunner.insert(articlePosts).values(postData).returning();

    return articlePost;
  }

  findAriticlePostById(postId: string) {
    const articlePostDetails = this.db.query.articlePosts.findFirst({
      where: and(eq(articlePosts.postId, postId)),
    });

    if (!articlePostDetails) throw itemNotFound(ENTITY_TYPES.ARTICLE);

    return articlePostDetails;
  }

  async updateArticlePost(
    postId: string,
    postData: Partial<NewArticlePost>,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    const [updatedArticlePost] = await queryRunner
      .update(articlePosts)
      .set(postData)
      .where(eq(articlePosts.postId, postId))
      .returning();

    return updatedArticlePost;
  }
}
