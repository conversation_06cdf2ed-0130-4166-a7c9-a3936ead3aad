import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { textPosts, NewTextPost } from '@/db/schema/text-posts';

@Injectable()
export class TextPostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createTextPost(postData: NewTextPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    // Insert the post
    const post = await queryRunner.insert(textPosts).values(postData).returning();

    return post;
  }

  async updateTextPost(
    postId: string,
    postData: Partial<NewTextPost>,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    const [updatedTextPost] = await queryRunner
      .update(textPosts)
      .set(postData)
      .where(eq(textPosts.postId, postId))
      .returning();

    return updatedTextPost;
  }
}
