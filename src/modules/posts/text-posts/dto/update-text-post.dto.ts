import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsDate, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';

import { PostStatus } from '@/constants/posts';

import { CreateTextPostDto } from './create-text-post.dto';
import { IsFutureDateIfScheduled } from 'src/modules/posts/dto/create-post.dto';

export class UpdateTextPostDto extends PartialType(
  OmitType(CreateTextPostDto, ['postScheduleDate']),
) {
  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false, // marked as not required here but validated conditionally
    example: '2024-12-31T23:59:59Z',
    description:
      'Required when postStatus is SCHEDULED. If publishing immediately, current date will be used.',
  })
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED)
  @IsDate()
  @IsFutureDateIfScheduled()
  @Transform(({ value }) => {
    if (value) {
      return new Date(value);
    }
  })
  postScheduleDate?: Date;
}
