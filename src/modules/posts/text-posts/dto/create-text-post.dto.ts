import { <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

import { CommonPostCreateDto } from '@/modules/posts/dto/create-post.dto';

import { MAX_LENGTH, MIN_LENGTH } from '@/constants/posts';

// create dto by extending create-post.dto.ts
export class CreateTextPostDto extends CommonPostCreateDto {
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsNotEmpty()
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  content: string;
}
