import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';

import { UserSegment } from '@/constants/user-segments';

export class GetDocumentRequirementsDto {
  @ApiProperty({
    name: 'userSegment',
    description: 'The specific workspace type',
    enum: UserSegment,
    example: UserSegment.ALLIED_CARDIAC,
  })
  @IsEnum(UserSegment, {
    message: `the user segment must be one of the following values: ${Object.values(UserSegment).join(', ')}`,
  })
  userSegment: UserSegment;
}
