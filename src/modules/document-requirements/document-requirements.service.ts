import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { documentRequirements } from '@/db/schema';

import { UserSegment } from '@/constants/user-segments';

@Injectable()
export class DocumentRequirementsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  /**
   * Get document requirements for a user segment
   */
  async getDocumentRequirementsByUserSegment(userSegment: UserSegment) {
    return this.drizzleDev.query.documentRequirements.findMany({
      columns: {
        id: true,
        isRequired: true,
        sortOrder: true,
        maxCount: true,
        instructions: true,
      },
      where: and(
        eq(documentRequirements.userSegment, userSegment),
        eq(documentRequirements.status, 1),
      ),
      with: {
        documentType: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: (req) => req.sortOrder,
    });
  }
}
