import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { RoleLevel } from '@/constants/roles';

export class CreateRoleDto {
  @ApiProperty({
    name: 'name',
    type: 'string',
    required: true,
    example: 'Administrator',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  name: string;

  @ApiProperty({
    name: 'key',
    type: 'string',
    required: true,
    example: 'admin',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  key: string;

  @ApiProperty({
    name: 'level',
    type: 'string',
    required: true,
    example: RoleLevel.INTERNAL,
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(RoleLevel)
  level: RoleLevel;
}
