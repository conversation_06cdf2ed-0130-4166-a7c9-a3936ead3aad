import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { RolesService } from './roles.service';

@Controller('roles')
@ApiBearerAuth()
@ApiTags('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Get all permissions associated with a role
   * @param roleId - The ID of the role
   * @returns An array of permissions associated with the role
   */
  @ApiOperation({ summary: 'Get all permissions associated with a role' })
  @Get(':id/permissions')
  findPermissionsByRoleId(@Param('id') roleId: string) {
    return this.rolesService.findPermissionsByRoleId(roleId);
  }
}
