import { Body, Controller, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { GenerateUploadUrlDto } from './dto/generate-upload-url.dto';

import { User } from '@/decorators/user.decorator';

import { AuthConstants } from '@/constants/auth';

import { FileUploadService } from './utils.service';

// Controller
@ApiBearerAuth()
@ApiTags('utils')
@Controller('utils')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('generate-upload-url')
  async generateUploadUrl(
    @Body() dto: GenerateUploadUrlDto,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  ) {
    return this.fileUploadService.createPresignedUploadUrl(dto, userId);
  }
}
