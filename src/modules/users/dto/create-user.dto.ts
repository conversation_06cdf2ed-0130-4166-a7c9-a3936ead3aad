import {
  IsE<PERSON>,
  <PERSON>S<PERSON>,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsEnum,
  IsIn,
} from 'class-validator';
import { Transform, TransformFnParams, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { AccountType } from '@/constants/users';

export class GlobalRoleDto {
  @ApiProperty({
    description: 'Global role ID for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'permission ids for the role',
    required: false,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  permissionIds: string[];
}

export class WorkspaceRoleDto {
  @ApiProperty({
    description: 'Workspace role ID for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    description: 'Workspace ID for the role',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  workspaceId: string;

  @ApiProperty({
    description: 'permission ids for the role',
    required: false,
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsUUID(4, { each: true })
  permissionIds: string[];
}

export class CreateUserDto {
  @ApiProperty({
    name: 'displayName',
    type: 'string',
    required: true,
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  displayName: string;

  @ApiProperty({
    name: 'email',
    type: 'string',
    required: true,
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    name: 'accountType',
    type: AccountType, // string , enum
    // enum: AccountType,
    required: true,
    example: AccountType.ORGANISATION,
  })
  @IsNotEmpty()
  @IsEnum(AccountType) // can be add message if needs
  @IsIn([AccountType.ORGANISATION, AccountType.PROFESSIONAL, AccountType.PUBLIC], {
    message: 'account type must be either professional, organisation or public',
  })
  accountType: AccountType;

  @ApiProperty({
    name: 'globalRoles',
    required: true,
    type: [GlobalRoleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GlobalRoleDto)
  @IsOptional()
  @ArrayMinSize(1)
  globalRoles: GlobalRoleDto[];

  @ApiProperty({
    name: 'workspaceRoles',
    required: true,
    type: [WorkspaceRoleDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkspaceRoleDto)
  @IsOptional()
  workspaceRoles: WorkspaceRoleDto[];
}
