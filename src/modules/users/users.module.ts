import { Module } from '@nestjs/common';

import { UsersService } from './users.service';

import { CustomConfigService } from '@/config/configuration.service';

import { MailModule } from '@/common/mail/mail.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { UsersController } from './users.controller';

@Module({
  imports: [MailModule, UserRolesModule, UserRolesModule],
  providers: [UsersService, CustomConfigService],
  exports: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
