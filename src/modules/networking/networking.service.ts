import { Inject, Injectable } from '@nestjs/common';

import * as schema from '@/db/schema';
import { workspaceConnections, workspaceFollowers, users } from '@/db/schema';

import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, exists, gt, inArray, ne, not, or } from 'drizzle-orm';
import { WorkspaceConnectionStatus } from '@/constants/networking';
import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import { EntityName } from '@/constants/entities';
import { FollowerStatus } from '@/constants/followers';
import { EntityType } from '@/constants/user-types';
import {
  connectionRequestAlreadyExists,
  connectionRequestAlreadyReceived,
  connectionRequestNotFound,
} from '@/exceptions/networking';
import { WORKSPACE_RELATIONS } from '@/constants/workspaces';
import { AccountSetupStage, AccountType } from '@/constants/users';

@Injectable()
export class NetworkingService {
  @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>;

  async createConnectionRequest(requestorId: string, recipientId: string) {
    const existingRow = await this.drizzleDev.query.workspaceConnections.findFirst({
      where: this.getConnectionBetweenUsersCondition(requestorId, recipientId),
    });

    if (existingRow) {
      const isExistingRowCreatedByTheSameRequestor = existingRow.requestorId === requestorId;

      if (
        [
          WorkspaceConnectionStatus.DISCONNECTED,
          WorkspaceConnectionStatus.REJECTED,
          WorkspaceConnectionStatus.WITHDRAWN,
        ].includes(existingRow.status)
      ) {
        await this.drizzleDev
          .update(workspaceConnections)
          .set({
            status: WorkspaceConnectionStatus.PENDING,
            ...(!isExistingRowCreatedByTheSameRequestor
              ? {
                  recipientId: existingRow.requestorId,
                  requestorId: existingRow.recipientId,
                }
              : {}),
          })
          .where(
            and(
              eq(workspaceConnections.requestorId, existingRow.requestorId),
              eq(workspaceConnections.recipientId, existingRow.recipientId),
            ),
          );
      } else if (existingRow.status === WorkspaceConnectionStatus.PENDING) {
        throw isExistingRowCreatedByTheSameRequestor
          ? connectionRequestAlreadyExists()
          : connectionRequestAlreadyReceived();
      } else {
        throw itemAlreadyExists(EntityName.CONNECTION);
      }
    } else {
      await this.drizzleDev.insert(workspaceConnections).values({
        recipientId,
        requestorId,
        status: WorkspaceConnectionStatus.PENDING,
      });
    }

    return 'ok';
  }

  async handlePendingConnectionRequest(
    requestorId: string,
    recipientId: string,
    status:
      | WorkspaceConnectionStatus.APPROVED
      | WorkspaceConnectionStatus.REJECTED
      | WorkspaceConnectionStatus.WITHDRAWN,
  ) {
    const [updatedRow] = await this.drizzleDev
      .update(workspaceConnections)
      .set({
        status,
      })
      .where(
        and(
          eq(workspaceConnections.requestorId, requestorId),
          eq(workspaceConnections.recipientId, recipientId),
          eq(workspaceConnections.status, WorkspaceConnectionStatus.PENDING),
        ),
      )
      .returning();

    if (!updatedRow) {
      throw connectionRequestNotFound();
    }

    return 'ok';
  }

  async disconnectConnection(targetUserId: string, actionByUserId: string) {
    const [updatedRow] = await this.drizzleDev
      .update(workspaceConnections)
      .set({
        status: WorkspaceConnectionStatus.DISCONNECTED,
      })
      .where(
        and(
          this.getConnectionBetweenUsersCondition(targetUserId, actionByUserId),
          eq(workspaceConnections.status, WorkspaceConnectionStatus.APPROVED),
        ),
      );

    if (!updatedRow) {
      throw itemNotFound(EntityName.CONNECTION);
    }

    return 'ok';
  }

  async createFollower(targetUserId: string, actionByUserId: string) {
    const existingRow = await this.drizzleDev.query.workspaceFollowers.findFirst({
      where: and(
        eq(workspaceFollowers.workspaceId, targetUserId),
        eq(workspaceFollowers.entityId, actionByUserId),
      ),
    });

    if (existingRow) {
      if (existingRow.status === FollowerStatus.INACTIVE) {
        await this.drizzleDev.update(workspaceFollowers).set({
          status: FollowerStatus.ACTIVE,
        });
      } else {
        throw itemAlreadyExists(EntityName.FOLLOWER);
      }
    } else {
      await this.drizzleDev.insert(workspaceFollowers).values({
        entityId: actionByUserId,
        workspaceId: targetUserId,
        entityType: EntityType.USER,
        status: FollowerStatus.ACTIVE,
      });
    }

    return 'ok';
  }

  async removeFollower(targetUserId: string, actionByUserId: string) {
    const [updatedData] = await this.drizzleDev
      .update(workspaceFollowers)
      .set({
        status: FollowerStatus.INACTIVE,
      })
      .where(
        and(
          eq(workspaceFollowers.entityId, actionByUserId),
          eq(workspaceFollowers.workspaceId, targetUserId),
          eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
        ),
      )
      .returning();

    if (!updatedData) throw itemNotFound(EntityName.FOLLOWER);

    return 'ok';
  }

  // TODO: CURRENTLY ONLY RETURNS PROFILES OF SPECIALISTS
  async discoverProfiles(
    userId: string,
    limit = 20,
    accountType: AccountType = AccountType.PROFESSIONAL,
  ) {
    // Use a single query with subqueries for filtering

    // Use a single query with subqueries for filtering
    const discoverableUsers = await this.drizzleDev.query.users.findMany({
      where: and(
        ne(users.id, userId),
        inArray(users.accountType, [accountType]),
        inArray(users.currentStage, [
          AccountSetupStage.DOCUMENT_UPLOAD,
          AccountSetupStage.ADDING_NETWORK,
          AccountSetupStage.COMPLETED,
        ]), //FIXME: CHANGE THIS TO ONLY RETURN USERS WHO IS COMPLETED ONBOARDING FLOW
        // Exclude users who have a connection with this user
        not(
          exists(
            this.drizzleDev
              .select()
              .from(workspaceConnections)
              .where(
                or(
                  and(
                    eq(workspaceConnections.requestorId, userId),
                    eq(workspaceConnections.recipientId, users.id),
                    gt(workspaceConnections.status, WorkspaceConnectionStatus.REJECTED),
                  ),
                  and(
                    eq(workspaceConnections.requestorId, users.id),
                    eq(workspaceConnections.recipientId, userId),
                    gt(workspaceConnections.status, WorkspaceConnectionStatus.REJECTED),
                  ),
                ),
              ),
          ),
        ),

        // Exclude users who are being followed by this user
        not(
          exists(
            this.drizzleDev
              .select()
              .from(workspaceFollowers)
              .where(
                and(
                  eq(workspaceFollowers.entityId, userId),
                  eq(workspaceFollowers.workspaceId, users.id),
                  eq(workspaceFollowers.status, FollowerStatus.ACTIVE),
                ),
              ),
          ),
        ),
      ),

      // Select relevant fields
      columns: {
        id: true,
        displayName: true,
        username: true,
        profileImageUrlThumbnail: true,
        // Add other relevant fields
      },
      with: {
        createdWorkspaces: {
          columns: {},
          with: {
            [WORKSPACE_RELATIONS.SPECIALISTS]: {
              columns: {
                title: true,
                qualifications: true,
                jobTitle: true,
              },
              with: {
                employer: {
                  columns: {
                    name: true,
                  },
                },
              },
            },
            [WORKSPACE_RELATIONS.ORGANISATION]: {
              columns: {
                location: true,
              },
              with: {
                segmentCategory: {
                  columns: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      // Limit results for efficient loading
      limit,
      // You could order by similarity, recent activity, or random
      orderBy: (user, { desc }) => [desc(user.createdAt)],
    });

    return discoverableUsers;
  }

  private getConnectionBetweenUsersCondition(user1Id: string, user2Id: string) {
    return or(
      and(
        eq(workspaceConnections.requestorId, user1Id),
        eq(workspaceConnections.recipientId, user2Id),
      ),
      and(
        eq(workspaceConnections.requestorId, user2Id),
        eq(workspaceConnections.recipientId, user1Id),
      ),
    );
  }
}
