import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { AccountType } from '@/constants/users';

export class DiscoverProfilesQueryDto {
  @ApiPropertyOptional({
    description: 'The account type to filter by',
    example: AccountType.PROFESSIONAL,
    enum: AccountType,
  })
  @IsOptional()
  @IsEnum(AccountType, {
    message: `accountType must be one of the following values: ${Object.values(AccountType).join(', ')}`,
  })
  accountType?: AccountType;
}
