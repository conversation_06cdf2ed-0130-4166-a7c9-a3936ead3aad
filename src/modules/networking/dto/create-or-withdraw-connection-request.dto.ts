import { ConnectionAction } from '@/constants/networking';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class CreateAndWithdrawConnectionRequestDto {
  @ApiProperty({
    description: 'Unique identifier of the user receiving the connection request',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
    type: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  recipientId: string;
}

export class HandleConnectionRequestDto {
  @ApiProperty({
    description: 'Unique identifier of the user who made the connection request',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
    type: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  requestorId: string;

  @ApiProperty({
    description: 'Action to take on the connection request',
    required: true,
    enum: ConnectionAction,
    example: ConnectionAction.APPROVED,
  })
  @IsNotEmpty()
  @IsEnum(ConnectionAction)
  actionType: ConnectionAction;
}

export class DisconnectConnectionDto {
  @ApiProperty({
    description: 'The ID of the connected user you want to remove from your connection list',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
    type: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  targetUserId: string;
}

export class CreateDeleteFollowDto {
  @ApiProperty({
    description: 'Unique identifier of the user who has to be followed',
    required: true,
    example: '123e4567-e89b-12d3-a456-************',
    type: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  targetUserId: string;
}
