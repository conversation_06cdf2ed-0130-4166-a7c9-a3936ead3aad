import { Controller, Post, Body, Delete, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { NetworkingService } from './networking.service';

import {
  HandleConnectionRequestDto,
  CreateAndWithdrawConnectionRequestDto,
  DisconnectConnectionDto,
  CreateDeleteFollowDto,
} from './dto/create-or-withdraw-connection-request.dto';
import { DiscoverProfilesQueryDto } from './dto/discover-profiles-query.dto';

import { User } from '@/decorators/user.decorator';

import { AuthConstants } from '@/constants/auth';
import { WorkspaceConnectionStatus } from '@/constants/networking';
import { selfActionNotAllowed } from '@/exceptions/common';

@Controller('networking')
@ApiTags('networking')
export class NetworkingController {
  constructor(private readonly networkingService: NetworkingService) {}

  @Post('connect/request')
  createConnectionRequest(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Body() createConnectionRequestDto: CreateAndWithdrawConnectionRequestDto,
  ) {
    if (userId === createConnectionRequestDto.recipientId) throw selfActionNotAllowed();

    return this.networkingService.createConnectionRequest(
      userId,
      createConnectionRequestDto.recipientId,
    );
  }

  @Post('connect/request/withdraw')
  withdrawConnectionRequest(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Body() withdrawConnectionRequestDto: CreateAndWithdrawConnectionRequestDto,
  ) {
    if (userId === withdrawConnectionRequestDto.recipientId) throw selfActionNotAllowed();

    return this.networkingService.handlePendingConnectionRequest(
      userId,
      withdrawConnectionRequestDto.recipientId,
      WorkspaceConnectionStatus.WITHDRAWN,
    );
  }

  @Post('connect/request/action')
  handlePendingConnectionRequest(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Body() handleConnectionRequestDto: HandleConnectionRequestDto,
  ) {
    const status = WorkspaceConnectionStatus[handleConnectionRequestDto.actionType];

    return this.networkingService.handlePendingConnectionRequest(
      handleConnectionRequestDto.requestorId,
      userId,
      status,
    );
  }

  @Delete('connect/disconnect')
  disconnectConnection(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) actionByUserId: string,
    @Body() disconnectConnectionDto: DisconnectConnectionDto,
  ) {
    return this.networkingService.disconnectConnection(
      disconnectConnectionDto.targetUserId,
      actionByUserId,
    );
  }

  @Post('follower')
  createFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) actionByUserId: string,
    @Body() createFollowDto: CreateDeleteFollowDto,
  ) {
    if (actionByUserId === createFollowDto.targetUserId) throw selfActionNotAllowed();

    return this.networkingService.createFollower(createFollowDto.targetUserId, actionByUserId);
  }

  @Delete('follower')
  removeFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) actionByUserId: string,
    @Body() deleteFollowDto: CreateDeleteFollowDto,
  ) {
    return this.networkingService.removeFollower(deleteFollowDto.targetUserId, actionByUserId);
  }

  @Get('discover-profiles')
  discoverProfiles(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Query() query: DiscoverProfilesQueryDto,
  ) {
    return this.networkingService.discoverProfiles(userId, 20, query.accountType);
  }
}
