import { OmitType, PartialType } from '@nestjs/swagger';

import { CreateAccountTypeByAdminDto, CreateAccountTypeDto } from './create-workspace.dto';
import { UserSegment } from '@/constants/user-segments';

export class UpdateAccountTypeDto extends PartialType(OmitType(CreateAccountTypeDto, ['type'])) {}

export class UpdateAccountTypeByAdminDto extends PartialType(CreateAccountTypeByAdminDto) {}

export function cleanDtoBasedOnAccountType(dto: UpdateAccountTypeDto, accountType: UserSegment) {
  const dtoCopy = { ...dto }; // Create a shallow copy to avoid mutating the original object

  switch (accountType) {
    case UserSegment.ORGANISATION:
      // Allow only fields that belong to Organisation
      return {
        hqLocation: dtoCopy.hqLocation,
        registrationNumber: dtoCopy.registrationNumber,
        countryId: dtoCopy.countryId,
        city: dtoCopy.city,
        email: dtoCopy.email,
        website: dtoCopy.website,
        phoneNumber: dtoCopy.phoneNumber,
        zipCode: dtoCopy.zipCode,
        aboutUs: dtoCopy.aboutUs,
        instagramLink: dtoCopy.instagramLink,
        facebookLink: dtoCopy.facebookLink,
        twitterLink: dtoCopy.twitterLink,
        linkedinLink: dtoCopy.linkedinLink,
        youtubeLink: dtoCopy.youtubeLink,
        tiktokLink: dtoCopy.tiktokLink,
        profileImageUrl: dtoCopy.profileImageUrl,
        profileImageUrlThumbnail: dtoCopy.profileImageUrlThumbnail,
        organisationCertifications: dtoCopy.organisationCertifications,
        workspacename: dtoCopy.workspacename,
      };

    case UserSegment.CARDIAC_SPECIALIST:
      return {
        profileSummary: dtoCopy.profileSummary,
        yearsOfExperience: dtoCopy.yearsOfExperience,
        currentHospital: dtoCopy.currentHospital,
        email: dtoCopy.email,
        professionalCertificates: dtoCopy.professionalCertificates,
        professionalAwards: dtoCopy.professionalAwards,
        professionalAssociations: dtoCopy.professionalAssociations,
        workspacename: dtoCopy.workspacename,
      };

    case UserSegment.ALLIED_CARDIAC:
      return {};

    default:
      return {};
  }
}
