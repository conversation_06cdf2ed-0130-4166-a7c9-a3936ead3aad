import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { SQL, and, eq, gt } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  organisations,
  workspaces,
  professionals,
  specialists,
  Specialists,
  Organisations,
  Professionals,
  Workspaces,
} from '@/db/schema';

import { itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { AccountTypeInfo, WorkspacesStatus, PrimarySpeciality } from '@/constants/workspaces';

import { UserSegment } from '@/constants/user-segments';

@Injectable()
export class WorkspacesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  findOneWorkspaceByWorkspaceId(workspaceId: string, getBasicDetailsOnly: boolean = false) {
    return this.findUserByCondition(eq(workspaces.id, workspaceId), getBasicDetailsOnly);
  }

  private async findUserByCondition(condition: SQL, getBasicDetailsOnly: boolean = false) {
    const workspace = await this.drizzleDev.query.workspaces.findFirst({
      where: and(condition, gt(workspaces.status, WorkspacesStatus.ARCHIVED)),
      with: {
        createdByUser: {
          columns: {
            email: true,
          },
        },
      },
    });

    if (!workspace) throw itemNotFound(EntityName.WORKSPACE);

    if (getBasicDetailsOnly) return workspace;

    let accountTypeInfo: AccountTypeInfo;

    switch (workspace.userSegment) {
      case UserSegment.ORGANISATION:
        accountTypeInfo = (await this.drizzleDev.query.organisations.findFirst({
          where: and(eq(organisations.workspaceId, workspace.id), eq(organisations.status, 1)),
          with: {
            organisationCertifications: true,
            country: {
              columns: {
                name: true,
              },
            },
          },
        })) as Organisations;
        break;
      case UserSegment.CARDIAC_SPECIALIST:
        accountTypeInfo = (await this.drizzleDev.query.specialists.findFirst({
          where: and(eq(specialists.workspaceId, workspace.id), eq(schema.specialists.status, 1)),
          with: {
            professionalAssociations: true,
            professionalCertificates: true,
            professionalAwards: true,
          },
        })) as Specialists;
        break;
      case UserSegment.ALLIED_CARDIAC:
        accountTypeInfo = (await this.drizzleDev.query.professionals.findFirst({
          where: and(eq(professionals.workspaceId, workspace.id), eq(professionals.status, 1)),
        })) as Professionals;
        break;
      default:
        throw itemNotFound(EntityName.WORKSPACE);
    }

    if (!accountTypeInfo) throw itemNotFound(EntityName.WORKSPACE);

    return {
      ...workspace,
      ...accountTypeInfo,
    };
  }

  async createWorkspace(
    createAccountDto: {
      userSegment: UserSegment;
      createdById: string;
      primarySpeciality: PrimarySpeciality;
    },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const { primarySpeciality, createdById, userSegment } = createAccountDto;

    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (txn) => {
      // Insert the new workspace
      const [newWorkspace] = await txn
        .insert(workspaces)
        .values({
          primarySpeciality,
          createdById,
          userSegment,
          status: WorkspacesStatus.PENDING,
        })
        .returning();

      return newWorkspace;
    });
  }

  async updateWorkspace(
    workspaceId: string,
    updateData: Partial<Omit<Workspaces, 'id'>>,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const [updatedWorkspace] = await dbOrTransaction
      .update(workspaces)
      .set(updateData)
      .where(eq(workspaces.id, workspaceId))
      .returning();

    return updatedWorkspace;
  }
}
