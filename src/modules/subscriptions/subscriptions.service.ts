import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  subscriptionPlans,
  workspaceSubscriptions,
  prestigeMembershipApplications,
} from '@/db/schema';

import { UserSegment } from '@/constants/user-segments';
import { SubscriptionPlanStatus } from '@/constants/subscription-plans';
import { WorkspaceSubscriptionsStatus } from '@/constants/workspace-subscriptions';
import { PrestigeMembershipApplicationStatus } from '@/constants/prestige-membership-applications';

import { calculateSubscriptionEndDate } from '@/utils/subscriptions';

@Injectable()
export class SubscriptionsService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  findSubscriptionPlansByUserSegment({ userSegment }: { userSegment: UserSegment }) {
    return this.drizzleDev.query.subscriptionPlans.findMany({
      where: and(
        eq(subscriptionPlans.userSegment, userSegment),
        eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      ),
      orderBy: (sub, { asc }) => [asc(sub.priceMonthly)],
      with: {
        planFeatures: {
          columns: {
            booleanValue: true,
            integerValue: true,
            textValue: true,
          },
          with: {
            subscriptionFeature: {
              columns: {
                id: true,
                name: true,
                type: true,
                description: true,
              },
            },
          },
        },
      },
    });
  }

  async findSubscriptionPlanById(subscriptionId: string) {
    const subscriptionPlan = await this.drizzleDev.query.subscriptionPlans.findFirst({
      where: and(
        eq(subscriptionPlans.id, subscriptionId),
        eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      ),
    });

    if (!subscriptionPlan) return null;

    return subscriptionPlan;
  }

  async createSubscripton(
    {
      subscriptionPlanId,
      isYearly,
      workspaceId,
    }: {
      subscriptionPlanId: string;
      isYearly: boolean;
      workspaceId: string;
    },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const now = new Date();
    const endDate = calculateSubscriptionEndDate(isYearly);

    const subscriptionWorkspacesData: schema.NewWorkspaceSubscription = {
      workspaceId,
      subscriptionPlanId,
      isYearly,
      paymentMethod: 'free', // TODO: UPDATE NEEDS SUBSCRIPTION PAYMENT
      endDate: endDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      paymentDate: now.toISOString().split('T')[0],
      startDate: now.toISOString().split('T')[0],
      status: WorkspaceSubscriptionsStatus.ACTIVE,
    };

    const [subscription] = await dbOrTransaction
      .insert(workspaceSubscriptions)
      .values(subscriptionWorkspacesData)
      .returning();

    return subscription;
  }

  async findExistingPrestigeMembershipApplication(workspaceId: string) {
    return this.drizzleDev.query.prestigeMembershipApplications.findFirst({
      where: eq(prestigeMembershipApplications.workspaceId, workspaceId),
    });
  }

  async applyForPrestigeMembership(
    {
      workspaceId,
      website,
      organisationSize,
      pointOfContactName,
      pointOfContactPhone,
      allowSubsidiaries,
    }: {
      workspaceId: string;
      website?: string;
      organisationSize?: string;
      pointOfContactName?: string;
      pointOfContactPhone?: string;
      allowSubsidiaries?: boolean;
    },
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    const [application] = await dbOrTransaction
      .insert(prestigeMembershipApplications)
      .values({
        workspaceId,
        website,
        organisationSize,
        pointOfContactName,
        pointOfContactPhone,
        allowSubsidiaries,
        status: PrestigeMembershipApplicationStatus.PENDING,
      })
      .returning();

    return application;
  }
}
