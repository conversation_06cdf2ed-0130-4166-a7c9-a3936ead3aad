import { Modu<PERSON> } from '@nestjs/common';

import { SubscriptionsService } from './subscriptions.service';

import { SubscriptionsController } from './subscriptions.controller';

import { WorkspacesModule } from '@/modules/workspaces/workspaces.module';
import { RolesModule } from '@/modules/roles/roles.module';
import { UserRolesModule } from '@/modules/user-roles/user-roles.module';
import { UsersModule } from '@/modules/users/users.module';

@Module({
  controllers: [SubscriptionsController],
  providers: [SubscriptionsService],
  imports: [WorkspacesModule, RolesModule, UserRolesModule, UsersModule],
  exports: [SubscriptionsService],
})
export class SubscriptionsModule {}
