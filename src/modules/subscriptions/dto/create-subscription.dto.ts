import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

import { ProfessionalCategories } from '@/constants/workspaces';

export class CreateSubscriptionDto {
  @ApiProperty({
    name: 'subscriptionPlanId',
    type: 'uuid',
    required: true,
    example: '789e1234-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  subscriptionPlanId: string;

  @ApiProperty({
    name: 'isYearly',
    type: 'boolean',
    required: true,
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isYearly: boolean;

  @ApiProperty({
    name: 'professionalCategory',
    enum: ProfessionalCategories,
    required: false,
    default: ProfessionalCategories.ALLIED_CARDIAC,
    description: "send this if it's professional account type",
  })
  @IsEnum(ProfessionalCategories)
  @IsOptional()
  professionalCategory: ProfessionalCategories;
}
