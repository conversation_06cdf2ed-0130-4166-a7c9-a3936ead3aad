import { Body, Controller, Get, Inject, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';

import { User } from '@/decorators/user.decorator';
import { AccountSetupStages } from '@/decorators/account-setup-stage.decorator';
import { AccountTypes } from '@/decorators/account-types.decorator';

import { AuthConstants } from '@/constants/auth';
import { AccountType, AccountSetupStage } from '@/constants/users';
import { PrimarySpeciality, ProfessionalCategories } from '@/constants/workspaces';
import { EntityName } from '@/constants/entities';
import { PUBLIC_ACCOUNT_TYPE_ROLES_MAP, Role } from '@/constants/roles';
import { PrestigeMembershipApplicationStatus } from '@/constants/prestige-membership-applications';

import { UserData } from '@/interfaces/auth';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';
import {
  accountTypeAndSubscriptionPlanNotMatching,
  organizationApplicationFieldsRequired,
  professionalCategoryIsRequiredIfAccountTypeIsProfessional,
} from '@/exceptions/subscription';

import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { GetSubscriptionsDto } from './dto/get-subscriptions.dto';
import { ApplyPrestigeMembershipDto } from './dto/apply-prestige-membership.dto';

import { SubscriptionsService } from './subscriptions.service';
import { UsersService } from '@/modules/users/users.service';
import { WorkspacesService } from '@/modules/workspaces/workspaces.service';
import { UserRolesService } from '@/modules/user-roles/user-roles.service';
import { RolesService } from '@/modules/roles/roles.service';

@Controller('subscription-plans')
@ApiBearerAuth()
@ApiTags('subscription-plans')
export class SubscriptionsController {
  constructor(
    @Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>,
    private readonly subscriptionsService: SubscriptionsService,
    private readonly usersService: UsersService,
    private readonly workspacesService: WorkspacesService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
  ) {}

  @Get(':userSegment')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  @AccountSetupStages(AccountSetupStage.SUBSCRIPTION)
  getSubscriptionDetails(@Param() params: GetSubscriptionsDto) {
    return this.subscriptionsService.findSubscriptionPlansByUserSegment({
      userSegment: params.userSegment,
    });
  }

  @Post()
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  @AccountSetupStages(AccountSetupStage.SUBSCRIPTION)
  async createSubscription(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE) accountType: AccountType,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @Body() createSubscriptionDto: CreateSubscriptionDto,
  ) {
    const { subscriptionPlanId, professionalCategory, isYearly } = createSubscriptionDto;

    // Validate user exists
    const user = await this.usersService.findUserById(userId);
    if (!user) {
      throw itemNotFound(EntityName.USER);
    }

    // Validate workspace exists
    const workspace = await this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true);
    if (!workspace) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    // Validate subscription plan exists
    const subscriptionDetails =
      await this.subscriptionsService.findSubscriptionPlanById(subscriptionPlanId);
    if (!subscriptionDetails) {
      throw itemNotFound(EntityName.SUBSCRIPTION);
    }

    // Validate account type matches subscription plan
    const eligibleSegments =
      PUBLIC_ACCOUNT_TYPE_ROLES_MAP[
        user.accountType as AccountType.ORGANISATION | AccountType.PROFESSIONAL
      ];

    if (!eligibleSegments.includes(subscriptionDetails.userSegment)) {
      throw accountTypeAndSubscriptionPlanNotMatching();
    }

    // Validate professional category if account type is PROFESSIONAL
    if (accountType === AccountType.PROFESSIONAL && !professionalCategory) {
      throw professionalCategoryIsRequiredIfAccountTypeIsProfessional();
    }

    // Create subscription with workspace in a transaction
    const result = await this.drizzleDev.transaction(async (txn) => {
      // Update workspace with professional category and user segment if needed
      await this.workspacesService.updateWorkspace(
        workspaceId,
        {
          userSegment: subscriptionDetails.userSegment,
          primarySpeciality:
            accountType === AccountType.PROFESSIONAL && professionalCategory
              ? [
                  ProfessionalCategories.CARDIAC_SURGEON,
                  ProfessionalCategories.CARDIOLOGIST,
                ].includes(professionalCategory as any as ProfessionalCategories)
                ? (professionalCategory as any as PrimarySpeciality)
                : PrimarySpeciality.BOTH
              : workspace.primarySpeciality,
        },
        txn,
      );

      // Create the subscription and ensure it's committed
      const subscription = await this.subscriptionsService.createSubscripton(
        {
          subscriptionPlanId,
          isYearly,
          workspaceId,
        },
        txn,
      );

      const role = await this.rolesService.findRoleByKey(
        subscriptionDetails.userSegment as any as Role,
      );
      if (!role) throw itemNotFound(subscriptionDetails.userSegment);

      // assign user role and associate it with workspace
      await this.userRolesService.createUserRole({ userId, roleId: role.id, workspaceId }, txn);

      const rolePermissions = await this.rolesService.findPermissionsByRoleId(role.id);
      const mappedPermissions = rolePermissions
        ? rolePermissions?.rolesPermissions.map((rp) => rp.permission.key)
        : [];

      const userRecord = await getAuth().getUser(firebaseUid);
      const currentClaims: UserData = userRecord.customClaims as UserData;

      // workspace details
      const newClaims = {
        [AuthConstants.FIREBASE_CLAIM_ROLE]: role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: mappedPermissions,
        [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage.PROFILE_SETUP,
        [AuthConstants.FIREBASE_CLAIM_SUBSCRIPTION_PLAN_TITLE]: subscriptionDetails.title,
      };

      // Merge the new claims with the existing ones
      const updatedClaims = {
        ...currentClaims, // Existing claims
        ...newClaims,
      };

      // Update the user's claims in Firebase
      await getAuth().setCustomUserClaims(firebaseUid, updatedClaims);

      // Generate a custom token with the updated claims
      const customToken = await getAuth().createCustomToken(firebaseUid);

      return {
        workspaceId,
        subscriptionId: subscription.id, // Include the subscription ID
        redirectToProfileSetup: true,
        currentStage: AccountSetupStage.PROFILE_SETUP,
        customToken,
      };
    });

    // Add a small delay to ensure Firebase claims propagate
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return result;
  }

  @Post('apply-prestige')
  @AccountTypes(AccountType.ORGANISATION, AccountType.PROFESSIONAL)
  async applyForPrestigeMembership(
    @User(AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE) accountType: AccountType,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Body() applyDto: ApplyPrestigeMembershipDto,
  ) {
    // Validate workspace exists
    const workspace = await this.workspacesService.findOneWorkspaceByWorkspaceId(workspaceId, true);
    if (!workspace) {
      throw itemNotFound(EntityName.WORKSPACE);
    }

    // Check if workspace has already applied
    const existingApplication =
      await this.subscriptionsService.findExistingPrestigeMembershipApplication(workspaceId);

    if (
      existingApplication &&
      existingApplication?.status >= PrestigeMembershipApplicationStatus.PENDING
    ) {
      throw itemAlreadyExists(EntityName.PRESTIGE_MEMBERSHIP_APPLICATION);
    }

    // For organizations, validate required fields
    if (accountType === AccountType.ORGANISATION) {
      if (
        !applyDto.website ||
        !applyDto.organisationSize ||
        !applyDto.pointOfContactName ||
        !applyDto.pointOfContactPhone ||
        !applyDto.allowSubsidiaries
      ) {
        throw organizationApplicationFieldsRequired();
      }
    }

    // Apply for prestige membership
    const application = await this.subscriptionsService.applyForPrestigeMembership({
      workspaceId,
      website: applyDto.website,
      organisationSize: applyDto.organisationSize,
      pointOfContactName: applyDto.pointOfContactName,
      pointOfContactPhone: applyDto.pointOfContactPhone,
      allowSubsidiaries: applyDto.allowSubsidiaries,
    });

    return {
      applicationId: application.id,
    };
  }
}
