import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, ilike } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { employers, continents, roles, modules, countries, segmentCategories } from '@/db/schema';

import { RoleStatus } from '@/constants/roles';
import { ModulesStatus } from '@/constants/modules';
import { ContinentsStatus } from '@/constants/continents';
import { OpportunityTypes } from '@/constants/opportunities';
import { UserSegment } from '@/constants/user-segments';

@Injectable()
export class DropDownService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  // update geEmpoyer for debounced search for employer roles
  async getEmployers(name = '') {
    const employersData = await this.drizzleDev.query.employers.findMany({
      where: and(eq(employers.status, 1), ilike(employers.name, `%${name}%`)),
      columns: {
        id: true,
        name: true,
      },
      limit: 10,
    });
    return employersData;
  }

  async getContinents() {
    const continentsList = await this.drizzleDev.query.continents.findMany({
      where: eq(continents.status, ContinentsStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return continentsList;
  }

  async getRoles() {
    const rolesList = await this.drizzleDev.query.roles.findMany({
      where: eq(roles.status, RoleStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return rolesList;
  }

  async getModules() {
    const modulesList = await this.drizzleDev.query.modules.findMany({
      where: eq(modules.status, ModulesStatus.ACTIVE),
      columns: {
        id: true,
        name: true,
      },
    });
    return modulesList;
  }

  async getCountries() {
    const countriesList = await this.drizzleDev.query.countries.findMany({
      where: eq(countries.status, 1),
      columns: {
        id: true,
        name: true,
      },
      with: {
        continent: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });
    return countriesList;
  }

  async getSimplifiedSegmentCategories(userSegment: UserSegment) {
    const segmentCategoriesList = await this.drizzleDev.query.segmentCategories.findMany({
      where: and(eq(segmentCategories.status, 1), eq(segmentCategories.userSegment, userSegment)),
      columns: {
        id: true,
        name: true,
        userSegment: true,
      },
    });

    return segmentCategoriesList;
  }

  async getOpportunities() {
    return Object.values(OpportunityTypes);
  }
}
