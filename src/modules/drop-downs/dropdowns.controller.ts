import { Controller, Get, Param, ParseEnumPipe, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { DropDownService } from './dropdowns.service';

import { UserSegment } from '@/constants/user-segments';

import { SegmentCategoriesService } from '@/modules/segment-categories/segment-categories.service';
import { GetEmployersQueryDto } from './dto/get-employers-query.dto';

@Controller('drop-downs')
@ApiBearerAuth()
@ApiTags('drop-downs')
export class DropDownController {
  constructor(
    private readonly dropDownService: DropDownService,
    private readonly segmentCategoriesService: SegmentCategoriesService,
  ) {}

  @Get('/segment-categories/:userSegment')
  getSubtypes(@Param('userSegment', new ParseEnumPipe(UserSegment)) userSegment: UserSegment) {
    return this.segmentCategoriesService.getSegmentCategories({ userSegment });
  }

  @Get('/employers')
  getEmployers(@Query() query: GetEmployersQueryDto) {
    return this.dropDownService.getEmployers(query.name);
  }

  @Get('/countries')
  getCountries() {
    return this.dropDownService.getCountries();
  }

  @Get('/simplified-segment-categories/:userSegment')
  getSimplifiedSegmentCategories(
    @Param('userSegment', new ParseEnumPipe(UserSegment)) userSegment: UserSegment,
  ) {
    return this.dropDownService.getSimplifiedSegmentCategories(userSegment);
  }
}
