import { <PERSON>, Get, Post, Body, Patch, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { UserRole } from '@/db/schema';

import { UserRolesService } from './user-roles.service';

import { CreateUserRolesDto } from './dto/create-user-role.dto';
import { UpdateUserRolesDto } from './dto/update-user-role.dto';
import { SwitchUserRoleDto } from './dto/switch-user-role.dto';

import { User } from '@/decorators/user.decorator';

import { AuthConstants } from '@/constants/auth';

@Controller('user-roles')
@ApiBearerAuth()
@ApiTags('user-roles')
export class UserRolesController {
  constructor(private readonly userRolesService: UserRolesService) {}

  @Post('')
  async createUserRole(@Body() createUserRolesDto: CreateUserRolesDto): Promise<UserRole> {
    return this.userRolesService.createUserRole(createUserRolesDto);
  }

  @Get(':id')
  async findOneUserRole(@Param('id') id: string): Promise<UserRole> {
    return this.userRolesService.findOneUserRole(id);
  }

  @Patch(':id')
  async updateUserRole(
    @Param('id') id: string,
    @Body() updateUserRolesDto: UpdateUserRolesDto,
  ): Promise<UserRole> {
    return this.userRolesService.updateUserRole(id, updateUserRolesDto);
  }

  @ApiOperation({ summary: 'Assign a permission to a user' })
  @Post('assign-permission-user-role/:userRoleId/:permissionId')
  assignPermissionToUser(
    @Param('userRoleId') userRoleId: string,
    @Param('permissionId') permissionId: string,
  ) {
    return this.userRolesService.assignPermissionToUserRole(userRoleId, permissionId);
  }

  @ApiOperation({ summary: 'Get all permissions associated with a user' })
  @Get(':id/permissions')
  findPermissionsByRoleId(@Param('id') userRoleId: string) {
    return this.userRolesService.findPermissionsByUserRoleId(userRoleId);
  }

  @Post('switch-role')
  switchUserRole(
    @User(AuthConstants.FIREBASE_UID) firebaseUid: string,
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @Body() switchUserRoleDto: SwitchUserRoleDto,
  ) {
    return this.userRolesService.switchUserRole(firebaseUid, userId, switchUserRoleDto.userRoleId);
  }
}
