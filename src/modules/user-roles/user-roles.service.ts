import { Injectable, Inject } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';
import { getAuth } from 'firebase-admin/auth';

import * as schema from '@/db/schema';
import { userPermissions, userRoles, rolePermissions } from '@/db/schema';

import { CreateUserRolesDtoWith } from './dto/create-user-role.dto';
import { UpdateUserRolesDto } from './dto/update-user-role.dto';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';
import { UserRolesStatus } from '@/constants/user-roles';
import { RolesPermissionStatus } from '@/constants/roles-permissions';
import { UserPermissionStatus } from '@/constants/user-permissions';
import { AuthConstants } from '@/constants/auth';

import { UserData } from '@/interfaces/auth';

@Injectable()
export class UserRolesService {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  async createUserRole(
    createUserRolesDto: CreateUserRolesDtoWith,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      const existingUserRoles = await tx.query.userRoles.findFirst({
        where: and(
          eq(userRoles.userId, createUserRolesDto.userId),
          eq(userRoles.roleId, createUserRolesDto.roleId),
          createUserRolesDto.workspaceId
            ? eq(userRoles.workspaceId, createUserRolesDto.workspaceId)
            : undefined,
        ),
      });

      let userRoleDetails;

      if (existingUserRoles) {
        if (existingUserRoles.status === UserRolesStatus.INACTIVE) {
          const [existingUserRole] = await tx
            .update(userRoles)
            .set({
              status: UserRolesStatus.ACTIVE,
            })
            .where(eq(userRoles.id, existingUserRoles.id))
            .returning();

          userRoleDetails = existingUserRole;
        } else {
          throw itemAlreadyExists(EntityName.USER_ROLES);
        }
      } else {
        const [newUserRole] = await tx
          .insert(userRoles)
          .values({
            ...createUserRolesDto,
            status: UserRolesStatus.ACTIVE,
          })
          .returning();

        userRoleDetails = newUserRole;
      }

      if (createUserRolesDto.permissionIds) {
        for (const permission of createUserRolesDto.permissionIds) {
          try {
            await this.assignPermissionToUserRole((userRoleDetails as any).id, permission, tx);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (error) {
            // Handle the error if necessary
          }
        }

        return (userRoleDetails as any).id;
      }

      // assigning permissions from rolesPermissions table into userPermissions table.
      const permissionsFromRolesPermission = await tx.query.rolePermissions.findMany({
        where: and(
          eq(rolePermissions.roleId, createUserRolesDto.roleId),
          eq(rolePermissions.status, RolesPermissionStatus.ACTIVE),
        ),
      });

      for (const permission of permissionsFromRolesPermission) {
        try {
          await this.assignPermissionToUserRole(
            (userRoleDetails as any).id,
            permission.permissionId,
            tx,
          );
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          // Handle the error if necessary
        }
      }

      return userRoleDetails;
    });
  }

  async findOneUserRole(id: string) {
    const userRole = await this.drizzleDev.query.userRoles.findFirst({
      where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
    });

    if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

    return userRole;
  }

  async updateUserRole(id: string, updateUserRolesDto: UpdateUserRolesDto) {
    const userRole = await this.drizzleDev.query.userRoles.findFirst({
      where: and(eq(userRoles.id, id), eq(userRoles.status, UserRolesStatus.ACTIVE)),
    });

    if (!userRole) throw itemNotFound(EntityName.USER_ROLES);

    const [updatedUserRole] = await this.drizzleDev
      .update(userRoles)
      .set({
        ...updateUserRolesDto,
      })
      .where(eq(userRoles.id, id))
      .returning();

    return updatedUserRole;
  }

  async assignPermissionToUserRole(
    userRoleId: string,
    permissionId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const dbOrTransaction = transaction || this.drizzleDev;

    return dbOrTransaction.transaction(async (tx) => {
      const userPermission = await tx.query.userPermissions.findFirst({
        where: and(
          eq(userPermissions.userRoleId, userRoleId),
          eq(userPermissions.permissionId, permissionId),
        ),
      });

      if (userPermission) {
        if (userPermission.status === UserPermissionStatus.ACTIVE) {
          throw itemAlreadyExists(EntityName.USER_PERMISSION);
        } else {
          const [updatedUserPermission] = await tx
            .update(userPermissions)
            .set({
              status: UserPermissionStatus.ACTIVE,
            })
            .where(
              and(
                eq(userPermissions.userRoleId, userRoleId),
                eq(userPermissions.permissionId, permissionId),
              ),
            )
            .returning();

          return updatedUserPermission;
        }
      }

      const [newUserPermission] = await tx
        .insert(userPermissions)
        .values({
          userRoleId,
          permissionId,
        })
        .returning();

      return newUserPermission;
    });
  }

  async findPermissionsByUserRoleId(userRoleId: string) {
    const userPermission = await this.drizzleDev.query.userRoles.findFirst({
      columns: {},
      where: and(eq(userRoles.id, userRoleId), eq(userRoles.status, UserRolesStatus.ACTIVE)),
      with: {
        userPermissions: {
          columns: {
            permissionId: true,
          },
          where: and(
            eq(userPermissions.userRoleId, userRoleId),
            eq(userPermissions.status, UserPermissionStatus.ACTIVE),
          ),
          with: {
            permission: {
              columns: {
                name: true,
                key: true,
              },
              with: {
                module: {
                  columns: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return userPermission;
  }

  async switchUserRole(firebaseUid: string, userId: string, userRoleId: string) {
    return this.drizzleDev.transaction(async (tx) => {
      // 1. Verify user role exists and is active
      const userRole = await tx.query.userRoles.findFirst({
        where: and(
          eq(userRoles.id, userRoleId),
          eq(userRoles.userId, userId),
          eq(userRoles.status, UserRolesStatus.ACTIVE),
        ),
        with: {
          role: {
            columns: {
              key: true,
            },
          },
        },
      });

      if (!userRole) {
        throw itemNotFound(EntityName.USER_ROLES);
      }

      // 2. Get user's permissions for this role
      const permissions = await this.findPermissionsByUserRoleId(userRoleId);
      const structuredPermissions = permissions?.userPermissions.map(
        (userPerm) => userPerm.permission.key,
      );

      // 4. Prepare new claims
      const workspaceDetails = {
        [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: userRole.workspaceId,
        [AuthConstants.FIREBASE_CLAIM_ROLE]: userRole.role.key,
        [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: structuredPermissions,
      };

      // 5. Update Firebase claims
      const userRecord = await getAuth().getUser(firebaseUid);
      const currentClaims: UserData = (userRecord.customClaims as UserData) || {};

      const updatedClaims = {
        ...currentClaims,
        ...workspaceDetails,
      };

      await getAuth().setCustomUserClaims(firebaseUid, updatedClaims);

      return workspaceDetails;
    });
  }
}
