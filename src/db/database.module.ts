// import { Module } from '@nestjs/common';
// import { ConfigModule, ConfigService } from '@nestjs/config';
// import { DrizzlePostgresModule } from '@knaadh/nestjs-drizzle-postgres';
// import * as schema from '../db/schema';

// @Module({
//   imports: [
//     ConfigModule.forRoot(),
//     DrizzlePostgresModule.register({
//       // imports: [ConfigModule.forRoot],
//       useFactory: async (configService: ConfigService) => ({
//         tag: 'DB_DEV',
//         postgres: {
//           url: configService.get<string>('DATABASE_URL'),
//         },
//         config: { schema: { ...schema } },
//       }),
//       inject: [ConfigService],
//     }),
//   ],
//   exports: [DrizzlePostgresModule],
// })
// export class DatabaseModule {}
