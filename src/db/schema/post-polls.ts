import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { posts } from './posts';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postPolls = pgTable('post_polls', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  option: text('option').notNull(),
  entityId: uuid('entity_id').notNull(),
  entityType: entityTypeEnum('entity_type').notNull(),
  status: integer('status').default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const postPollsRelations = relations(postPolls, ({ one }) => ({
  post: one(posts, {
    fields: [postPolls.postId],
    references: [posts.id],
  }),
}));

export type PostPolls = typeof postPolls.$inferSelect;
export type NewPostPolls = typeof postPolls.$inferInsert;
