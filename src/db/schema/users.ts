import { pgTable, uuid, text, timestamp, integer, pgEnum } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';

import { AccountType, AccountSetupStage } from '@/constants/users';

import { workspaceUsers } from './workspace-users';
import { userRoles } from './user-roles';
import { posts, postsRelationsNames } from './posts';
import { postViews } from './post-views';
import { workspaces } from './workspaces';
import { opportunityApplications } from './opportunity-applications';
import { reportedPosts } from './reported-posts';
import { opportunities } from './opportunities';
import { workspaceDocuments } from './workspace-documents';
import { otps } from './otps';

const accountTypeEnum = pgEnum('account_type', enumToPgEnum(AccountType));
const accountSetupStageEnum = pgEnum('current_stage', enumToPgEnum(AccountSetupStage));

export const users = pgTable('users', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  displayName: text('display_name').notNull(),
  username: text('username').notNull().unique(),
  introductoryStatement: text('introductory_statement'),
  email: text('email').notNull().unique(),
  currentStage: accountSetupStageEnum('current_stage')
    .notNull()
    .default(AccountSetupStage.SUBSCRIPTION),
  providerId: text('provider_id').unique().notNull(),
  accountType: accountTypeEnum('account_type').notNull(), // enum PROFESSIONAL, ORGANISATION, PUBLIC, SYSTEM_ADMIN, SUPER_ADMIN, OWNER
  profileImageUrl: text('profile_image_url'),
  profileImageUrlThumbnail: text('profile_image_url_thumbnail'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const usersRelations = relations(users, ({ many }) => ({
  workspaceUsers: many(workspaceUsers),
  userRoles: many(userRoles),
  publishedPosts: many(posts, {
    relationName: postsRelationsNames.publisher,
  }),
  publishedOpportunities: many(opportunities),
  updatedPosts: many(posts, {
    relationName: postsRelationsNames.updatedBy,
  }),
  verifiedWorkspaceDocuments: many(workspaceDocuments),
  postViews: many(postViews),
  createdWorkspaces: many(workspaces),
  applications: many(opportunityApplications),
  reportedPosts: many(reportedPosts),
  otps: many(otps),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
