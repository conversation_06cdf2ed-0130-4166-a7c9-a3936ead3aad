import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid, integer } from 'drizzle-orm/pg-core';

import { specialists } from './specialists';

export const professionalAssociations = pgTable('professional_associations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  specialistId: uuid('specialist_id')
    .notNull()
    .references(() => specialists.id, { onDelete: 'cascade' }),
  associationName: text('association_name').notNull(),
  membershipId: text('membership_id'),
  joinedDate: date('joined_date').notNull(),
  expiryDate: date('expiry_date'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalAssociationsRelations = relations(professionalAssociations, ({ one }) => ({
  specialist: one(specialists, {
    fields: [professionalAssociations.specialistId],
    references: [specialists.id],
  }),
}));
