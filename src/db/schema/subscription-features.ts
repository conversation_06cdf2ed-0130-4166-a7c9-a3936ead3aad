import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { SubscriptionFeaturesStatus, ValueType } from '@/constants/subscription-features';

import { enumToPgEnum } from '@/utils/database';

import { subscriptionPlanFeatures } from './subscription-plan-features';

const typeEnum = pgEnum('type', enumToPgEnum(ValueType as any));

export const subscriptionFeatures = pgTable('subscription_features', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  key: text('key').unique().notNull(),
  type: typeEnum('type').notNull(),
  description: text('description'),
  status: integer('status').notNull().default(SubscriptionFeaturesStatus.ACTIVE),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const subscriptionFeaturesRelations = relations(subscriptionFeatures, ({ many }) => ({
  subscriptionFeatures: many(subscriptionPlanFeatures),
}));
