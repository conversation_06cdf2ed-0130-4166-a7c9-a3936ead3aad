// database/schema.ts
import { relations, sql } from 'drizzle-orm';
import { pgTable, timestamp, boolean, uuid, pgEnum, integer } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { PostNotificationsStatus, PostNotificationsType } from '@/constants/post-notifications';
import { EntityType } from '@/constants/user-types';

import { posts } from './posts';
import { postComments } from './post-comments';

const typeEnum = pgEnum('type', enumToPgEnum(PostNotificationsType));
const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postNotifications = pgTable('post_notifications', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  entityId: uuid('entity_id').notNull(), // id from workspaces or users table
  entityType: entityTypeEnum('entity_type').notNull(),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  commentId: uuid('comment_id').references(() => postComments.id, { onDelete: 'cascade' }),
  type: typeEnum('type').notNull(), // 'like' or 'comment'
  isRead: boolean('is_read').default(false),
  status: integer('status').notNull().default(PostNotificationsStatus.ACTIVE),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const postNotificationsRelations = relations(postNotifications, ({ one }) => ({
  post: one(posts, {
    fields: [postNotifications.postId],
    references: [posts.id],
  }),
  comment: one(postComments, {
    fields: [postNotifications.commentId],
    references: [postComments.id],
  }),
}));

export type PostNotifications = typeof postNotifications.$inferSelect;
export type NewPostNotifications = typeof postNotifications.$inferInsert;
