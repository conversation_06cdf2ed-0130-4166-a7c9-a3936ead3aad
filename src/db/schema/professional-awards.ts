import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid, integer } from 'drizzle-orm/pg-core';

import { specialists } from './specialists';

export const professionalAwards = pgTable('professional_awards', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  specialistId: uuid('specialist_id')
    .notNull()
    .references(() => specialists.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  awardingBody: text('awarding_body').notNull(),
  awardedDate: date('awarded_date').notNull(),
  description: text('description'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalAwardsRelations = relations(professionalAwards, ({ one }) => ({
  specialist: one(specialists, {
    fields: [professionalAwards.specialistId],
    references: [specialists.id],
  }),
}));
