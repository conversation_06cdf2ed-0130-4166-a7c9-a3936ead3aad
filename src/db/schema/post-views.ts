import { pgTable, uuid, timestamp, integer, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { posts } from './posts';
import { users } from './users';

export const postViews = pgTable(
  'post_views',
  {
    postId: uuid('post_id')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    viewCount: integer('view_count').default(1).notNull(),
    status: integer('status').notNull().default(1),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.postId, table.userId] }),
  }),
);

export const postViewsRelations = relations(postViews, ({ one }) => ({
  post: one(posts, {
    fields: [postViews.postId],
    references: [posts.id],
  }),
  user: one(users, {
    fields: [postViews.userId],
    references: [users.id],
  }),
}));

export type PostViews = typeof postViews.$inferSelect;
export type NewPostViews = typeof postViews.$inferInsert;
