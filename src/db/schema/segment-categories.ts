import { relations, sql } from 'drizzle-orm';
import { AnyPgColumn, integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { UserSegment } from '@/constants/user-segments';

import { organisations } from './organisations';
import { professionals } from './professionals';

const userSegmentEnum = pgEnum('user_segment', enumToPgEnum(UserSegment));

export const segmentCategories = pgTable('segment_categories', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  parentId: uuid('parent_id').references((): AnyPgColumn => segmentCategories.id, {
    onDelete: 'cascade',
  }),
  userSegment: userSegmentEnum('user_segment').notNull(), // CARDIAC_SPECIALIST, ALLIED_CARDIAC, ORGANISATION etc
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const segmentCategoriesRelations = relations(segmentCategories, ({ one, many }) => ({
  parent: one(segmentCategories, {
    fields: [segmentCategories.parentId],
    references: [segmentCategories.id],
    relationName: 'parentChild',
  }),
  children: many(segmentCategories, { relationName: 'parentChild' }),
  organisations: many(organisations),
  professionals: many(professionals),
}));

export type SegmentCategory = typeof segmentCategories.$inferSelect;
