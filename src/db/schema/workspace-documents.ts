import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid, date, smallint } from 'drizzle-orm/pg-core';

import { documentTypes } from './document-types';
import { workspaces } from './workspaces';
import { users } from './users';

export const workspaceDocuments = pgTable('workspace_documents', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, {
      onDelete: 'cascade',
    })
    .notNull(),
  documentTypeId: uuid('document_type_id')
    .references(() => documentTypes.id, {
      onDelete: 'cascade',
    })
    .notNull(),
  filePath: text('file_path').notNull(),
  fileName: text('file_name').notNull(),
  fileSize: integer('file_size').notNull(),
  sortOrder: integer('sort_order').notNull(),
  mimeType: text('mime_type').notNull(),
  verificationStatus: smallint('verification_status').notNull(),
  verifiedById: uuid('verified_by_id').references(() => users.id, {
    onDelete: 'cascade',
  }),
  verifiedDate: date('verified_date'),
  verificationNotes: text('verification_notes'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const workspaceDocumentsRelations = relations(workspaceDocuments, ({ one }) => ({
  workspace: one(workspaces, {
    fields: [workspaceDocuments.workspaceId],
    references: [workspaces.id],
  }),
  documentType: one(documentTypes, {
    fields: [workspaceDocuments.documentTypeId],
    references: [documentTypes.id],
  }),
  verifiedBy: one(users, {
    fields: [workspaceDocuments.verifiedById],
    references: [users.id],
  }),
}));

export type NewWorkspaceDocument = typeof workspaceDocuments.$inferInsert;
