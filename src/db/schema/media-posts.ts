import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';
import { postMedias } from './post-medias';

export const mediaPosts = pgTable('media_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  caption: text('caption'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const mediaPostsRelations = relations(mediaPosts, ({ one, many }) => ({
  post: one(posts, {
    fields: [mediaPosts.postId],
    references: [posts.id],
  }),
  postMedias: many(postMedias),
}));

export type MediaPost = typeof mediaPosts.$inferSelect;
export type NewMediaPost = typeof mediaPosts.$inferInsert;
