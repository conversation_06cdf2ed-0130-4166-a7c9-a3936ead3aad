import { relations, sql } from 'drizzle-orm';
import { date, pgTable, text, timestamp, uuid, integer } from 'drizzle-orm/pg-core';

import { specialists } from './specialists'; // Assuming you have this file

export const professionalCertificates = pgTable('professional_certificates', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  specialistId: uuid('specialist_id')
    .notNull()
    .references(() => specialists.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  issuingBody: text('issuing_body').notNull(),
  issuedDate: date('issued_date').notNull(),
  expiryDate: date('expiry_date'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const professionalCertificatesRelations = relations(professionalCertificates, ({ one }) => ({
  specialist: one(specialists, {
    fields: [professionalCertificates.specialistId],
    references: [specialists.id],
  }),
}));
