import { relations, sql } from 'drizzle-orm';
import { date, pgEnum, pgTable, text, timestamp, uuid, integer } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { CertificationType } from '@/constants/organisation-certifications';

import { organisations } from './organisations';

const certificateTypeEnum = pgEnum('certification_type', enumToPgEnum(CertificationType));

export const organisationCertifications = pgTable('organisation_certifications', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  organisationId: uuid('organisation_id')
    .notNull()
    .references(() => organisations.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  issuingBody: text('issuing_body').notNull(),
  issuedDate: date('issued_date').notNull(),
  expiryDate: date('expiry_date'),
  certificationType: certificateTypeEnum('certification_type').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const organisationCertificationsRelations = relations(
  organisationCertifications,
  ({ one }) => ({
    organisation: one(organisations, {
      fields: [organisationCertifications.organisationId],
      references: [organisations.id],
    }),
  }),
);
