import { relations, sql } from 'drizzle-orm';
import { pgTable, text, uuid, integer, timestamp } from 'drizzle-orm/pg-core';

import { opportunities } from './opportunities';

export const opportunityAttachments = pgTable('opportunity_attachments', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  opportunityId: uuid('opportunity_id')
    .notNull()
    .references(() => opportunities.id, { onDelete: 'cascade' }),
  filePath: text('file_path').notNull(),
  fileType: text('file_type').notNull(),
  fileSize: integer('file_size').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const opportunitiyAttachmentsRelations = relations(opportunityAttachments, ({ one }) => ({
  opportunity: one(opportunities, {
    fields: [opportunityAttachments.opportunityId],
    references: [opportunities.id],
  }),
}));
