import { integer, pgTable, primaryKey, timestamp, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { users } from './users';

export const workspaceConnections = pgTable(
  'workspace_connections',
  {
    requestorId: uuid('requestor_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    recipientId: uuid('recipient_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    status: integer('status').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.requestorId, table.recipientId] }),
  }),
);

export const workspaceConnectionsRelations = relations(workspaceConnections, ({ one }) => ({
  requestor: one(users, {
    fields: [workspaceConnections.requestorId],
    references: [users.id],
  }),
  recipient: one(users, {
    fields: [workspaceConnections.recipientId],
    references: [users.id],
  }),
}));

export type WorkspaceConnection = typeof workspaceConnections.$inferSelect;
export type NewWorkspaceConnection = typeof workspaceConnections.$inferInsert;
