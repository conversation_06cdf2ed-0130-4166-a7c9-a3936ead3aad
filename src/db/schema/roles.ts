import { relations, sql } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { Role, RoleLevel, RoleStatus } from '@/constants/roles';

import { enumToPgEnum } from '@/utils/database';

import { rolePermissions } from './role-permissions';
import { userRoles } from './user-roles';

const levelEnum = pgEnum('level', enumToPgEnum(RoleLevel));
const keyEnum = pgEnum('key', enumToPgEnum(Role));

export const roles = pgTable('roles', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  key: keyEnum('key').notNull(),
  level: levelEnum('level').notNull(),
  status: integer('status').notNull().default(RoleStatus.ACTIVE),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const rolesRelations = relations(roles, ({ many }) => ({
  userRoles: many(userRoles),
  rolesPermissions: many(rolePermissions),
}));
