import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';

export const textPosts = pgTable('text_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const textPostsRelations = relations(textPosts, ({ one }) => ({
  post: one(posts, {
    fields: [textPosts.postId],
    references: [posts.id],
  }),
}));

export type TextPost = typeof textPosts.$inferSelect;
export type NewTextPost = typeof textPosts.$inferInsert;
