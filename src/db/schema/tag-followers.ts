import { pgTable, uuid, timestamp, integer, primaryKey, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';

import { EntityType } from '@/constants/user-types';

import { tags } from './tags';
import { postTags } from './post-tags';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const tagFollowers = pgTable(
  'tag_followers',
  {
    tagId: uuid('tag_id')
      .notNull()
      .references(() => tags.id, { onDelete: 'cascade' }),
    entityType: entityTypeEnum('entity_type').notNull(),
    entityId: uuid('entity_id').notNull(),
    status: integer('status').notNull().default(1),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'pk_tag_followers', columns: [table.tagId, table.entityId] }),
  }),
);

export const tagFollowersRelations = relations(tagFollowers, ({ one, many }) => ({
  tag: one(tags, {
    fields: [tagFollowers.tagId],
    references: [tags.id],
  }),
  postTags: many(postTags),
}));

export type TagFollowers = typeof tagFollowers.$inferSelect;
export type NewTagFollowers = typeof tagFollowers.$inferInsert;
