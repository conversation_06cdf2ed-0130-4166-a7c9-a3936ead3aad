import { relations, sql } from 'drizzle-orm';
import { boolean, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';

export const prestigeMembershipApplications = pgTable('prestige_membership_applications', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, { onDelete: 'cascade' }),
  website: text('website'),
  organisationSize: text('organisation_size'),
  pointOfContactName: text('point_of_contact_name'),
  pointOfContactPhone: text('point_of_contact_phone'),
  allowSubsidiaries: boolean('allow_subsidiaries'),
  status: integer('status').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const prestigeMembershipApplicationsRelations = relations(
  prestigeMembershipApplications,
  ({ one }) => ({
    workspace: one(workspaces, {
      fields: [prestigeMembershipApplications.workspaceId],
      references: [workspaces.id],
    }),
  }),
);

export type PrestigeMembershipApplication = typeof prestigeMembershipApplications.$inferSelect;
export type NewPrestigeMembershipApplication = typeof prestigeMembershipApplications.$inferInsert;
