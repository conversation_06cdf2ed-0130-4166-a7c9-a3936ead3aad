import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { organisations } from './organisations';
import { professionals } from './professionals';
import { specialists } from './specialists';

export const employers = pgTable('employers', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  url: text('url').notNull(),
  organisationId: uuid('organisation_id').references(() => organisations.id, {
    onDelete: 'cascade',
  }),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const employersRelations = relations(employers, ({ one, many }) => ({
  organisation: one(organisations, {
    fields: [employers.organisationId],
    references: [organisations.id],
  }),
  professionals: many(professionals),
  specialists: many(specialists),
}));
