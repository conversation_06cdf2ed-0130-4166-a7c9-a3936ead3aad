import { pgTable, uuid, timestamp, integer, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { posts } from './posts';
import { countries } from './countries';

export const postCountries = pgTable(
  'post_countries',
  {
    postId: uuid('post_id')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    countryId: uuid('country_id')
      .notNull()
      .references(() => countries.id, { onDelete: 'cascade' }),
    status: integer('status').notNull().default(1),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.postId, table.countryId] }),
  }),
);

export const postCountriesRelations = relations(postCountries, ({ one }) => ({
  post: one(posts, {
    fields: [postCountries.postId],
    references: [posts.id],
  }),
  country: one(countries, {
    fields: [postCountries.countryId],
    references: [countries.id],
  }),
}));

export type PostCountries = typeof postCountries.$inferSelect;
export type NewPostCountries = typeof postCountries.$inferInsert;
