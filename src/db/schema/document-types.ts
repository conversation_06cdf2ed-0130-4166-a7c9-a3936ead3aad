import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { documentRequirements } from './document-requirements';
import { workspaceDocuments } from './workspace-documents';

export const documentTypes = pgTable('document_types', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  description: text('description'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const documentTypesRelations = relations(documentTypes, ({ many }) => ({
  documentRequirements: many(documentRequirements),
  workspaceDocuments: many(workspaceDocuments),
}));
