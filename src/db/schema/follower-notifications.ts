// database/schema.ts
import { sql } from 'drizzle-orm';
import { pgTable, timestamp, boolean, uuid, integer, pgEnum } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { FollowerNotificationsStatus } from '@/constants/follower-notifications';
import { EntityType } from '@/constants/user-types';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const followerNotifications = pgTable('follower_notifications', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  entityId: uuid('entity_id').notNull(),
  entityType: entityTypeEnum('entity_type').notNull(),
  followedId: uuid('followed_id').notNull(),
  isRead: boolean('is_read').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  status: integer('status').notNull().default(FollowerNotificationsStatus.ACTIVE),
});

export type FollowerNotifications = typeof followerNotifications.$inferSelect;
export type NewFollowerNotifications = typeof followerNotifications.$inferInsert;
