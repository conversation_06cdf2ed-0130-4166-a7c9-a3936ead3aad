// database/schema.ts
import { relations } from 'drizzle-orm';
import { pgTable, timestamp, uuid, pgEnum, integer, primaryKey } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { ReportedPostStatus, ReportedReason } from '@/constants/posts';

import { users } from './users';
import { posts } from './posts';

const reportedReasonEnum = pgEnum('type', enumToPgEnum(ReportedReason));

export const reportedPosts = pgTable(
  'reported_posts',
  {
    reporterId: uuid('reporter_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    postId: uuid('post_id')
      .notNull()
      .references(() => posts.id, { onDelete: 'cascade' }),
    reportedReason: reportedReasonEnum('reported_reason').notNull(), // spam, harassment etc..
    status: integer('status').notNull().default(ReportedPostStatus.ACTIVE),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.reporterId, table.postId] }),
  }),
);

export const reportedPostsRelations = relations(reportedPosts, ({ one }) => ({
  reporter: one(users, {
    fields: [reportedPosts.reporterId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [reportedPosts.postId],
    references: [posts.id],
  }),
}));

export type ReportedPosts = typeof reportedPosts.$inferSelect;
export type NewReportedPosts = typeof reportedPosts.$inferInsert;
