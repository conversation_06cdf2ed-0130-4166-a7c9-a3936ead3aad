import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid, boolean, pgEnum } from 'drizzle-orm/pg-core';

import { enumToPgEnum } from '@/utils/database';

import { UserSegment } from '@/constants/user-segments';

import { documentTypes } from './document-types';

const userSegmentEnum = pgEnum('user_segment', enumToPgEnum(UserSegment));

export const documentRequirements = pgTable('document_requirements', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  userSegment: userSegmentEnum('user_segment').notNull(), // CARDIAC_SPECIALIST, ALLIED_CARDIAC, STUDENT, ORGANISATION
  documentTypeId: uuid('document_type_id')
    .references(() => documentTypes.id, {
      onDelete: 'cascade',
    })
    .notNull(),
  isRequired: boolean('is_required').notNull(),
  sortOrder: integer('sort_order').notNull(),
  maxCount: integer('max_count').notNull(),
  instructions: text('instructions'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const documentRequirementsRelations = relations(documentRequirements, ({ one }) => ({
  documentType: one(documentTypes, {
    fields: [documentRequirements.documentTypeId],
    references: [documentTypes.id],
  }),
}));
