import { pgTable, uuid, text, timestamp, boolean, integer, pgEnum } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';

import { OtpStatus, OtpType } from '@/constants/otp';

import { users } from './users';

const otpTypeEnum = pgEnum('otp_type', enumToPgEnum(OtpType));

export const otps = pgTable('otps', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  otp: text('otp').notNull(),
  type: otpTypeEnum('type').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  used: boolean('used').notNull().default(false),
  status: integer('status').notNull().default(OtpStatus.ACTIVE),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const otpsRelations = relations(otps, ({ one }) => ({
  user: one(users, {
    fields: [otps.userId],
    references: [users.id],
  }),
}));

export type Otp = typeof otps.$inferSelect;
export type NewOtp = typeof otps.$inferInsert;
