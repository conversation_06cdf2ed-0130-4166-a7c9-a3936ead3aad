import { relations, sql } from 'drizzle-orm';
import { boolean, integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { countries } from './countries';
import { workspaces } from './workspaces';
import { organisationCertifications } from './organisation-certifications';
import { segmentCategories } from './segment-categories';
import { employers } from './employers';

export const organisations = pgTable('organisations', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  segmentCategoryId: uuid('segment_category_id')
    .references(() => segmentCategories.id, { onDelete: 'cascade' })
    .notNull(),
  parentOrganisationId: uuid('parent_organisation_id'),
  location: text('location'),
  mapLink: text('map_link'),
  organisationSize: text('organisation_size'),
  allowSubsidiaries: boolean('allow_subsidiaries'),
  countryId: uuid('country_id').references(() => countries.id, { onDelete: 'cascade' }),
  registrationNumber: text('registration_number'),
  website: text('website'),
  pointOfContactPhone: text('point_of_contact_phone'),
  pointOfContactName: text('point_of_contact_name'),
  city: text('city'),
  zipCode: text('zip_code'),
  profileSummary: text('profile_summary'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const organisationsRelations = relations(organisations, ({ one, many }) => ({
  workspace: one(workspaces, {
    fields: [organisations.workspaceId],
    references: [workspaces.id],
  }),
  segmentCategory: one(segmentCategories, {
    fields: [organisations.segmentCategoryId],
    references: [segmentCategories.id],
  }),
  parentOrganisation: one(employers, {
    fields: [organisations.parentOrganisationId],
    references: [employers.id],
  }),
  country: one(countries, {
    fields: [organisations.countryId],
    references: [countries.id],
  }),
  employers: many(employers),
  organisationCertifications: many(organisationCertifications),
}));

export type Organisations = typeof organisations.$inferSelect;
