import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, timestamp, uuid, text } from 'drizzle-orm/pg-core';

import { workspaces } from './workspaces';
import { professionalAssociations } from './professional-associations';
import { professionalAwards } from './professional-awards';
import { professionalCertificates } from './professional-certificates';
import { employers } from './employers';

export const specialists = pgTable('specialists', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  workspaceId: uuid('workspace_id')
    .references(() => workspaces.id, { onDelete: 'cascade' })
    .notNull(),
  title: text('title'),
  jobTitle: text('job_title'),
  qualifications: text('qualifications'),
  employerId: uuid('employer_id').references(() => employers.id, { onDelete: 'cascade' }),
  profileSummary: text('profile_summary'),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const specialistsRelations = relations(specialists, ({ one, many }) => ({
  workspace: one(workspaces, {
    fields: [specialists.workspaceId],
    references: [workspaces.id],
  }),
  employer: one(employers, {
    fields: [specialists.employerId],
    references: [employers.id],
  }),
  professionalAssociations: many(professionalAssociations),
  professionalAwards: many(professionalAwards),
  professionalCertificates: many(professionalCertificates),
}));

export type Specialists = typeof specialists.$inferSelect;
