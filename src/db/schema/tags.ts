import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { postTags } from './post-tags';

export const tags = pgTable('tags', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  name: text('name').notNull(),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const tagsRelations = relations(tags, ({ many }) => ({
  postTags: many(postTags),
}));
