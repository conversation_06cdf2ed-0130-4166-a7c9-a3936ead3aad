import { pgTable, uuid, timestamp, integer, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { countries } from './countries';
import { workspaceSubscriptions } from './workspace-subscriptions';

export const workspaceSubscriptionCountries = pgTable(
  'workspace_subscription_countries',
  {
    workspaceSubscriptionId: uuid('workspace_subscription_id')
      .notNull()
      .references(() => workspaceSubscriptions.id, { onDelete: 'cascade' }),
    countryId: uuid('country_id')
      .notNull()
      .references(() => countries.id, { onDelete: 'cascade' }),
    status: integer('status').notNull().default(1),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.workspaceSubscriptionId, table.countryId] }),
  }),
);

export const workspaceSubscriptionCountriesRelations = relations(
  workspaceSubscriptionCountries,
  ({ one }) => ({
    subscriptionWorkspace: one(workspaceSubscriptions, {
      fields: [workspaceSubscriptionCountries.workspaceSubscriptionId],
      references: [workspaceSubscriptions.id],
    }),
    country: one(countries, {
      fields: [workspaceSubscriptionCountries.countryId],
      references: [countries.id],
    }),
  }),
);

export type WorkspaceSubscriptionCountries = typeof workspaceSubscriptionCountries.$inferSelect;
export type NewWorkspaceSubscriptionCountries = typeof workspaceSubscriptionCountries.$inferInsert;
