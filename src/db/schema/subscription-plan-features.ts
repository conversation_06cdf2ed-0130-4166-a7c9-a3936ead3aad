import { relations } from 'drizzle-orm';
import { boolean, integer, pgTable, primaryKey, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { subscriptionPlans } from './subscription-plans';
import { subscriptionFeatures } from './subscription-features';

export const subscriptionPlanFeatures = pgTable(
  'subscription_plan_features',
  {
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    subscriptionFeatureId: uuid('subscription_feature_id')
      .notNull()
      .references(() => subscriptionFeatures.id, { onDelete: 'cascade' }),
    booleanValue: boolean('boolean_value'),
    integerValue: integer('integer_value'),
    textValue: text('text_value'),
    status: integer('status').notNull().default(1),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({
      name: 'id',
      columns: [table.subscriptionFeatureId, table.subscriptionPlanId],
    }),
  }),
);

export const subscriptionPlanFeaturesRelations = relations(subscriptionPlanFeatures, ({ one }) => ({
  subscriptionPlan: one(subscriptionPlans, {
    fields: [subscriptionPlanFeatures.subscriptionPlanId],
    references: [subscriptionPlans.id],
  }),
  subscriptionFeature: one(subscriptionFeatures, {
    fields: [subscriptionPlanFeatures.subscriptionFeatureId],
    references: [subscriptionFeatures.id],
  }),
}));
