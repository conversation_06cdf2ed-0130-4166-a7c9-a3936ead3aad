import { sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { Controller, Get, Inject } from '@nestjs/common';

import * as schema from '@/db/schema';

import { Public } from '@/decorators/public.decorator';

@Controller('health')
export class HealthController {
  constructor(@Inject('DB_DEV') private drizzleDev: PostgresJsDatabase<typeof schema>) {}

  @Get()
  @Public()
  async check() {
    try {
      await this.drizzleDev.execute(sql`SELECT 1`);

      return {
        status: 'ok',
        message: 'Service is healthy',
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(error);

        return {
          status: 'error',
          message: 'Service is unhealthy',
          database: 'disconnected',
          error: error.message,
        };
      }

      throw new Error('Unknown error');
    }
  }
}
