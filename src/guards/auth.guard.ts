import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { Reflector } from '@nestjs/core';

// import { Roles } from '@/constants/user-segments';
// import { Permissions } from '@/constants/permissions';
import { <PERSON><PERSON><PERSON> } from '@/constants/auth';

import { sessionExpired, unauthorized } from '@/exceptions/system';

import { authUserData } from '@/interfaces/auth';

import { CustomConfigService } from '@/config/configuration.service';

import { verifySessionCookie, verifyToken } from '@/helpers/auth.helpers';

import { IS_PUBLIC_METADATA_KEY } from '@/decorators/public.decorator';
import { IS_SESSION_ONLY_METADATA_KEY } from '@/decorators/session-only.decorator';
import { IS_TOKEN_ONLY_METADATA_KEY } from '@/decorators/token-only.decorator';

type UserContext = Request & {
  user: authUserData;
};

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    protected readonly customConfigService: CustomConfigService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: UserContext = context.switchToHttp().getRequest();
    const isHealthEndpoint: boolean = request.path.replaceAll('/', '').endsWith('health');
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    const isSessionOnly = this.reflector.getAllAndOverride<boolean>(IS_SESSION_ONLY_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    const isTokenOnly = this.reflector.getAllAndOverride<boolean>(IS_TOKEN_ONLY_METADATA_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Determine the client type (web or mobile)
    const clientType = this.detectClientType(request);

    const token = this.extractTokenFromHeader(request);

    // Extract user details even for public routes
    if (token) {
      try {
        request.user = await verifyToken<authUserData>(token);
      } catch (e) {
        console.error('Invalid token for public route:', e.message);
      }
    }

    // Allow health and public routes without additional checks
    if (isPublic || isHealthEndpoint) {
      return true;
    }

    // For mobile clients, prioritize token-based auth
    if (clientType === 'mobile') {
      if (!token) {
        throw unauthorized();
      }

      try {
        request.user = await verifyToken<authUserData>(token);
        return true;
      } catch (e) {
        console.error('Invalid token for mobile client:', e.message);
        throw unauthorized();
      }
    }

    // Web client authentication flow
    // Enforce token or session requirements for protected routes
    if (!isSessionOnly && !token) {
      throw unauthorized();
    }

    const sessionCookie = request.cookies?.[CookieKey];

    if (!isTokenOnly && !sessionCookie) {
      throw sessionExpired();
    }

    request.user = isSessionOnly
      ? await verifySessionCookie<authUserData>(sessionCookie)
      : await verifyToken<authUserData>(token!);

    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  private detectClientType(request: Request): 'web' | 'mobile' {
    // Check for mobile app identifier header
    const clientTypeHeader = request.headers['x-client-type'];
    if (clientTypeHeader === 'mobile-app') {
      return 'mobile';
    }

    // Check user agent as a fallback
    const userAgent = request.headers['user-agent'] || '';
    if (userAgent.includes('YourMobileAppIdentifier')) {
      return 'mobile';
    }

    // Default to web client
    return 'web';
  }
}
