import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { USER_PERMISSIONS_METADATA_KEY } from '@/decorators/permissions.decorator';

import { Permissions } from '@/constants/permissions';

import { UserData } from '@/interfaces/auth';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(cxt: ExecutionContext): Promise<boolean> {
    const requiredUserPermissions = this.reflector.getAllAndOverride<Permissions[] | undefined>(
      USER_PERMISSIONS_METADATA_KEY,
      [cxt.getHandler(), cxt.getClass()],
    );

    if (!requiredUserPermissions || requiredUserPermissions.length === 0) {
      return true;
    }

    const req = cxt.switchToHttp().getRequest();
    const user: Partial<UserData> = req.user;

    return requiredUserPermissions.every((permission) => user.permissions?.includes(permission));
  }
}
