import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ACCOUNT_SETUP_STAGE_METADATA_KEY } from '@/decorators/account-setup-stage.decorator';

import { AccountSetupStage } from '@/constants/users';

import { UserData } from '@/interfaces/auth';

@Injectable()
export class AccountSetupStagesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(cxt: ExecutionContext): Promise<boolean> {
    const requiredAccountSetupStages = this.reflector.getAllAndOverride<
      AccountSetupStage[] | undefined
    >(ACCOUNT_SETUP_STAGE_METADATA_KEY, [cxt.getHandler(), cxt.getClass()]);

    if (!requiredAccountSetupStages || requiredAccountSetupStages.length === 0) {
      return true;
    }

    const req = cxt.switchToHttp().getRequest();
    const user: UserData = req.user;

    return requiredAccountSetupStages.includes(user.currentStage);
  }
}
