import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ACCOUNT_TYPES_METADATA_KEY } from '@/decorators/account-types.decorator';

import { AccountType } from '@/constants/users';

import { UserData } from '@/interfaces/auth';

@Injectable()
export class AccountTypesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(cxt: ExecutionContext): Promise<boolean> {
    const requiredAccountTypes = this.reflector.getAllAndOverride<AccountType[] | undefined>(
      ACCOUNT_TYPES_METADATA_KEY,
      [cxt.getHandler(), cxt.getClass()],
    );

    if (!requiredAccountTypes || requiredAccountTypes.length === 0) {
      return true;
    }

    const req = cxt.switchToHttp().getRequest();
    const user: UserData = req.user;

    return requiredAccountTypes.includes(user.accountType);
  }
}
