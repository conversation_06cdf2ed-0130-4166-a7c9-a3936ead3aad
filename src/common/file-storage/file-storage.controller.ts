import { Controller, Get, Param } from '@nestjs/common';

import { FileStorageService } from './file-storage.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

@ApiBearerAuth()
@ApiTags('file storage')
@Controller('file-storage')
export class FileStorageController {
  constructor(private readonly fileStorageService: FileStorageService) {}

  @Get('/generate-signed-url/:blobName/:expInHours')
  generateSignedAzureUrl(
    @Param('blobName') blobName: string,
    @Param('expInHours') expInHours: string,
  ) {
    return this.fileStorageService.generateSignedUrl(blobName, +expInHours);
  }
}
