import { Injectable } from '@nestjs/common';

import { FileStorageService } from '../file-storage.service';

@Injectable()
export class AzureStorageService implements FileStorageService {
  async generateSignedUrl(blobName: string, expiresInHours: number): Promise<string> {
    // eslint-disable-next-line no-console
    console.log(blobName, expiresInHours);
    // const containerClient = this.blobServiceClient.getContainerClient(this.containerName);
    // const blobClient = containerClient.getBlobClient(blobName);
    // const expiryDate = new Date();
    // expiryDate.setHours(expiryDate.getHours() + expiresInHours);
    // const permissions = new BlobSASPermissions();
    // permissions.write = true;
    // const sasToken = generateBlobSASQueryParameters(
    //   {
    //     containerName: this.containerName,
    //     blobName,
    //     // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //     // @ts-expect-error
    //     permissions: permissions.toString(),
    //     startsOn: new Date(),
    //     expiresOn: expiryDate,
    //     protocol: SASProtocol.Https,
    //   },
    //   this.blobServiceClient.credential as StorageSharedKeyCredential,
    // ).toString();
    // const sasUrl = `${blobClient.url}?${sasToken}`;
    // return sasUrl;
    // FIXME: replace with S3 logic when the feature must be updated
    throw new Error('This feature is not available');
  }
}
