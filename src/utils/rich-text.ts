import * as cheerio from 'cheerio';

export function extractImagePathsFromRichText(richText: string): string[] {
  try {
    const $ = cheerio.load(richText);
    const urls: string[] = [];

    $('img').each((_, img) => {
      const src = $(img).attr('src');
      if (src) {
        try {
          // Handle absolute URLs (extract pathname)
          if (src.startsWith('http://') || src.startsWith('https://')) {
            const urlObj = new URL(src);
            urls.push(urlObj.pathname);
          }
          // Handle protocol-relative URLs
          else if (src.startsWith('//')) {
            const urlObj = new URL(`https:${src}`);
            urls.push(urlObj.pathname);
          }
          // Handle relative URLs (assuming they're already paths)
          else {
            urls.push(src);
          }
        } catch {
          urls.push(src);
        }
      }
    });

    return urls;
  } catch {
    return [];
  }
}
