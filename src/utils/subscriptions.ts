export function calculateSubscriptionEndDate(isYearly: boolean) {
  // Start with current date
  const currentDate = new Date();

  // Clone the date to avoid modifying the original
  const endDate = new Date(currentDate);

  if (isYearly) {
    // For yearly: add 1 year to current date
    endDate.setFullYear(endDate.getFullYear() + 1);
  } else {
    // For monthly: add 1 month to current date
    endDate.setMonth(endDate.getMonth() + 1);
  }

  // Subtract 1 day
  endDate.setDate(endDate.getDate() - 1);

  return endDate;
}
