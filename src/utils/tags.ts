/**
 * Formats a tag string by:
 * 1. Removing leading/trailing whitespace
 * 2. Capitalizing the first letter of each word
 * 3. Removing spaces between words
 *
 * This maintains existing capitalization within words (except first letter)
 * to preserve intentional camelCase or PascalCase formatting.
 *
 * Examples:
 * - "cardiac surgeon" → "CardiacSurgeon"
 * - "  Cardiac  SURGEON  " → "CardiacSURGEON"
 * - "cardiacSurgeon" → "CardiacSurgeon"
 */
export const formatTag = (name: string): string => {
  // Handle edge case of empty or null input
  if (!name) return '';

  // Trim any leading/trailing whitespace before processing
  const trimmedName = name.trim();

  // Split by one or more whitespace characters
  // Capitalize first letter of each word while preserving rest of word's casing
  // Then join without spaces
  return trimmedName
    .split(/\s+/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
};
