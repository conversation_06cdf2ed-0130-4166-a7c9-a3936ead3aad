import { HttpStatus, HttpException } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

import { IExceptionPayload } from '@/interfaces/system';

import { I18nPath } from '@/generated/i18n.generated';

export default class CustomHttpException extends HttpException {
  constructor(
    i18nKey: I18nPath,
    httpStatus: HttpStatus,
    payload: { args?: object; metadata?: object } = {},
  ) {
    const i18n = I18nContext.current(); // Get the current i18n context

    const message = i18n ? String(i18n.t(i18nKey, { args: payload.args })) : i18nKey; // Translate the message using the key

    super(
      createExceptionPayload({
        message,
        metadata: payload.metadata,
        data: null,
        stack: null,
      }),
      httpStatus,
    );
  }
}

const createExceptionPayload = (payload: IExceptionPayload) =>
  HttpException.createBody(payload as any);
