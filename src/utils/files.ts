import { MEDIA_TYPES_INFO, MediaCategoryType, MEDIA_CATEGORY } from '@/constants/posts';

// Function to get the media type
export function getMediaCategoryByMimeType(mimeType: string): MediaCategoryType | null {
  for (const [mediaType, info] of Object.entries(MEDIA_TYPES_INFO)) {
    if (info.accept.includes(mimeType)) {
      return mediaType as MediaCategoryType;
    }
  }
  return null;
}

export function guessMediaTypeFromPath(path: string): string {
  const extension = path.split('.').pop()?.toLowerCase();

  if (!extension) {
    return MEDIA_TYPES_INFO[MEDIA_CATEGORY.IMAGE].accept[0]; // Default to first image type
  }

  // Map file extensions to their corresponding MIME types using MEDIA_TYPES_INFO
  for (const [, info] of Object.entries(MEDIA_TYPES_INFO)) {
    // Get all extensions from this category
    const extensionsMap = info.extensions;

    // Find the MIME type that has this extension
    for (const [mimeType, ext] of Object.entries(extensionsMap)) {
      if (ext === extension) {
        return mimeType;
      }
    }
  }

  // If no match found, return default image type
  return MEDIA_TYPES_INFO[MEDIA_CATEGORY.IMAGE].accept[0];
}
