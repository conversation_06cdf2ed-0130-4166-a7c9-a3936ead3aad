import sanitizeHtml from 'sanitize-html';

/**
 * Default configuration for HTML sanitization
 * Allows common HTML elements and attributes while removing potentially dangerous content
 */
const DEFAULT_SANITIZE_OPTIONS: sanitizeHtml.IOptions = {
  allowedTags: [
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'blockquote',
    'p',
    'a',
    'ul',
    'ol',
    'nl',
    'li',
    'b',
    'i',
    'strong',
    'em',
    'strike',
    'code',
    'hr',
    'br',
    'div',
    'table',
    'thead',
    'caption',
    'tbody',
    'tr',
    'th',
    'td',
    'pre',
    'img',
    'span',
    'sup',
    'sub',
  ],
  allowedAttributes: {
    a: ['href', 'name', 'target', 'rel'],
    img: ['src', 'alt', 'title', 'width', 'height'],
    '*': ['class', 'id', 'style'],
  },
  // Restrict CSS to prevent JavaScript injection via CSS
  allowedStyles: {
    '*': {
      color: [/^#(0x)?[0-9a-f]+$/i, /^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/],
      'text-align': [/^left$/, /^right$/, /^center$/],
      'font-size': [/^\d+(?:px|em|%)$/],
      'font-weight': [/^bold$/, /^\d+$/],
      'text-decoration': [/^underline$/, /^line-through$/],
      'font-style': [/^italic$/],
    },
  },
  // Set URL schemes that are allowed
  allowedSchemes: ['http', 'https', 'mailto', 'tel'],
  allowedSchemesByTag: {
    img: ['http', 'https', 'data'],
  },
  // Set to true to allow data: URLs for images
  allowedSchemesAppliedToAttributes: ['src', 'href'],
  // Strip out any JavaScript handlers
  disallowedTagsMode: 'discard',
  // Don't allow script or style tags
  // Don't allow JavaScript in URLs
  allowProtocolRelative: false,
};

export function sanitizeContent(
  html: string,
  options: sanitizeHtml.IOptions = DEFAULT_SANITIZE_OPTIONS,
): string {
  if (!html) return '';
  return sanitizeHtml(html, options);
}

export function containsMaliciousContent(html: string): boolean {
  if (!html) return false;
  const sanitized = sanitizeHtml(html, DEFAULT_SANITIZE_OPTIONS);
  return sanitized !== html;
}
