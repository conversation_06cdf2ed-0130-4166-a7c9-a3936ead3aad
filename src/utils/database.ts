import { sql } from 'drizzle-orm';
import { PgDialect } from 'drizzle-orm/pg-core';

import { PostLikesStatus } from '@/constants/post-likes';

import { postLikes } from '@/db/schema';

export function enumToPgEnum<T extends Record<string, any>>(
  myEnum: T,
): [T[keyof T], ...T[keyof T][]] {
  return Object.values(myEnum).map((value: any) => `${value}`) as any;
}

// Create a helper function (you can put this in a utils file)
const pgDialect = new PgDialect(); // Import from drizzle-orm/pg-core

export const countRelation = (name: string, fieldId: any, refId: any, status: any) => {
  const sqlChunks = sql`(SELECT COUNT(*)::int FROM ${refId.table} WHERE ${refId} = ${fieldId} AND ${refId.table}.status = ${status})`;
  const rawSQL = sql.raw(pgDialect.sqlToQuery(sqlChunks).sql);

  return {
    [name]: rawSQL.as(name),
  };
};

export const isLikedByEntity = (name: string, fieldId: any, entityId?: string) => {
  if (!entityId) {
    return {
      [name]: sql<boolean>`false`.as(name),
    };
  }

  const sqlChunks = sql`(
    SELECT EXISTS(
      SELECT 1
      FROM ${postLikes} 
      WHERE ${postLikes.postId} = ${fieldId} 
      AND ${postLikes.entityId} = ${sql.raw(`'${entityId}'`)}
      AND ${postLikes.status} = ${PostLikesStatus.ACTIVE}
    )
  )`;
  const rawSQL = sql.raw(pgDialect.sqlToQuery(sqlChunks).sql);

  return {
    [name]: rawSQL.as(name),
  };
};
