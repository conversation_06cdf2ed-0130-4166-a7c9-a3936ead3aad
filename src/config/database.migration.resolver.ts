// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { FsMigrations } from 'knex/lib/migrations/migrate/sources/fs-migrations.js';
import path from 'path';

export default class PolyglotMigrationSource extends FsMigrations {
  fsSource: FsMigrations;

  constructor({
    migrationDirectories,
    sortDirsSeparately = false,
    loadExtensions,
  }: {
    migrationDirectories: string | string[];
    sortDirsSeparately?: boolean;
    loadExtensions?: string[] | undefined;
  }) {
    super(migrationDirectories, sortDirsSeparately, loadExtensions);
    this.fsSource = new FsMigrations(migrationDirectories, sortDirsSeparately, loadExtensions);
  }

  getMigrations(loadExtensions: string[]) {
    return this.fsSource.getMigrations(loadExtensions);
  }

  getMigrationName(migration: any) {
    // use filename without extension as migration name
    // so that different file types (eg. ts and js) could be used
    return path.parse(migration.file as string).name;
  }

  getMigration(migration: any) {
    return this.fsSource.getMigration(migration);
  }
}
