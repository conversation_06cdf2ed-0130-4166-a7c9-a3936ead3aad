import { S3Client } from '@aws-sdk/client-s3';
import { SNSClient } from '@aws-sdk/client-sns';

import config from '@/config/configuration';

const compiledConfig = config();

const accessKeyId: string = compiledConfig.aws.awsAccessKeyId;
const secretAccessKey: string = compiledConfig.aws.awsSecretsAccessKey;
export const region: string = compiledConfig.aws.awsDefaultRegion;

// maintaining a centralised location for all the config and sdk inits
const awsConfig = {
  credentials: {
    accessKeyId,
    secretAccessKey,
  },
  region,
};

export const s3Client = new S3Client(awsConfig);

export const snsClient = new SNSClient(awsConfig);
