import path from 'path';
import fs from 'fs';

import { Environment } from '../constants/system';

import { ISystemConfig } from '../interfaces/system';

export default (): ISystemConfig => {
  const isProdCompiled: boolean = process.env.NODE_ENV === 'production';

  const filepath = path.resolve(
    path.join(__dirname),
    isProdCompiled ? '../VERSION.txt' : '../../VERSION.txt',
  );
  const appVersion = fs.readFileSync(filepath, 'utf8');

  const firebase: object = JSON.parse(
    Buffer.from(process.env.FIREBASE_SERVICE_KEY!, 'base64').toString('utf-8'),
  );

  const environment: Environment = process.env.ENVIRONMENT as Environment;

  return {
    database: {
      host: process.env.DB_HOST!,
      user: process.env.DB_USER!,
      password: process.env.DB_PASSWORD!,
      port: process.env.DB_PORT as unknown as number,
      name: process.env.DB_NAME!,
      ssl: [Environment.Develop, Environment.Staging, Environment.Production].includes(environment),
    },
    app: {
      port: parseInt(process.env.PORT ?? '9200', 10),
      hostDomain: process.env.HOST_DOMAIN!,
      feDashboardUrl: process.env.FE_DASHBOARD_URL!,
      fePublicUrl: process.env.FE_PUBLIC_URL!,
    },
    aws: {
      assetsBucketName: process.env.ASSETS_BUCKET_NAME!,
      assetsCDNBaseUrl: process.env.ASSETS_CDN_BASE_URL!,
      awsAccessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      awsDefaultRegion: process.env.AWS_DEFAULT_REGION!,
      awsSecretsAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    },
    firebaseServiceAccount: firebase,
    smtp: {
      host: process.env.SMTP_HOST!,
      password: process.env.SMTP_PASSWORD!,
      user: process.env.SMTP_USER!,
    },
    isProdCompiled,
    environment: {
      isLocal: environment === Environment.Local,
      isDevelop: environment === Environment.Develop,
      isStaging: environment === Environment.Staging,
      isProduction: environment === Environment.Production,
    },
    environmentName: environment,
    frontendHost: process.env.FE_HOST!,
    superAdminPassword: process.env.SUPER_ADMIN_PASSWORD!,
    appVersion,
  };
};
