import { HttpStatus } from '@nestjs/common';

export interface IAwsS3Config {
  assetsBucketName: string;
  assetsCDNBaseUrl: string;
  awsAccessKeyId: string;
  awsDefaultRegion: string;
  awsSecretsAccessKey: string;
}

export interface IDatabaseConfig {
  ssl: boolean;
  host: string;
  user: string;
  password: string;
  port: number;
  name: string;
}

export interface ISMTPConfig {
  host: string;
  password: string;
  user: string;
}

export interface IAppConfig {
  port: number;
  hostDomain: string;
  feDashboardUrl: string;
  fePublicUrl: string;
}

export interface IEnviroment {
  isLocal: boolean;
  isDevelop: boolean;
  isStaging: boolean;
  isProduction: boolean;
}

export interface ISystemConfig {
  database: IDatabaseConfig;
  app: IAppConfig;
  aws: IAwsS3Config;
  smtp: ISMTPConfig;
  firebaseServiceAccount: object;
  isProdCompiled: boolean;
  environment: IEnviroment;
  frontendHost: string;
  superAdminPassword: string;
  appVersion: string;
  environmentName: string;
}

export interface IExceptionPayload {
  message: string;
  stack?: any;
  data?: any;
  metadata?: any;
}

export interface IException {
  response: IExceptionPayload;
  status: HttpStatus;

  initMessage(): void;
  initName(): void;
  getResponse(): string | object;
  getStatus(): number;
  createBody(objectOrError: object | string, description?: string, statusCode?: number): object;
}
