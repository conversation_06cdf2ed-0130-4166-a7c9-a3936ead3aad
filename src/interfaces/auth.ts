import { DecodedIdToken } from 'firebase-admin/auth';

import { Role } from '@/constants/roles';
import { AuthConstants } from '@/constants/auth';
import { Permissions } from '@/constants/permissions';
import { AccountSetupStage, AccountType } from '@/constants/users';

export interface UserData {
  [AuthConstants.FIREBASE_UID]?: string;
  [AuthConstants.FIREBASE_CLAIM_USER_ID]: string;
  [AuthConstants.FIREBASE_CLAIM_EMAIL]: string;
  [AuthConstants.FIREBASE_CLAIM_ROLE]: Role | null;
  [AuthConstants.FIREBASE_CLAIM_PERMISSIONS]: Permissions[];
  [AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID]: string | null | undefined;
  [AuthConstants.FIREBASE_CLAIM_ACCOUNT_TYPE]: AccountType;
  [AuthConstants.FIREBASE_CLAIM_CURRENT_STAGE]: AccountSetupStage;
}

export type authUserData = Partial<DecodedIdToken> & UserData;
