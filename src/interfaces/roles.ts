interface RolePermission {
  permissionsId: string;
  permission: {
    id: string;
    name: string;
    key: string;
    module: {
      id: string;
      name: string;
    };
  };
}

export interface FindRoleByIdOrKey {
  id: string;
  name: string;
  key: string;
  status: number;
  createdAt: Date;
  updatedAt: Date;
  rolesPermissions: RolePermission[];
}

export type FindAllRolesResponse = FindRoleByIdOrKey[];
