// src/exceptions/common.ts
import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const itemNotFound = (item = 'item') =>
  new CustomHttpException('exception.not_found', HttpStatus.NOT_FOUND, { args: { item } });

export const itemAlreadyExists = (item = 'item') =>
  new CustomHttpException('exception.already_exists', HttpStatus.CONFLICT, { args: { item } });

export const itemSizeTooLarge = (item = 'item') =>
  new CustomHttpException('exception.item_size_too_large', HttpStatus.PAYLOAD_TOO_LARGE, {
    args: { item },
  });

export const itemLimitReached = (item = 'item') =>
  new CustomHttpException('exception.limit_reached', HttpStatus.CONFLICT, { args: { item } });

export const selfActionNotAllowed = () =>
  new CustomHttpException('exception.self_action_not_allowed', HttpStatus.BAD_REQUEST);
