import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const workspaceNotVerified = () =>
  new CustomHttpException('workspaces.workspace_not_verified', HttpStatus.FORBIDDEN);

export const lowLevelSubscriptionPlanCantChooseBothAsPrimarySpecialist = () =>
  new CustomHttpException(
    'workspaces.basic_and_primary_subscription_tier_user_cant_choose_both_for_mainly_works_with',
    HttpStatus.FORBIDDEN,
  );
