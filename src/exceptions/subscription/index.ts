import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const accountTypeAndSubscriptionPlanNotMatching = () =>
  new CustomHttpException(
    'subscription.the_account_type_and_subscription_plan_user_chosen_is_not_matching',
    HttpStatus.BAD_REQUEST,
  );

export const professionalCategoryIsRequiredIfAccountTypeIsProfessional = () =>
  new CustomHttpException(
    'subscription.professional_category_is_required_if_account_type_is_professional',
    HttpStatus.BAD_REQUEST,
  );

export const organizationApplicationFieldsRequired = () =>
  new CustomHttpException(
    'subscription.organization_application_fields_required',
    HttpStatus.BAD_REQUEST,
  );
