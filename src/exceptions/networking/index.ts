import { HttpStatus } from '@nestjs/common';

import CustomHttpException from '@/utils/custom-http-exception';

export const connectionRequestAlreadyExists = () =>
  new CustomHttpException('networking.connection_request_already_exists', HttpStatus.CONFLICT);

export const connectionRequestAlreadyReceived = () =>
  new CustomHttpException('networking.connection_request_already_received', HttpStatus.CONFLICT);

export const connectionRequestNotFound = () =>
  new CustomHttpException('networking.connection_request_not_found', HttpStatus.NOT_FOUND);
