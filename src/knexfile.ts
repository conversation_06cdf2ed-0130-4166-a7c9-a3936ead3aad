import { Knex } from 'knex';
import * as path from 'path';

import CustomMigrationSource from './config/database.migration.resolver';
import configuration from './config/configuration';

import rdsCertificate from './certificates/aws-rds-certificate/aws.rds';

const { database } = configuration();

const config: Knex.Config = {
  client: 'pg',
  connection: {
    user: database.user,
    password: database.password,
    host: database.host,
    port: database.port,
    database: database.name,
    ssl: database.ssl
      ? {
          rejectUnauthorized: true,
          ca: rdsCertificate,
        }
      : undefined,
  },
  pool: {
    min: 2,
    max: 10,
  },
  migrations: {
    migrationSource: new CustomMigrationSource({
      migrationDirectories: path.join(__dirname, 'migrations'),
      sortDirsSeparately: false,
    }),
  },
};

export default config;
